import React from 'react'
import <PERSON><PERSON>ew<PERSON>annerHeaderComponent from '../../component/MPNewBannerHeaderComponent'
import MPNewBannerHeaderCard from '../../ui/MPNewBannerHeaderCard'
import { LocationType } from '../../ui/MPNewHeaderCard'

interface IF {
  data: any
  onSearchDebounce: (event: any) => Promise<void>
  handleBookingSearch: ({ type, item }: any) => Promise<void>
  handleDetectLocation: () => void
  searchData: any
  locationData: any
  locationType?: LocationType
  searching: boolean
}

export const MPNewBannerHeader = (props: IF) => {
  return (
    <MPNewBannerHeaderComponent
      {...props}
      renderItem={() => {
        return <MPNewBannerHeaderCard {...props} />
      }}
    />
  )
}

export default MPNewBannerHeader
