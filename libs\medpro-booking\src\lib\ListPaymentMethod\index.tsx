import {
  <PERSON><PERSON>ni<PERSON>,
  MPContainer,
  MPLeftButton,
  MPListPaymentMethod,
  MPPaymentFeeInfo,
  MPPaymentPatientInfo,
  MPRightButton,
  OkModal,
  PaymentMethod,
  PaymentType,
  SelectionData,
  Valid,
  getFormatMoney
} from '@medpro-libs/libs'
import {
  Button,
  Card,
  Checkbox,
  Col,
  Form,
  Input,
  notification,
  Row
} from 'antd'
import cx from 'classnames'
import { pick } from 'lodash'
import router from 'next/router'
import { useState } from 'react'
import { FaHospitalAlt } from 'react-icons/fa'
import { RiArrowGoBackFill } from 'react-icons/ri'
import styles from './styles.module.less'
import { ExtraConfig } from '@medpro-libs/types'
import { HiCheckBadge } from 'react-icons/hi2'
const valid = new Valid()
//TODO Tạm thời để typescript "any" sau khi optimize xong nhớ xóa dòng này

declare const window: Window &
  typeof globalThis & {
    dataLayer: any
  }

export interface ListPaymentMethodProps {
  isRepayment?: boolean
  allowBack?: boolean
  patient: any
  paymentFeeInfo: any[]
  partnerInfo?: any
  canReserveBooking: boolean
  partnerId?: any
  userId?: any
  paymentMethods: PaymentMethod[]
  selectedPaymentType?: PaymentType
  selectedPaymentMethod?: PaymentMethod
  onReserve: (params?) => Promise<void>
  onConfirmSelectPaymentMethod: ({
    item
  }: SelectionData<{
    paymentType?: PaymentType
    paymentMethod?: PaymentMethod
  }>) => void
  onUpdateUserInfo?: (payload) => Promise<void>
  makePayment?: string
  extraConfig: ExtraConfig
  setReferralCode?: (code: string) => void
  hideRefferralCode?: boolean
}

/**
 * Dành cho các trang: Phương thức thanh toán, thanh toán hộ,...
 * Các trang chỉ khác nhau tham số để lấy danh sách PTTT
 * @param props
 * @constructor
 */
export const ListPaymentMethod = (props: ListPaymentMethodProps) => {
  const {
    isRepayment = false,
    allowBack = true,
    paymentMethods,
    selectedPaymentMethod,
    selectedPaymentType,
    canReserveBooking,
    partnerInfo,
    onReserve,
    paymentFeeInfo,
    patient,
    onConfirmSelectPaymentMethod,
    partnerId,
    userId,
    onUpdateUserInfo,
    makePayment,
    extraConfig,
    setReferralCode,
    hideRefferralCode
  } = props
  const [form] = Form.useForm()
  const [modalVisible, setModalVisible] = useState(false)
  const [isReserving, setIsReserving] = useState(false)
  const [updateVisa, setUpdateVisa] = useState({
    status: false,
    initValue: {}
  })
  const [isCheckedVisa, setIsCheckedVisa] = useState(false)
  const [visaEntered, setVisaEntered] = useState({
    status: false,
    info: {
      fullName: '',
      userName: '',
      email: ''
    }
  })

  const onTogglePayment = (e: any) => {
    setModalVisible(true)
  }

  const handleRequireInput = (label: string, require: boolean) => {
    if (require) {
      return (
        <>
          {label}
          <sup className={styles['requireInput']}>*</sup>
        </>
      )
    }
    return <span>{label}</span>
  }

  const handleCheckboxChange = (e) => {
    setIsCheckedVisa(e.target.checked)
  }

  const toggleVisa = () => {
    if (!visaEntered.status) {
      setUpdateVisa((preState) => ({
        ...preState,
        status: !updateVisa.status
      }))
    }
  }

  const handleFinish = async () => {
    const { fullName, userName, email } = form.getFieldsValue()
    if (makePayment === 'SharePayment') {
      setVisaEntered({
        status: true,
        info: {
          fullName,
          userName,
          email
        }
      })
      notification.success({ message: 'Cập nhật thông tin thành công !!!' })
      toggleVisa()
    } else {
      await onUpdateUserInfo?.({
        fullname: fullName,
        username: userName,
        email: email,
        isSaveEmail: true
      })
        .then(() => {
          setVisaEntered((preState) => ({
            ...preState,
            status: true
          }))
          notification.success({ message: 'Cập nhật thông tin thành công !!!' })
          toggleVisa()
        })
        .catch(() => {
          notification.error({ message: 'Cập nhật thông tin thất bại !!!' })
        })
    }
  }

  const handleCancel = () => {
    onConfirmSelectPaymentMethod({
      item: {
        paymentType: undefined,
        paymentMethod: selectedPaymentMethod
      }
    })
    setIsCheckedVisa(false)
    toggleVisa()
  }

  return (
    <MPContainer medproSeo className={styles['paymentMethodPage']}>
      <Row gutter={[24, 24]} className={styles['rowPayments']}>
        <Col xs={24} sm={24} lg={6} xl={6}>
          <MPAnimation duration={0.5} fillMode='forwards' transLate='Top'>
            <Card title={'Thông tin bệnh nhân'}>
              <MPAnimation
                duration={0.5}
                delay={0.3}
                fillMode='forwards'
                transLate='Top'
              >
                <MPPaymentPatientInfo
                  patient={pick(patient, [
                    'fullname',
                    'name',
                    'surname',
                    'mobile',
                    'fullAddress',
                    'id'
                  ])}
                />
              </MPAnimation>
            </Card>
          </MPAnimation>
          <MPAnimation duration={0.5} fillMode='forwards' transLate='Top'>
            <Card
              title={'Thông tin cơ sở y tế'}
              className={styles['cardHopital']}
            >
              <MPAnimation
                duration={0.5}
                delay={0.3}
                fillMode='forwards'
                transLate='Top'
              >
                <div className={styles['infoHopital']}>
                  <FaHospitalAlt
                    className={styles['icon']}
                    color='#b1b1b1'
                    size={16}
                  />
                  {partnerInfo?.address && (
                    <div className={styles['content']}>
                      <p>
                        {partnerInfo?.name}
                        {partnerInfo?.listingPackagePaid && (
                          <HiCheckBadge color='#0097FF' size={16} />
                        )}
                      </p>
                      <p className={styles['address']}>
                        {partnerInfo?.address || ''}
                      </p>
                    </div>
                  )}
                </div>
              </MPAnimation>
            </Card>
          </MPAnimation>
        </Col>
        <Col xs={24} sm={24} lg={18} xl={18}>
          <MPAnimation
            duration={0.5}
            delay={0.15}
            fillMode='forwards'
            transLate='Top'
          >
            <Card
              className={styles['selectPaymentMT']}
              title={'Chọn phương thức thanh toán'}
            >
              <Row gutter={30}>
                <Col
                  xs={24}
                  sm={24}
                  lg={14}
                  xl={14}
                  className={styles['colPayments']}
                >
                  <MPAnimation
                    duration={0.5}
                    delay={0.4}
                    fillMode='forwards'
                    transLate='Top'
                  >
                    <MPListPaymentMethod
                      data={paymentMethods}
                      onWarning={() => console.info('warning')}
                      onFinish={onConfirmSelectPaymentMethod}
                      selectedPaymentMethod={selectedPaymentMethod}
                      selectedPaymentType={selectedPaymentType}
                      toggleVisa={toggleVisa}
                      visaEntered={visaEntered}
                      partnerId={partnerId}
                    />
                  </MPAnimation>
                </Col>
                <Col
                  xs={24}
                  sm={24}
                  lg={10}
                  xl={10}
                  className={styles['colBill']}
                >
                  <MPAnimation
                    duration={0.5}
                    delay={0.4}
                    fillMode='forwards'
                    transLate='Top'
                  >
                    <MPPaymentFeeInfo
                      paymentFeeInfo={paymentFeeInfo}
                      selectedPaymentType={selectedPaymentType}
                      selectedPaymentMethod={selectedPaymentMethod}
                      agreement={selectedPaymentMethod?.agreement || ''}
                      partnerId={partnerId}
                      extraConfig={extraConfig}
                      setReferralCode={setReferralCode}
                      isRepayment={isRepayment}
                      hideRefferralCode={hideRefferralCode}
                    />
                  </MPAnimation>
                </Col>
              </Row>
              <div className={cx(styles['btnFooter'])} id='payment'>
                <MPRightButton
                  className={cx(
                    styles['btnSubmit'],
                    !canReserveBooking ? styles['disable'] : 'default'
                  )}
                  onClick={onTogglePayment}
                  disabled={!canReserveBooking}
                >
                  {isRepayment ? 'Thanh toán lại' : 'Thanh toán'}
                </MPRightButton>
              </div>
            </Card>

            {allowBack && (
              <MPLeftButton
                className={styles['btnBack']}
                onClick={() => router.back()}
              >
                Quay lại{' '}
                <div className={styles['icon']}>
                  <RiArrowGoBackFill color='#003553' size={16} />
                </div>
              </MPLeftButton>
            )}
          </MPAnimation>
        </Col>
      </Row>
      <OkModal
        title={'Xác nhận thanh toán'}
        open={modalVisible}
        onOk={async (e) => {
          try {
            setIsReserving(true)
            await onReserve(visaEntered)
          } finally {
            setIsReserving(false)
            window.dataLayer.push({
              event: 'Xác Nhận Phương Thức Thanh Toán',
              Action: 'Click',
              Category: 'Button-Action',
              Label: 'Đồng ý',
              Event: 'Xác Nhận Phương Thức Thanh Toán',
              PartnerId: partnerId,
              UserId: userId
            })
          }
        }}
        onCancel={() => setModalVisible(false)}
        okText={isReserving ? 'Đang xử lý...' : 'Đồng ý'}
        cancelText={'Quay lại'}
        cancelButtonProps={{ type: 'text' }}
        okButtonProps={{ loading: isReserving }}
        className={styles['modalConfirmPayment']}
      >
        <div>
          <div className={styles['title']}>
            {selectedPaymentType?.paymentIcon?.path && (
              <img src={selectedPaymentType?.paymentIcon?.path} alt='' />
            )}
            <p>
              Thanh toán số tiền
              <b> {getFormatMoney(selectedPaymentType?.grandTotal)} đ</b> bằng
              <b> {selectedPaymentType?.name}</b>
            </p>
          </div>
          <div className={styles['description']}>
            Bạn sẽ nhận được phiếu khám bệnh ngay khi{' '}
            <b>thanh toán thành công.</b> Trường hợp không nhận được phiếu khám
            bệnh, vui lòng liên hệ <b>19002115.</b>
          </div>
        </div>
      </OkModal>
      {updateVisa.status && (
        <OkModal
          open={updateVisa.status}
          footer={false}
          className={styles['UpdateVisa']}
          centered
          closable={false}
        >
          <div>
            <p className={styles.description}>
              Theo quy định của Tổ chức thẻ Quốc tế, vui lòng điền đầy đủ thông
              tin người thanh toán
            </p>
            <Form
              form={form}
              layout='vertical'
              initialValues={{
                ...selectedPaymentType?.userInfo,
                confirm: false
              }}
              onFinish={handleFinish}
            >
              <Form.Item
                label={handleRequireInput('Họ & tên', true)}
                name='fullName'
                rules={[{ validator: valid.required }]}
              >
                <Input placeholder='Nhập họ và tên ghi trên thẻ Visa' />
              </Form.Item>
              <Form.Item
                label={handleRequireInput('Số điện thoại', true)}
                name='userName'
                rules={[{ validator: valid.mobile }]}
              >
                <Input placeholder='Nhập số điện thoại' />
              </Form.Item>
              <Form.Item
                label={handleRequireInput('Email', true)}
                name='email'
                rules={[{ validator: valid.email }]}
              >
                <Input placeholder='Nhập email' />
              </Form.Item>
              <Form.Item name='confirm'>
                <Checkbox
                  onChange={handleCheckboxChange}
                  children={
                    <p className={styles.contentConfirm}>
                      Tôi xác nhận thông tin trên là chính xác và hoàn toàn chịu
                      trách nhiệm về thông tin này.
                    </p>
                  }
                />
              </Form.Item>
              <div className={styles.ButtonControl}>
                <Button type='ghost' onClick={handleCancel}>
                  Trở lại
                </Button>
                <Button
                  htmlType='submit'
                  disabled={!isCheckedVisa}
                  type='primary'
                >
                  Xác nhận
                </Button>
              </div>
            </Form>
          </div>
        </OkModal>
      )}
    </MPContainer>
  )
}
