import { createSlice, PayloadAction } from '@reduxjs/toolkit'

// Types
export interface PaymentInfo {
  transactionId: string
  amount: number
  customerName: string
  service: string
  bankAccount: string
  bankName: string
  transferCode: string
  status: string
  createdAt: string
  // Add other payment fields as needed
}

export interface PaymentState {
  paymentInfo: PaymentInfo | null
  loading: boolean
  error: string | null
  successModalOpen: boolean
}

// Initial State
const initialState: PaymentState = {
  paymentInfo: null,
  loading: false,
  error: null,
  successModalOpen: false
}

// Slice
const paymentSlice = createSlice({
  name: 'payment',
  initialState,
  reducers: {
    // Get Payment Info Actions
    getPaymentInfoRequest: (state, action: PayloadAction<{
      transactionId: string
      partnerId: string
      onSuccess?: (payment: PaymentInfo) => void
      onError?: (error: string) => void
    }>) => {
      state.loading = true
      state.error = null
    },
    
    getPaymentInfoSuccess: (state, action: PayloadAction<PaymentInfo>) => {
      state.loading = false
      state.paymentInfo = action.payload
      state.error = null
    },
    
    getPaymentInfoFailure: (state, action: PayloadAction<string>) => {
      state.loading = false
      state.error = action.payload
      state.paymentInfo = null
    },
    
    // UI Actions
    setSuccessModalOpen: (state, action: PayloadAction<boolean>) => {
      state.successModalOpen = action.payload
    },
    
    clearPaymentError: (state) => {
      state.error = null
    },
    
    resetPaymentState: (state) => {
      state.paymentInfo = null
      state.loading = false
      state.error = null
      state.successModalOpen = false
    }
  }
})

// Export actions
export const {
  getPaymentInfoRequest,
  getPaymentInfoSuccess,
  getPaymentInfoFailure,
  setSuccessModalOpen,
  clearPaymentError,
  resetPaymentState
} = paymentSlice.actions

// Selectors
export const selectPaymentInfo = (state: { payment: PaymentState }) => state.payment.paymentInfo
export const selectPaymentLoading = (state: { payment: PaymentState }) => state.payment.loading
export const selectPaymentError = (state: { payment: PaymentState }) => state.payment.error
export const selectSuccessModalOpen = (state: { payment: PaymentState }) => state.payment.successModalOpen

// Export reducer
export default paymentSlice.reducer
