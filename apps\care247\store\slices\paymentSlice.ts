import { createSlice, PayloadAction } from '@reduxjs/toolkit'

// Types
export interface PaymentInfo {
  transactionId: string
  amount: number
  customerName: string
  service: string
  bankAccount: string
  bankName: string
  transferCode: string
  status: string
  createdAt: string
}

export interface PaymentState {
  paymentInfo: PaymentInfo | null
  loading: boolean
  error: string | null
}

// Initial State
const initialState: PaymentState = {
  paymentInfo: null,
  loading: false,
  error: null
}

// Slice
const paymentSlice = createSlice({
  name: 'payment',
  initialState,
  reducers: {
    getPaymentInfo: (state, _action: PayloadAction<{
      transactionId: string
      partnerId: string
    }>) => {
      state.loading = true
      state.error = null
    },

    getPaymentInfoSuccess: (state, action: PayloadAction<PaymentInfo>) => {
      state.loading = false
      state.paymentInfo = action.payload
      state.error = null
    },

    getPaymentInfoFailure: (state, action: PayloadAction<string>) => {
      state.loading = false
      state.error = action.payload
      state.paymentInfo = null
    },

    clearPaymentError: (state) => {
      state.error = null
    },

    resetPaymentState: (state) => {
      state.paymentInfo = null
      state.loading = false
      state.error = null
    }
  }
})

// Export actions
export const paymentActions = paymentSlice.actions

// Selectors
export const selectPaymentInfo = (state: any) => {
  console.log('🔍 Selector state:', state)
  return state.payment?.paymentInfo
}
export const selectPaymentLoading = (state: any) => state.payment?.loading
export const selectPaymentError = (state: any) => state.payment?.error

// Export reducer
export default paymentSlice.reducer
