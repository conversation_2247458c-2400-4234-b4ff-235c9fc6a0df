import { createSlice, PayloadAction } from '@reduxjs/toolkit'

// Types
export interface PaymentInfo {
  appId: string
  partnerId: string
  type: number
  feeCode: string
  amount: number
  subTotal: number
  totalFee: number
  medproFee: number
  transferFee: number
  hospitalName: string
  partnerImage: string
}

export interface TranslateData {
  [key: string]: string
}

export interface PaymentState {
  paymentInfo: PaymentInfo | null
  translateData: TranslateData | null
  loading: boolean
  translateLoading: boolean
  error: string | null
  translateError: string | null
}

// Initial State
const initialState: PaymentState = {
  paymentInfo: null,
  translateData: null,
  loading: false,
  translateLoading: false,
  error: null,
  translateError: null
}

// Slice
const paymentSlice = createSlice({
  name: 'payment',
  initialState,
  reducers: {
    getPaymentInfo: (state, _action: PayloadAction<{
      transactionId: string
      partnerId: string
    }>) => {
      state.loading = true
      state.error = null
    },

    getPaymentInfoSuccess: (state, action: PayloadAction<PaymentInfo>) => {
      state.loading = false
      state.paymentInfo = action.payload
      state.error = null
    },

    getPaymentInfoFailure: (state, action: PayloadAction<string>) => {
      state.loading = false
      state.error = action.payload
      state.paymentInfo = null
    },

    getTranslate: (state, _action: PayloadAction<{
      language: string
    }>) => {
      state.translateLoading = true
      state.translateError = null
    },

    getTranslateSuccess: (state, action: PayloadAction<TranslateData>) => {
      state.translateLoading = false
      state.translateData = action.payload
      state.translateError = null
    },

    getTranslateFailure: (state, action: PayloadAction<string>) => {
      state.translateLoading = false
      state.translateError = action.payload
      state.translateData = null
    },

    clearPaymentError: (state) => {
      state.error = null
    },

    resetPaymentState: (state) => {
      state.paymentInfo = null
      state.translateData = null
      state.loading = false
      state.translateLoading = false
      state.error = null
      state.translateError = null
    }
  }
})

// Export actions
export const paymentActions = paymentSlice.actions

// Selectors
export const selectPaymentInfo = (state: any) => {
  console.log('🔍 Selector state:', state)
  return state.payment?.paymentInfo
}
export const selectPaymentLoading = (state: any) => state.payment?.loading
export const selectPaymentError = (state: any) => state.payment?.error
export const selectTranslateData = (state: any) => state.payment?.translateData
export const selectTranslateLoading = (state: any) => state.payment?.translateLoading
export const selectTranslateError = (state: any) => state.payment?.translateError

// Export reducer
export default paymentSlice.reducer
