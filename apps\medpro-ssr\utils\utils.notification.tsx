import { notification } from 'antd'
import { MPIcon } from '@medpro-libs/libs'
import { ArgsProps } from 'antd/lib/notification'
import { getError } from './utils.error'

type NotificationType = 'success' | 'info' | 'warning' | 'error' | 'open'
type NotificationStyle = 'primary'

const icons = {
  success: <MPIcon name={'Note'} />,
  info: <MPIcon name={'Note'} />,
  warning: <MPIcon name={'Repair'} />,
  error: <span></span>,
  open: <span></span>
}

export const openNotification = (
  type: NotificationType,
  args: ArgsProps,
  style?: NotificationStyle
) => {
  notification[type]({
    icon: icons[type],
    className: style,
    style: {
      width: 'auto',
      maxWidth: 500
    },
    ...args
  })
}

export const showMessage = (
  message: string,
  type: NotificationType = 'success'
) => {
  openNotification(type, { message })
}

export const showError = (err: any) => {
  const parsedError = getError(err)
  console.info('parsedError: ', parsedError)
  openNotification('error', {
    message: parsedError.message
  })
}
