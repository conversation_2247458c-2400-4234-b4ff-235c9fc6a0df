import React from 'react'
import <PERSON><PERSON><PERSON><PERSON><PERSON>pitalComponent, {
  ItemRender
} from '../../component/MPListHospitalComponent'
import MPListHospitalCard from '../../ui/MPListHospitalCard'

interface IF_ListHospital {
  data: any[]
  appInfo: any
  provinces: any[]
  onBooking: (item: any, setMessageHospital: any) => void
  onSelectHospital: (hospital: any, setHospitalDescription: any) => void
  handleDetectLocation: () => void
  locationData: any
}

export const MPListHospital = (props: IF_ListHospital) => {
  return (
    <MPListHospitalComponent
      hospitals={props.data}
      onSelectHospital={props.onSelectHospital}
      onBooking={props.onBooking}
      provinces={props.provinces}
      locationData={props.locationData}
      renderItem={({
        dataProvinces,
        data,
        type,
        dataSelected,
        searching,
        title,
        messageHospital,
        setMessageHospital,
        countSearch,
        isMobile,
        stickyScroll,
        setCount,
        count,
        loading,
        setLoading,
        filteredList,
        handleSelectHospital,
        handleBooking,
        handleViewMore,
        renderPagination,
        handleFilterCity,
        handleSearchDebounce,
        handleTypeSelect
      }: ItemRender) => {
        return (
          <MPListHospitalCard
            data={data}
            type={type}
            appInfo={props.appInfo}
            provinces={dataProvinces}
            dataSelected={dataSelected}
            searching={searching}
            title={title}
            messageHospital={messageHospital}
            setMessageHospital={setMessageHospital}
            countSearch={countSearch}
            isMobile={isMobile}
            stickyScroll={stickyScroll}
            setCount={setCount}
            count={count}
            loading={loading}
            setLoading={setLoading}
            filteredList={filteredList}
            handleBooking={handleBooking}
            handleViewMore={handleViewMore}
            handleSelectHospital={handleSelectHospital}
            renderPagination={renderPagination}
            handleFilterCity={handleFilterCity}
            handleSearchDebounce={handleSearchDebounce}
            handleTypeSelect={handleTypeSelect}
            locationData={props.locationData}
            handleDetectLocation={props.handleDetectLocation}
          />
        )
      }}
    />
  )
}

export default MPListHospital
