import { useEffect, useState } from 'react'
import styles from './styles.module.less'

interface QRPaymentProps {
  transactionId: string
  amount: number
  bankAccount?: string
  bankName?: string
}

export function QRPayment({
  transactionId,
  amount,
  bankAccount = '',
  bankName = ''
}: QRPaymentProps) {
  const [timeLeft, setTimeLeft] = useState(3172) // 52:52 in seconds

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timer)
          return 0
        }
        return prevTime - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('vi-VN').format(amount) + 'đ'
  }

  return (
    <div className={styles.qrPaymentContainer}>
      <div className={styles.paymentHeader}>
        <h2>THANH TOÁN</h2>
        <div className={styles.statusBadges}>
          <span className={styles.badge}>Giao dịch kết thúc sau</span>
          <span className={styles.timer}>{formatTime(timeLeft)}</span>
        </div>
      </div>

      <div className={styles.orderInfo}>
        <h3>THÔNG TIN ĐƠN HÀNG</h3>
        <div className={styles.orderDetails}>
          <div className={styles.orderItem}>
            <span>Mã đơn hàng</span>
            <span>{transactionId}</span>
          </div>
          <div className={styles.orderItem}>
            <span>Tên khách hàng</span>
            <span>Vũ Thị Kim Ngân</span>
          </div>
          <div className={styles.orderItem}>
            <span>Số tiền</span>
            <span>{formatAmount(amount)}</span>
          </div>
          <div className={styles.orderItem}>
            <span>Tên dịch vụ</span>
            <span>Trẻ sơ sinh, ghế nôi</span>
          </div>
        </div>
      </div>

      <div className={styles.transferInfo}>
        <h3>THÔNG TIN CHUYỂN KHOẢN</h3>
        <div className={styles.bankDetails}>
          <div className={styles.bankItem}>
            <span>Số tài khoản ngân hàng</span>
            <span>{bankAccount}</span>
          </div>
          <div className={styles.bankItem}>
            <span>Ngân hàng</span>
            <span>{bankName}</span>
          </div>
          <div className={styles.bankItem}>
            <span>Thanh toán bằng ứng dụng Ngân hàng/ Ví điện tử</span>
            <span>{transactionId}</span>
          </div>
        </div>
      </div>

      <div className={styles.qrSection}>
        <h3>Quét mã bằng ứng dụng Ngân hàng/ Ví điện tử</h3>
        <div className={styles.qrContainer}>
          <div className={styles.qrCode}>
            <div className={styles.qrPattern}>
              {/* QR Code pattern simulation */}
              <div className={styles.qrGrid}>
                {Array.from({ length: 25 }, (_, i) => (
                  <div
                    key={i}
                    className={`${styles.qrDot} ${
                      Math.random() > 0.5 ? styles.filled : ''
                    }`}
                  />
                ))}
              </div>
              <div className={styles.qrCorners}>
                <div className={styles.corner}></div>
                <div className={styles.corner}></div>
                <div className={styles.corner}></div>
              </div>
            </div>
          </div>
          <div className={styles.vietqrLogo}>
            <div className={styles.logoPlaceholder}>VietQR</div>
          </div>
        </div>
        <p className={styles.transactionCode}>{transactionId}</p>
      </div>

      <div className={styles.actionButtons}>
        <button className={styles.cancelBtn}>✕ Hủy giao dịch</button>
        <button className={styles.confirmBtn}>Tôi đã thanh toán</button>
      </div>
    </div>
  )
}

export default QRPayment
