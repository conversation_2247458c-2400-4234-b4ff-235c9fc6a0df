import { Valid } from '@medpro-libs/libs'
import { Form, Input, Select } from 'antd'
import { get, size } from 'lodash'
import styles from '../styles.module.less'
import cx from 'classnames'
// export interface ListFormIF {}

const valid = new Valid()
const { Option } = Select

interface addressProps {
  data: any
  district: any
  ward: any
  openSelect: (field: any) => void
  form: any
}

export const handleAddress = ({
  data,
  district,
  ward,
  openSelect,
  form
}: addressProps) => {
  const handleRequireInput = (label: string, require: boolean) => {
    if (require) {
      return (
        <>
          {label} <sup className={styles['requireInput']}>*</sup>
        </>
      )
    }
    return <>{label}</>
  }

  const ignoreProperties = get(data, 'patient.propertyIgnoreUpdate', [])

  const list = [
    {
      id: 'city_id',
      type: 'text',
      label: 'Tỉnh/Thành',
      placeholder: 'Chọn tỉnh thành',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ required: require, validator: valid.province }]}
            className={styles['formInputItem']}
          >
            <Select
              disabled={disabled}
              placeholder={placeholder}
              open={false}
              onClick={() =>
                !disabled &&
                openSelect({
                  id,
                  title: 'Chọn tỉnh/thành',
                  data: data?.province
                })
              }
              className={cx({
                [styles['validInput']]:
                  form.getFieldValue('city_id') && !disabled,
                [styles['disabledValidInput']]: disabled
              })}
            >
              {size(data?.province) > 0 &&
                data?.province?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'district_id',
      type: 'text',
      label: 'Quận/Huyện',
      placeholder: 'Chọn quận huyện',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ required: require, validator: valid.district }]}
            className={styles['formInputItem']}
          >
            <Select
              disabled={disabled}
              placeholder={placeholder}
              open={false}
              onClick={() =>
                !disabled &&
                openSelect({
                  id,
                  title: 'Chọn quận/huyện',
                  data: district
                })
              }
              className={cx({
                [styles['validInput']]:
                  form.getFieldValue('district_id') && !disabled,
                [styles['disabledValidInput']]: disabled
              })}
            >
              {size(district) > 0 &&
                district?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'ward_id',
      type: 'text',
      label: 'Phường/Xã',
      placeholder: 'Chọn xã phường',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            // style={{ marginTop: 18 }}
            rules={[{ required: require, validator: valid.ward }]}
            className={styles['formInputItem']}
          >
            <Select
              disabled={disabled}
              open={false}
              placeholder={placeholder}
              onClick={() =>
                !disabled &&
                openSelect({
                  id,
                  title: 'Chọn phường/xã',
                  data: ward
                })
              }
              className={cx({
                [styles['validInput']]:
                  form.getFieldValue('ward_id') && !disabled,
                [styles['disabledValidInput']]: disabled
              })}
            >
              {size(ward) > 0 &&
                ward?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'address',
      type: 'text',
      label: (
        <div className={styles['boxAddress']}>
          <div>{handleRequireInput('Số nhà/Tên đường/Ấp thôn xóm', true)}</div>

          <p className={styles['sup']}>
            (không bao gồm tỉnh/thành, quận/huyện, phường/xã)
          </p>
        </div>
      ),
      placeholder: 'Nhập số nhà, tên đường, ấp thôn xóm,...',
      require: true,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ required: require, validator: valid.address }]}
            className={styles['formInputItem']}
          >
            <Input
              size='large'
              disabled={disabled}
              type={type}
              placeholder={placeholder}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    }
  ]

  return list.map((l) => ({ ...l, disabled: ignoreProperties.includes(l.id) }))
}
