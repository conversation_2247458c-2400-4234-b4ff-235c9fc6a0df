import { Col, Row } from 'antd'
import cx from 'classnames'
import { debounce, size } from 'lodash'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { ReactNode, useEffect, useState } from 'react'
import { LIST_TYPE_PARTNER } from '../../common/constant'
import derectionUp from '../../common/images/derectionUp.svg'
import MPHeaderHospitals from '../../shapes/MPHeaderHospitals'
import MPHospitalIntroduce from '../../shapes/MPHospitalIntroduce'
import MPButton from '../MPButton'
import MPContainer from '../MPContainer'
import { AlertModal } from '../common/Modal'
import HopitalsSkeleton from '../common/selekon/HopitalsSkeleton'
import HospitalItem from './common/HospitalItem'
import EmptyList from './common/images/EmptyList.png'
import styles from './styles.module.less'

export interface ListHospitalProps {
  data: any[]
  type: any
  appInfo: any
  provinces: any[]
  searching: any
  title: any
  messageHospital: any
  setMessageHospital: any
  dataSelected: any
  countSearch: any
  isMobile: boolean
  stickyScroll: boolean
  setCount: any
  setLoading: any
  count: any
  loading: any
  locationData: any
  filteredList: any[]
  renderPagination: () => ReactNode
  handleBooking: (item: any) => void
  handleViewMore: (item: any) => void
  handleSelectHospital: (item: any) => void
  handleFilterCity: (v2: any) => void
  handleSearchDebounce: (event: any) => Promise<void>
  handleTypeSelect: (item: any) => void
  handleDetectLocation: () => void
}

const MPListHospitalCard = (props: ListHospitalProps) => {
  const [isHidden, setIsHidden] = useState(false)
  const router = useRouter()
  const handleScroll = debounce(() => {
    const scrollTop = window.scrollY
    const idHospital: any = document.getElementById('top')
    const width = idHospital.getBoundingClientRect()

    if (scrollTop >= width.top) {
      setIsHidden(true)
    } else {
      setIsHidden(false)
    }
  }, 5)
  useEffect(() => {
    window.addEventListener('scroll', handleScroll)

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  return (
    <div>
      <div className={styles['headerHospitals']}>
        <div id='top' />
        <MPHeaderHospitals
          title={props.title?.title || 'Cơ sở y tế'}
          province={props.provinces}
          handleSearch={props.handleFilterCity}
          onSearch={props.handleSearchDebounce}
          subTitle={
            props.title?.subTitle ||
            'Với những cơ sở Y Tế hàng đầu sẽ giúp trải nghiệm khám, chữa bệnh của bạn tốt hơn'
          }
          isHidden={isHidden}
          locationData={props.locationData}
          handleDetectLocation={props.handleDetectLocation}
        />
        <ul className={cx(styles['tag'], isHidden && styles['hidden'])}>
          {LIST_TYPE_PARTNER.flatMap((t) => {
            return (
              <li key={t.key}>
                <MPButton
                  type='primary'
                  className={cx({
                    [styles['tagItem']]: true,
                    [styles['active']]: props.type === t.key
                  })}
                  onClick={() => {
                    props.isMobile && props.setCount(10)
                    props.handleTypeSelect(t.key)
                    window.scrollTo({
                      top: 0
                    })
                  }}
                >
                  {!router?.query?.type ? (
                    <h2>{t.title + ` (${props.countSearch[t.key] || 0})`}</h2>
                  ) : (
                    <p>{t.title + ` (${props.countSearch[t.key] || 0})`}</p>
                  )}
                </MPButton>
              </li>
            )
          })}
        </ul>
      </div>
      <MPContainer medproSeo>
        {props.searching ? (
          <HopitalsSkeleton />
        ) : (
          <div className={styles['listHospital']}>
            {size(props.data) > 0 ? (
              <>
                <Row gutter={[26, 0]}>
                  <Col span={24} lg={{ span: 15, order: 1 }}>
                    {props.data?.map((item: any) => {
                      const isSelect =
                        item.partnerId === props.dataSelected?.partnerId
                      return (
                        <HospitalItem
                          key={`detail${item._id}`}
                          onSelect={() => {
                            props.handleSelectHospital(item)
                          }}
                          data={item}
                          itemSelect={props.dataSelected}
                          handleBooking={props.handleBooking}
                          handleViewMore={props.handleViewMore}
                          isSelect={isSelect}
                        />
                      )
                    })}
                  </Col>
                  {!props.isMobile && (
                    <Col
                      span={24}
                      lg={{ span: 9, order: 2 }}
                      className={cx(
                        styles['detailHospital'],
                        props.stickyScroll
                          ? styles[`detailBottom`]
                          : styles[`detailTop`]
                      )}
                    >
                      <MPHospitalIntroduce
                        data={props.dataSelected}
                        downloadApp={props.appInfo}
                      />
                    </Col>
                  )}
                  {props.messageHospital && (
                    <AlertModal
                      open={!!props.messageHospital}
                      onOk={() => props.setMessageHospital('')}
                      onCancel={() => props.setMessageHospital('')}
                    >
                      {props.messageHospital}
                    </AlertModal>
                  )}
                </Row>
                {!props.isMobile && (
                  <Row>
                    <Col span={24} lg={{ span: 24, order: 3 }}>
                      <div className={styles['pagination']}>
                        {props.renderPagination()}
                      </div>
                    </Col>
                  </Row>
                )}
                {props.loading ? (
                  <div className={styles['loadingRing']}>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                  </div>
                ) : (
                  size(props.filteredList) > props.count &&
                  props.isMobile && (
                    <div
                      onClick={() => {
                        props.setCount((prev: any) => prev + 10)
                      }}
                      className={styles['viewMove']}
                    >
                      <p>Xem tiếp</p>
                      <span>
                        <Image
                          src={derectionUp}
                          width={13}
                          height={13}
                          objectFit='contain'
                          alt='Icon Derection Up'
                          layout='fixed'
                        />
                      </span>
                    </div>
                  )
                )}
              </>
            ) : (
              <div className={styles['error']}>
                <p>Danh sách sẽ cập nhật trong thời gian tới</p>
                <Image src={EmptyList} alt='Icon empty' layout='fixed' />
              </div>
            )}
          </div>
        )}
      </MPContainer>
    </div>
  )
}

export default MPListHospitalCard
