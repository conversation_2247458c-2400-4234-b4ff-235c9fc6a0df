import moment from 'moment'
import { GetServerSideProps } from 'next'
import getConfig from 'next/config'
import { noLayout } from '../../layout'

const { publicRuntimeConfig } = getConfig()

const buildTimestamp = moment(publicRuntimeConfig.modifiedDate).format(
  'YYYY-MM-DD'
)

const Sitemap = () => {
  return <p> Sitemap </p>
}

Sitemap.getLayout = noLayout
export default Sitemap

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const host = ctx.req?.headers['x-forwarded-host'] || ctx.req?.headers['host']
  ctx.res.setHeader('Content-Type', 'text/xml')

  const xml = getSitemap({ host })
  ctx.res.write(xml)
  ctx.res.end()

  return { props: {} }
}

const LIST_ROUTER = [
  '/',
  '/co-so-y-te',
  '/co-so-y-te/phong-kham',
  '/co-so-y-te/phong-mach',
  '/co-so-y-te/benh-vien-cong',
  '/co-so-y-te/benh-vien-tu',
  '/co-so-y-te/xet-nghiem',
  '/co-so-y-te/y-te-tai-nha',
  '/co-so-y-te/tiem-chung',
  '/tim-kiem',
  '/dich-vu-y-te',
  '/dich-vu-y-te/dat-kham-tai-co-so',
  '/dich-vu-y-te/dat-kham-theo-bac-si',
  '/dich-vu-y-te/tu-van-kham-benh-tu-xa',
  '/dich-vu-y-te/dat-lich-xet-nghiem',
  '/dich-vu-y-te/goi-kham-suc-khoe',
  '/dich-vu-y-te/dat-lich-tiem-chung',
  '/dich-vu-y-te/y-te-tai-nha',
  '/dich-vu-y-te/thanh-toan-vien-phi',
  '/goi-kham-suc-khoe',
  '/bac-si',
  '/umc',
  '/da-lieu',
  '/clinic',
  '/ve-chung-toi',
  '/tin-tuc',
  '/tin-tuc/tin-dich-vu',
  '/tin-tuc/tin-y-te',
  '/tin-tuc/y-hoc-thuong-thuc',
  // '/tuyen-dung',
  '/quy-dinh-su-dung',
  '/lien-he',
  // '/hop-tac',
  '/tim-lai-ma-benh-nhan',
  '/ket-qua-tim-benh-nhan',
  '/xac-nhan-thong-tin-benh-nhan',
  '/xac-thuc-dien-thoai-benh-nhan',
  // '/danh-sach-benh-vien',
  '/hinh-thuc-dat-kham',
  '/chon-ho-so',
  '/tao-moi-ho-so',
  '/cap-nhat-thong-tin',
  '/check-patient-before-create',
  '/chon-lich-kham',
  '/chi-tiet-phieu-kham-benh',
  '/xac-nhan-thong-tin',
  '/phuong-thuc-thanh-toan',
  '/share',
  '/user',
  '/tim-ma-thanh-toan-vien-phi',
  '/chi-tiet-ma-thanh-toan',
  '/hinh-thuc-thanh-toan-vien-phi',
  '/lich-su-thanh-toan-vien-phi',
  '/huong-dan-bao-hiem-y-te-trai-tuyen',
  '/huong-dan-bao-hiem-y-te-trai-tuyen-app',
  '/thong-tin-dat-kham-mono',
  '/gioi-thieu',
  '/quy-trinh',
  '/reexam',
  '/huong-dan/cai-dat-ung-dung',
  '/huong-dan/dat-lich-kham',
  '/huong-dan/tu-van-kham-benh-tu-xa',
  '/huong-dan/quy-trinh-hoan-phi',
  '/huong-dan/cau-hoi-thuong-gap',
  '/tra-cuu-hoa-don-dien-tu',
  '/danh-sach-hoa-don-dien-tu',
  '/dieu-khoan-dich-vu',
  '/chinh-sach-bao-mat',
  '/kham-suc-khoe-doanh-nghiep',
  '/hop-tac-phong-mach',
  '/doi-tac-co-so-y-te'
]

const getSitemap = ({ host }: any) => {
  const getRouter = Array.from(new Set(LIST_ROUTER))
    .map((item) => {
      return `<url>
                 <loc>https://${host}${item}</loc>
                  <lastmod>${buildTimestamp}</lastmod>
                    <changefreq>daily</changefreq>
                    <priority>1.0</priority>
              </url>`
    })
    .join('')
  return `<?xml version="1.0" encoding="utf-8"?>
        <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        ${getRouter}
      </urlset>`
}
