export const URL_CMS = {
  TESTING: 'https://cms.medpro.com.vn',
  LIVE: 'https://cms.medpro.com.vn',
  HOTFIX: 'https://cms.medpro.com.vn',
  BETA: 'https://cms.medpro.com.vn'
}

export const GOOGLE_RECAPCHA_SITEKEY_ENV = {
  DEVELOP: '6LeYoO8oAAAAAEaLA4GLuWUBTgto-mTTDsv96kES',
  TESTING: '6LeYoO8oAAAAAEaLA4GLuWUBTgto-mTTDsv96kES',
  LIVE: '6LcaKyEpAAAAAAXphvAybaJVtkUwuokMQ1Y9evzF',
  BETA: '6LcaKyEpAAAAAAXphvAybaJVtkUwuokMQ1Y9evzF',
  HOTFIX: '6LcaKyEpAAAAAAXphvAybaJVtkUwuokMQ1Y9evzF'
}

export const URL_LOGIN = {
  TESTING: 'https://id-testing.medpro.com.vn/check-phone',
  BETA: 'https://id-beta.medpro.com.vn/check-phone',
  LIVE: 'https://id-v121.medpro.com.vn/check-phone',
  HOTFIX: 'https://id-v121.medpro.com.vn/check-phone'
}

export const URL_BE = {
  DEVELOP: 'http://192.168.0.101:6500',
  TESTING: 'https://medpro-api-v3-testing.medpro.com.vn',
  BETA: 'https://api-beta.medpro.com.vn',
  LIVE: 'https://api-v2.medpro.com.vn',
  HOTFIX: 'https://api-hotfix.medpro.com.vn'
}

export const URL_BO = {
  TESTING: 'https://bo-api-testing.medpro.com.vn',
  BETA: 'https://bo-api.medpro.com.vn',
  LIVE: 'https://bo-api.medpro.com.vn',
  HOTFIX: 'https://bo-api.medpro.com.vn'
}

export const URL_STATIC_RESOURCE = {
  TESTING: 'https://resource.medpro.com.vn',
  BETA: 'https://resource.medpro.com.vn',
  LIVE: 'https://resource.medpro.com.vn',
  HOTFIX: 'https://resource.medpro.com.vn'
}

export const URL_PAGE = {
  TRANG_CHU: '/',
  CHON_BENH_VIEN: 'benh-vien',
  CHON_BENH_VIEN_FOR_CSKH: 'benh-vien-cskh',
  THANH_TOAN_HO: 'thanh-toan-ho',
  DAT_LICH_KHAM: 'dat-kham',
  HINH_THUC_DAT_KHAM: 'hinh-thuc-dat-kham',
  LICH_SU_THANH_TOAN_VIEN_PHI: 'lich-su-thanh-toan-vien-phi',
  PHUONG_THUC_THANH_TOAN: 'phuong-thuc-thanh-toan',
  XAC_NHAN_THONG_TIN: 'xac-nhan-thong-tin',
  CHI_TIET_PHIEU_KHAM: 'chi-tiet-phieu-kham',
  THONG_TIN_HO_SO: 'thong-tin-ho-so',
  TAO_HO_SO: 'tao-ho-so',
  TIM_MA_THANH_TOAN_VIEN_PHI: 'tim-ma-thanh-toan-vien-phi',
  BHYTDungTuyen: 'huong-dan-bao-hiem-y-te-dung-tuyen',
  BHYTTraiTuyen: 'huong-dan-bao-hiem-y-te-trai-tuyen',
  BHYTTraiTuyen_Dalieuhcm: 'huong-dan-bao-hiem-y-te-trai-tuyen-dalieuhcm',
  CHON_HO_SO: 'chon-ho-so',
  CHI_TIET_MA_THANH_TOAN: 'chi-tiet-ma-thanh-toan',
  CAP_NHAT_HO_SO: 'cap-nhat-ho-so',
  XAC_THUC_SO_DIEN_THOAI_BENH_NHAN: 'xac-thuc-dien-thoai-benh-nhan',
  XAC_NHAN_THONG_TIN_BENH_NHAN: 'xac-nhan-thong-tin-benh-nhan',
  HUONG_DANH_BENH_NHAN: 'huong-danh-benh-nhan',
  TIM_LAI_MA_BENH_NHAN: 'tim-lai-ma-benh-nhan',
  KET_QUA_THANH_TOAN_VIEN_PHI: 'ket-qua-thanh-toan-vien-phi',
  HUONG_DAN_DAT_KHAM: 'huong-dan-dat-kham',
  DAN_XUAT: 'dang-xuat',
  GIOI_THIEU: 'gioi-thieu',
  LIEN_HE: 'lien-he',
  CHINH_SACH_BAO_MAT: 'chinh-sach-bao-mat',
  QUY_DINH_SU_DUNG: 'quy-dinh-su-dung',
  AuthVerifyCskh: 'auth-verify-cskh',
  CSKH: 'cskh',
  BOOKING: 'booking'
}

export const RADA_PUBLISHABLE = {
  DEVELOP: 'prj_test_pk_febb9d69ef3d0d4d511b92db330362a7cb40bf1d',
  TESTING: 'prj_test_pk_febb9d69ef3d0d4d511b92db330362a7cb40bf1d',
  LIVE: 'prj_live_pk_503f1e7d4a2219d1c6f0e79d96579cd9be9adf84',
  BETA: 'prj_live_pk_503f1e7d4a2219d1c6f0e79d96579cd9be9adf84',
  HOTFIX: 'prj_live_pk_503f1e7d4a2219d1c6f0e79d96579cd9be9adf84'
}

export const enum NameEnv {
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  PRODUCTION = 'production',
  HOTFIX = 'hotfix'
}
