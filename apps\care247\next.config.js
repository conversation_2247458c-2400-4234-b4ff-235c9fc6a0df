//@ts-check

// eslint-disable-next-line @typescript-eslint/no-var-requires
const { withNx } = require('@nrwl/next/plugins/with-nx')
const withPlugins = require('next-compose-plugins')

// This plugin is needed until this PR is merged.
// https://github.com/vercel/next.js/pull/23185
const { withLess } = require('@nrwl/next/plugins/with-less')
const withFonts = require('next-fonts')
// const withBundleAnalyzer = require('@next/bundle-analyzer')({
//   enabled: false
// })

/**
 * @type {import('@nrwl/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  useFileSystemPublicRoutes: true,
  nx: {
    // Set this to true if you would like to to use SVGR
    // See: https://github.com/gregberge/svgr
    svgr: false
  },
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    config.module.rules.push({
      test: /\.(js|jsx|ts|tsx)$/,
      use: defaultLoaders.babel,
      exclude: /node_modules/
    })

    // Important: return the modified config
    return config
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*longvan.net'
      },
      {
        protocol: 'https',
        hostname: '*medpro.com.vn*'
      },
      {
        protocol: 'https',
        hostname: '*medpro.vn'
      }
    ],
    dangerouslyAllowSVG: true,
    domains: [
      'bo-api.medpro.com.vn',
      'bo-api-testing.medpro.com.vn',
      'api-backoffice.medpro.com.vn',
      'medpro.com.vn',
      'resource.medpro.com.vn',
      'resource-testing.medpro.com.vn',
      'medpro.vn',
      'cms.medpro.com.vn',
      'prod-partner.s3-hcm-r1.longvan.net',
      'testing-partner.s3-hcm-r1.longvan.net',
      'testing-partner.s3-hcm1-r1.longvan.net',
      'medpro-api-v2-testing.medpro.com.vn',
      'api-v2.medpro.com.vn',
      'demobvdaihocapi.umc.edu.vn',
      'cdn.medpro.vn',
    ]
  },
  publicRuntimeConfig: {
    modifiedDate: new Date().toISOString()
  },
  experimental: {
    scrollRestoration: true
  },
  async headers() {
    return [
      {
        source: "/(.*)", // áp dụng cho tất cả các route
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY", // "DENY" Cấm mọi iframe hoặc "SAMEORIGIN" nếu bạn muốn cho phép từ cùng domain
          },
          {
            key: "Content-Security-Policy",
            value: "frame-ancestors 'none'", // Không cho phép iframe từ bất kỳ nguồn nào hoặc 'self' nếu bạn muốn cho cùng origin
          },
        ],
      },
    ];
  }
}

module.exports = withPlugins([/*withBundleAnalyzer,*/ withLess, withNx, withFonts], nextConfig)
