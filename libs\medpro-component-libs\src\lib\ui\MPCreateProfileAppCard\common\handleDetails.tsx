import { Form, Input, Select } from 'antd'
import styles from '../styles.module.less'
import { Valid, getReplaceUTF8, sexData } from '@medpro-libs/libs'
import { size } from 'lodash'
import cx from 'classnames'
import { FormInstance } from 'antd/es/form/Form'

// export interface ListFormIF {}

const valid = new Valid()
const { Option } = Select

interface Detail {
  data: any
  openSelect: (field: any) => void
  form: FormInstance<any>
  toggleAge?: any
}

const handleRequireInput = (label: string, require: boolean, sup?: any) => {
  if (require) {
    return (
      <>
        <div>
          {label} <sup className={styles['requireInput']}>*</sup>
        </div>
        {sup && <div className={styles['sup']}>({sup})</div>}
      </>
    )
  }
  return <>{label}</>
}

export const handleDetails = ({
  data,
  openSelect,
  form,
  toggleAge
}: Detail) => {
  const list = [
    {
      id: 'name',
      type: 'text',
      label: 'Họ và tên (có dấu)',
      placeholder: 'Nhập họ và tên',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.name }]}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'mobile',
      type: 'text',
      label: 'Số điện thoại',
      placeholder: 'Nhập số điện thoại',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.mobile }]}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'cmnd',
      type: 'text',
      label: 'Mã định danh/CCCD/Passport',
      placeholder: 'Vui lòng nhập mã định danh/CCCD/Passport',
      require: true,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[
              {
                validator: valid.cccd
              }
            ]}
            className={styles['formInputItem']}
          >
            <Input
              type={type}
              disabled={disabled}
              placeholder={placeholder}
              className={cx({
                [styles['validInput']]: form.getFieldValue('cmnd') && !disabled
              })}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'birthdate',
      type: 'text',
      label: 'Ngày sinh',
      placeholder: 'Nhập ngày sinh',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.birthdate }]}
            className={styles['formInputItem']}
          >
            <Input
              type={type}
              onChange={() => {
                toggleAge(form.getFieldValue('birthdate'))
              }}
              placeholder={placeholder}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: ''
    },
    {
      id: 'sex',
      type: 'select',
      label: 'Giới tính',
      placeholder: 'Chọn giới tính',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.sex }]}
            className={styles['formInputItem']}
          >
            <Select
              placeholder={placeholder}
              open={false}
              onClick={() => openSelect({ id })}
              className={
                form.getFieldValue('sex') === 0 ||
                form.getFieldValue('sex') === 1
                  ? styles['validInput']
                  : ''
              }
            >
              {sexData?.map((item, index) => (
                <Option key={index} value={item.value}>
                  {item.title}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: ''
    },
    {
      id: 'city_id',
      type: 'select',
      label: 'Tỉnh/Thành phố',
      placeholder: 'Nhập tỉnh / thành phố',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(
              label,
              require,
              'theo thông tin trên CCCD'
            )}
            name={id}
            rules={[{ validator: valid.province }]}
            className={styles['formInputItem']}
          >
            <Select
              placeholder={placeholder}
              open={false}
              onClick={() =>
                openSelect({
                  id,
                  title: 'Chọn tỉnh/thành',
                  data: data?.province
                })
              }
              className={form.getFieldValue('city_id') && styles['validInput']}
            >
              {size(data?.province) > 0 &&
                data?.province?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'insuranceId',
      type: 'text',
      label: 'Mã bảo hiểm y tế',
      placeholder: 'Nhập mã bảo hiểm y tế',
      require: false,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            className={styles['formInputItem']}
          >
            <Input
              type={type}
              disabled={disabled}
              placeholder={placeholder}
              className={cx({
                [styles['validInput']]:
                  form.getFieldValue('insuranceId') && !disabled
              })}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    }
  ]
  return list
}

interface DetailQRScan {
  openSelect: (field: any) => void
  province: any[]
  form: any
  partnerId?: string
  toggleAge?: any
  data?: any
  ward?: any
  district?: any
  isForeign?: boolean
}

export const handleDetailQRScan = ({
  openSelect,
  province,
  form,
  toggleAge,
  data,
  ward,
  district,
  isForeign
}: DetailQRScan) => {
  const label_code_ID = 'Mã định danh/CCCD/Passport'

  const list = [
    {
      id: 'name',
      type: 'text',
      label: 'Họ và tên (có dấu)',
      placeholder: 'Nhập họ và tên',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.name }]}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'mobile',
      type: 'text',
      label: 'Số điện thoại',
      placeholder: 'Nhập số điện thoại',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.mobile }]}
            className={styles['formInputItem']}
          >
            <Input type={type} placeholder={placeholder} />
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'birthdate',
      type: 'text',
      label: 'Ngày sinh',
      placeholder: 'Nhập ngày sinh',
      require: true,
      enter: ({ id, require, type, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.birthdate }]}
            className={styles['formInputItem']}
          >
            <Input
              type={type}
              onChange={() => {
                toggleAge(form.getFieldValue('birthdate'))
              }}
              placeholder={placeholder}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: ''
    },
    {
      id: 'sex',
      type: 'select',
      label: 'Giới tính',
      placeholder: 'Chọn giới tính',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.sex }]}
            className={styles['formInputItem']}
          >
            <Select
              placeholder={placeholder}
              open={false}
              onClick={() => openSelect({ id })}
              className={
                form.getFieldValue('sex') === 0 ||
                form.getFieldValue('sex') === 1
                  ? styles['validInput']
                  : ''
              }
            >
              {sexData?.map((item, index) => (
                <Option key={index} value={item.value}>
                  {item.title}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: ''
    },
    {
      id: 'cmnd',
      type: 'text',
      label: label_code_ID,
      placeholder: `Vui lòng nhập ${label_code_ID}`,
      require: true,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[
              {
                validator: valid.cccd
              }
            ]}
            className={styles['formInputItem']}
          >
            <Input
              type={type}
              disabled={disabled}
              placeholder={placeholder}
              className={cx({
                [styles['validInput']]: form.getFieldValue('cmnd') && !disabled
              })}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'country_code',
      type: 'text',
      label: 'Quốc gia',
      placeholder: 'Chọn quốc gia',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.country }]}
            className={styles['selectItem']}
          >
            <Select
              disabled={disabled}
              open={false}
              placeholder={placeholder}
              onClick={() =>
                openSelect({
                  id,
                  title: 'Chọn quốc gia',
                  data: data?.countries
                })
              }
            >
              {size(data?.countries) > 0 &&
                data?.countries?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'dantoc_id',
      type: 'select',
      label: 'Dân tộc',
      placeholder: 'Nhập Dân tộc',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[
              {
                validator: valid.nation
              }
            ]}
            className={styles['formInputItem']}
          >
            <Select
              placeholder={placeholder}
              disabled={
                form.getFieldValue('dantoc_id') === 'medpro_82'
                  ? true
                  : disabled
              }
              open={false}
              onClick={() =>
                !(form.getFieldValue('dantoc_id') === 'medpro_82'
                  ? true
                  : disabled) &&
                openSelect({
                  id,
                  title: 'Chọn dân tộc',
                  data: data?.nation
                })
              }
              className={cx({
                [styles['validInput']]:
                  form.getFieldValue('dantoc_id') &&
                  !(form.getFieldValue('dantoc_id') === 'medpro_82'
                    ? true
                    : disabled),
                [styles['disabledValidInput']]:
                  form.getFieldValue('dantoc_id') === 'medpro_82'
                    ? true
                    : disabled
              })}
            >
              {size(data?.nation) > 0 &&
                data?.nation?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'city_id',
      type: 'select',
      label: 'Tỉnh/Thành phố',
      placeholder: 'Nhập tỉnh / thành phố',
      require: true,
      enter: ({ id, require, placeholder, label }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(
              label,
              require,
              'theo thông tin trên CCCD'
            )}
            name={id}
            rules={[{ validator: valid.province }]}
            className={styles['formInputItem']}
          >
            <Select
              placeholder={placeholder}
              open={false}
              onClick={() =>
                openSelect({ id, title: 'Chọn tỉnh/thành', data: province })
              }
              className={form.getFieldValue('city_id') && styles['validInput']}
            >
              {size(province) > 0 &&
                province?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'district_id',
      type: 'text',
      label: 'Quận/Huyện',
      placeholder: 'Chọn quận huyện',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.district }]}
            className={styles['formInputItem']}
          >
            <Select
              disabled={disabled}
              placeholder={placeholder}
              open={false}
              onClick={() =>
                !disabled &&
                openSelect({
                  id,
                  title: 'Chọn quận/huyện',
                  data: district
                })
              }
              className={cx({
                [styles['validInput']]:
                  form.getFieldValue('district_id') && !disabled,
                [styles['disabledValidInput']]: disabled
              })}
            >
              {size(district) > 0 &&
                district?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'ward_id',
      type: 'text',
      label: 'Phường/Xã',
      placeholder: 'Chọn xã phường',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.ward }]}
            className={styles['formInputItem']}
          >
            <Select
              disabled={disabled}
              open={false}
              placeholder={placeholder}
              onClick={() =>
                !disabled &&
                openSelect({
                  id,
                  title: 'Chọn phường/xã',
                  data: ward
                })
              }
              className={cx({
                [styles['validInput']]:
                  form.getFieldValue('ward_id') && !disabled,
                [styles['disabledValidInput']]: disabled
              })}
            >
              {size(ward) > 0 &&
                ward?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'address',
      type: 'text',
      label: (
        <div className={styles['boxAddress']}>
          <div>{handleRequireInput('Số nhà/Tên đường/Ấp thôn xóm', true)}</div>

          <p className={styles['sup']}>
            (không bao gồm tỉnh/thành, quận/huyện, phường/xã)
          </p>
        </div>
      ),
      placeholder: 'Nhập số nhà, tên đường, ấp thôn xóm,...',
      require: true,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={
              !isForeign
                ? label
                : handleRequireInput('Địa chỉ lưu trú', require)
            }
            name={id}
            rules={[{ validator: valid.address }]}
            className={styles['formInputItem']}
          >
            <Input
              size='large'
              disabled={disabled}
              type={type}
              placeholder={placeholder}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    },
    {
      id: 'insuranceId',
      type: 'text',
      label: 'Mã bảo hiểm y tế',
      placeholder: 'Nhập mã bảo hiểm y tế',
      require: false,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            // rules={[{ validator: valid.mobile }]}
            className={styles['formInputItem']}
          >
            <Input
              type={type}
              disabled={disabled}
              placeholder={placeholder}
              className={cx({
                [styles['validInput']]:
                  form.getFieldValue('insuranceId') && !disabled
              })}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: '100%'
    }
  ]
  return list
}
