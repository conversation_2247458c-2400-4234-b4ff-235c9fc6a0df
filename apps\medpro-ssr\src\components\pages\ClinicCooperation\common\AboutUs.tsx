import React from 'react'
import cx from 'classnames'
import Image from 'next/image'
import Slider from 'react-slick'
import { RiArrowLeftSLine, RiArrowRightSLine } from 'react-icons/ri'
import IconNetwork from '../images/iconNetwork.png'
import IconPackage from '../images/iconPackage.png'
import IconExpense from '../images/iconExpense.png'
import IconService from '../images/iconService.png'
import BgAboutUs from '../images/bgAboutUs.png'
import styles from '../styles.module.less'

const AboutUs = (props: any) => {
  const { windowWidth } = props

  const SampleNextArrow = (props_next: any) => {
    const { className, style, onClick } = props_next
    return (
      <div
        className={cx(className, styles['btnNext'])}
        style={{ ...style }}
        onClick={onClick}
      >
        <RiArrowRightSLine
          size={20}
          color='#003553'
          aria-labelledby='Next Icon'
        />
      </div>
    )
  }

  const SamplePrevArrow = (props_prev: any) => {
    const { className, style, onClick } = props_prev
    return (
      <div
        className={cx(className, styles['btnPrev'])}
        style={{ ...style }}
        onClick={onClick}
      >
        <RiArrowLeftSLine
          size={20}
          color='#003553'
          aria-labelledby='Previous Icon'
        />
      </div>
    )
  }

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    rows: 1,
    autoplay: false,
    autoplaySpeed: 4000,
    arrows: true,
    nextArrow: <SampleNextArrow />,
    prevArrow: <SamplePrevArrow />,
    responsive: [
      {
        breakpoint: 1440,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1
        }
      }
    ]
  }
  return (
    <div
      className={cx(styles['wrapper'])}
      style={{
        backgroundImage: `url(${BgAboutUs.src})`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundPosition: 'bottom center'
      }}
    >
      <div className={cx(styles['titleWrapper'], styles['aboutUsTitle'])}>
        {windowWidth && windowWidth <= 576 ? (
          <h3>
            Vấn đề <br /> Phòng mạch đang gặp phải
          </h3>
        ) : (
          <h3>Vấn đề Phòng mạch dang gặp phải</h3>
        )}
      </div>
      <div className={styles['aboutUs']}>
        {windowWidth && windowWidth <= 1024 ? (
          <div className={styles['cards']}>
            <div className={styles['card']}>
              <div className={styles['logo']}>
                <Image src={IconNetwork} alt={''} />
              </div>
              <div className={styles['content']}>
                <div className={styles['label']}>Mạng lưới y tế uy tín</div>
                <div className={styles['paraphrase']}>
                  Kết nối với hơn 300 cơ sở y tế công/tư hàng đầu trên cả nước.
                </div>
              </div>
            </div>
            <div className={styles['card']}>
              <div className={styles['logo']}>
                <Image src={IconPackage} alt={''} />
              </div>
              <div className={styles['content']}>
                <div className={styles['label']}>Gói khám tùy chỉnh</div>
                <div className={styles['paraphrase']}>
                  Phù hợp với từng ngành nghề, nhóm nhân viên và ngân sách doanh
                  nghiệp.
                </div>
              </div>
            </div>
            <div className={styles['card']}>
              <div className={styles['logo']}>
                <Image src={IconExpense} alt={''} />
              </div>
              <div className={styles['content']}>
                <div className={styles['label']}>Chi phí tối ưu</div>
                <div className={styles['paraphrase']}>
                  Chính sách chiết khấu cao, không phát sinh chi phí.
                </div>
              </div>
            </div>
            <div className={styles['card']}>
              <div className={styles['logo']}>
                <Image src={IconService} alt={''} />
              </div>
              <div className={styles['content']}>
                <div className={styles['label']}>Dịch vụ hỗ trợ trọn gói</div>
                <div className={styles['paraphrase']}>
                  Hỗ trợ từ khâu tư vấn, đặt lịch, khám, sau khám và xử lý thủ
                  tục.
                </div>
              </div>
            </div>
          </div>
        ) : (
          <Slider {...settings} className={styles['cards']}>
            <div className={styles['card']}>
              <div className={styles['logo']}>
                <Image src={IconNetwork} alt={''} />
              </div>
              <div className={styles['content']}>
                <div className={styles['label']}>Mạng lưới y tế uy tín</div>
                <div className={styles['paraphrase']}>
                  Kết nối với hơn 300 cơ sở y tế công/tư hàng đầu trên cả nước.
                </div>
              </div>
            </div>
            <div className={styles['card']}>
              <div className={styles['logo']}>
                <Image src={IconPackage} alt={''} />
              </div>
              <div className={styles['content']}>
                <div className={styles['label']}>Gói khám tùy chỉnh</div>
                <div className={styles['paraphrase']}>
                  Phù hợp với từng ngành nghề, nhóm nhân viên và ngân sách doanh
                  nghiệp.
                </div>
              </div>
            </div>
            <div className={styles['card']}>
              <div className={styles['logo']}>
                <Image src={IconExpense} alt={''} />
              </div>
              <div className={styles['content']}>
                <div className={styles['label']}>Chi phí tối ưu</div>
                <div className={styles['paraphrase']}>
                  Chính sách chiết khấu cao, không phát sinh chi phí.
                </div>
              </div>
            </div>
            <div className={styles['card']}>
              <div className={styles['logo']}>
                <Image src={IconService} alt={''} />
              </div>
              <div className={styles['content']}>
                <div className={styles['label']}>Dịch vụ hỗ trợ trọn gói</div>
                <div className={styles['paraphrase']}>
                  Hỗ trợ từ khâu tư vấn, đặt lịch, khám, sau khám và xử lý thủ
                  tục.
                </div>
              </div>
            </div>
          </Slider>
        )}
      </div>
    </div>
  )
}

export default AboutUs
