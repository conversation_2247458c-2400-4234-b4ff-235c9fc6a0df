import styles from './styles.module.less'
import Image from 'next/image'
import Link from 'next/link'

export interface MPCarouselBannerMultiProps {
  title?: string
  isMobile: boolean
}

const MPBannerCollaborationCard = (props: MPCarouselBannerMultiProps) => {
  const { isMobile } = props
  return (
    <div className={styles['CarouselBanner']}>
      {props.title && (
        <div className={styles['headerTitle']}>
          <h2>{props.title}</h2>
        </div>
      )}
      <div className={styles['MPBannerAnKhang']}>
        <Link
          className={styles['item']}
          href={
            'https://www.nhathuocankhang.com/?utm_source=medpro_landingpage&utm_medium=hero_banner&utm_campaign=medpro_ankhang&utm_content=voucher50k#voucher-medpro'
          }
        >
          <a target={'_blank'} rel={'nofollow'}>
            {isMobile ? (
              <Image
                src={
                  'https://cdn.medpro.vn/prod-partner/2018dfdd-f9d2-4a51-8cf7-c6b3d11739a4-banner_home_mobile-_328x164_new.png'
                }
                width={396}
                height={200}
                layout='fixed'
                style={{ borderRadius: 16 }}
                alt={'Banner'}
                quality={100}
              />
            ) : (
              <Image
                src={
                  'https://cdn.medpro.vn/prod-partner/12831fcd-fdbf-4a3d-bf93-8648cb54eda6-banner_home_desktop_-_1180x310_new.png'
                }
                width={1180}
                height={310}
                layout='fixed'
                style={{ borderRadius: 16 }}
                alt={'Banner'}
                quality={100}
              />
            )}
          </a>
        </Link>
      </div>
    </div>
  )
}

export default MPBannerCollaborationCard
