import cx from 'classnames'
import React from 'react'
import BgStatistical from '../../../../../public/images/pricing/bgStatistical.jpg'
import styles from '../styles.module.less'

export const Statistical = () => {
  return (
    <div
      className={styles['statisticalContainer']}
      style={{
        backgroundImage: `url(${BgStatistical.src})`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundPosition: 'center center',
        minHeight: '336px'
      }}
    >
      <div className={styles['statisticalWrapper']}>
        <h2 className={styles['title']}><PERSON>ác con số ấn tượng của chúng tôi</h2>
        <div className={styles['cardWrapper']}>
          <div className={styles['statItem']}>
            <h2>3M+</h2>
            <p>Khách hàng đặt khám</p>
          </div>
          <div className={styles['divider']} />
          <div className={styles['statItem']}>
            <h2>8000+</h2>
            <p>Lượt đặt khám mỗi ngày</p>
          </div>
          <div className={styles['divider']} />
          <div className={styles['statItem']}>
            <h2>850.000+</h2>
            <p>Lượt truy cập hàng tháng</p>
          </div>
          <div className={styles['divider']} />
          <div className={styles['statItem']}>
            <h2>300+</h2>
            <p>Cơ sở y tế liên kết</p>
          </div>
        </div>
      </div>
    </div>
  )
}
