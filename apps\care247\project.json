{"$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/care247", "projectType": "application", "targets": {"build": {"executor": "@nrwl/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/care247", "outputPath": "dist/apps/care247"}, "configurations": {"development": {"outputPath": "apps/care247"}, "production": {}}}, "serve": {"executor": "@nrwl/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "care247:build", "dev": true}, "configurations": {"development": {"buildTarget": "care247:build:development", "dev": true}, "production": {"buildTarget": "care247:build:production", "dev": false}}}, "export": {"executor": "@nrwl/next:export", "options": {"buildTarget": "care247:build:production"}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/care247"], "options": {"jestConfig": "apps/care247/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/care247/**/*.{ts,tsx,js,jsx}"]}}}, "tags": []}