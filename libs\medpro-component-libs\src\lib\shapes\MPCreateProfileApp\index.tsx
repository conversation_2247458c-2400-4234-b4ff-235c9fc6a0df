import {
  ItemRender_patient,
  MPCreateProfileAppCompoment
} from '../../component/MPCreateProfileAppCompoment'
import MPCreateProfileAppCard from '../../ui/MPCreateProfileAppCard'

interface Props {
  province: any[]
  onSubmit: (values: any) => void
  extractInformation: (values: any) => void
  showPopup: boolean
  idForUpdate: string
  partnerId?: string
  data: any
  district: any
  ward: any
  onChangeAddress: (type: string, id: string) => void
}

export const MPCreateProfileApp = ({
  province,
  onSubmit,
  extractInformation,
  showPopup,
  idForUpdate,
  partnerId,
  data,
  district,
  ward,
  onChangeAddress
}: Props) => {
  return (
    <MPCreateProfileAppCompoment
      onSubmit={onSubmit}
      onChangeAddress={onChangeAddress}
      render={({ handleSubmit, handleChangeAddress }: ItemRender_patient) => (
        <MPCreateProfileAppCard
          province={province}
          handleSubmit={handleSubmit}
          extractInformation={extractInformation}
          showPopup={showPopup}
          idForUpdate={idForUpdate}
          partnerId={partnerId}
          data={data}
          district={district}
          ward={ward}
          handleChangeAddress={handleChangeAddress}
        />
      )}
    />
  )
}
