import { Form, Input, Select, Image } from 'antd'
import { FormInstance } from 'antd/es/form/Form'
import React from 'react'
import styles from './styles.module.less'
import { MdOutlineChevronRight } from 'react-icons/md'
import { getReplaceUTF8 } from '@medpro-libs/libs'
import { size } from 'lodash'

const { Option } = Select
interface Bank {
  code: string
  logo: string
  short_name: string
  name: string
}

interface RefundFieldsProps {
  form: FormInstance<any>
  bankList: Bank[]
  isMobile?: boolean
  openBankDrawer?: boolean
  setOpenBankDrawer?: (open: boolean) => void
  selectedBank?: Bank | null
  setSelectedBank?: (bank: Bank | null) => void
}

const handleRequireInput = (label: string, require: boolean) => {
  return (
    <span>
      {label}
      {require && <span className={styles['requireInput']}>*</span>}
    </span>
  )
}

export function renderRefundFields({
  form,
  bankList,
  isMobile = false,
  openBankDrawer,
  setOpenBankDrawer,
  selectedBank,
  setSelectedBank
}: RefundFieldsProps) {
  return [
    {
      enter: () => (
        <Form.Item
          label={handleRequireInput('Mã ngân hàng', true)}
          name='bankCode'
          rules={[{ required: true, message: 'Vui lòng chọn ngân hàng' }]}
          className={isMobile ? styles['formInputItem'] : styles['selectItem']}
        >
          {isMobile ? (
            <Input
              prefix={
                selectedBank && (
                  <Image
                    src={selectedBank?.logo}
                    alt={selectedBank?.name}
                    width={30}
                    height={30}
                    preview={false}
                  />
                )
              }
              placeholder='Chọn ngân hàng'
              onClick={() => setOpenBankDrawer?.(true)}
              readOnly
            />
          ) : (
            <Select
              placeholder='Chọn ngân hàng'
              showSearch
              allowClear
              filterOption={(input, option) =>
                getReplaceUTF8(option!.label as unknown as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            >
              {size(bankList) > 0 &&
                bankList.map((bank) => (
                  <Option
                    key={bank.code}
                    value={bank.code}
                    label={`${bank.name} - ${bank.short_name} - ${bank.code}`}
                  >
                    <div className={styles['selectBankItem']}>
                      <Image
                        src={bank.logo}
                        alt={bank.name}
                        width={35}
                        height={35}
                        preview={false}
                      />
                      <span>{bank.name}</span>
                    </div>
                  </Option>
                ))}
            </Select>
          )}
        </Form.Item>
      ),
      width: 24
    },
    {
      enter: () => (
        <Form.Item
          label=''
          name='bankName'
          className={styles['formInputItem']}
          hidden={true}
        />
      )
    },
    {
      enter: () => (
        <Form.Item
          label={handleRequireInput('Số tài khoản', true)}
          name='accountNumber'
          rules={[{ required: true, message: 'Vui lòng nhập số tài khoản' }]}
          className={styles['formInputItem']}
        >
          <Input placeholder='Nhập số tài khoản' />
        </Form.Item>
      ),
      width: 12
    },
    {
      enter: () => (
        <Form.Item
          label={handleRequireInput('Tên tài khoản', true)}
          name='accountName'
          rules={[{ required: true, message: 'Vui lòng nhập tên tài khoản' }]}
          className={styles['formInputItem']}
        >
          <Input placeholder='Nhập tên tài khoản' />
        </Form.Item>
      ),
      width: 12
    }
  ]
}
