import { MPCompleteProfileApp, MPCreateProfileApp } from '@medpro-libs/libs'
import * as React from 'react'

import { useEffect, useMemo, useState } from 'react'
import { useAppSelector } from '../../../../store/hooks'
import { size } from 'lodash'
import { useDispatch } from 'react-redux'
import {
  fetchCountries,
  fetchDistricts,
  fetchProvinces,
  fetchRelative,
  fetchWards,
  setBreadcrumb
} from '../../../../store/total/slice'
import client from '../../../../config/medproSdk'
import { openNotification } from '../../../../utils/utils.notification'
import { showErrorNotification } from '../../../../utils/utils.error'
import moment from 'moment'
import { useRouter } from 'next/router'
import { patientActions } from '../../../../store/patient/patientSlice'
import _ from 'lodash'
import { useFetchNation, useFetchProfession } from '../../../../hooks/query'
import withAuth from '../../../../layout/Private/withAuth'

export interface ICreateProfileAppProps {}

function CreateProfileApp(props: ICreateProfileAppProps) {
  const router = useRouter()
  const partnerId = router.query.partnerId as string
  const dispatch = useDispatch()
  const [showPopup, setShowPopup] = useState(false)
  const [idForUpdate, setIdForUpdate] = useState('')
  const [phoneLocale, setPhoneLocale] = useState([])
  const [submmitting, setSubmitting] = useState(false)

  const countries = useAppSelector((s) => s.total.countries)
  const district = useAppSelector((s) => s.total.districts)
  const ward = useAppSelector((s) => s.total.wards)
  const province = useAppSelector((s) => s.total.provinces)
  const relative = useAppSelector((s) => s.total.relative)
  const { data: profession } = useFetchProfession()
  const { data: nation } = useFetchNation()
  const schedule: any = useAppSelector((s) => s.booking.schedule)
  const treeId = useAppSelector((s) => s.booking.treeId)

  useEffect(() => {
    if (size(province) === 0) {
      dispatch(fetchProvinces({ country_code: '203' }))
    }
    if (size(countries) === 0) {
      dispatch(fetchCountries({}))
    }
    if (size(relative) === 0) {
      dispatch(fetchRelative())
    }
    fetchPhoneLocale()
    dispatch(setBreadcrumb([]))
  }, [])

  const onCreateProfile = async (values: any) => {
    try {
      setSubmitting(true)
      let params = {}
      const { serviceId, subjectId, doctorId, roomId, date, timeslot } =
        schedule || {}
      const dateStr = `${moment(date).format('YYYY-MM-DD')} ${
        timeslot?.startTime
      }`
      const bookingDate = moment(dateStr, 'YYYY-MM-DD HH:mm').toISOString()
      params = { serviceId, subjectId, doctorId, roomId, bookingDate, treeId }

      if (partnerId !== 'choray') {
        if (values.surname) {
          values = { ...values, name: `${values.surname} ${values.name}` }
          delete values.surname
        }
        const { data } = await client.patient.insertBasicInfo({
          ...values,
          bookingData: { ...params }
        })
        openNotification('success', {
          message: 'Tạo hồ sơ thành công'
        })
        dispatch(
          patientActions.setSelectedPatient({ ...data, isCreated: true })
        )
        dispatch(patientActions.getPatientByUserIdApp())
        if (
          data &&
          data.constraintInfo &&
          data.constraintInfo.isValid === false
        ) {
          setShowPopup(!data.constraintInfo.isValid)
          setIdForUpdate(data.id)
        } else {
          router.push({
            pathname: '/chon-lich-kham',
            query: {
              ...router.query,
              step: 'chon-ho-so'
            }
          })
        }
      } else {
        const { data } = await client.patient.insertPatient({
          ...values,
          bookingData: { ...params }
        })
        openNotification('success', {
          message: 'Tạo hồ sơ thành công'
        })
        dispatch(
          patientActions.setSelectedPatient({ ...data, isCreated: true })
        )
        dispatch(patientActions.getPatientByUserIdApp())
        if (
          data &&
          data.constraintInfo &&
          data.constraintInfo.isValid === false
        ) {
          setShowPopup(!data.constraintInfo.isValid)
          setIdForUpdate(data.id)
        } else {
          router.push({
            pathname: '/chon-lich-kham',
            query: {
              ...router.query,
              step: 'chon-ho-so'
            }
          })
        }
      }
    } catch (err) {
      showErrorNotification(err)
    } finally {
      setSubmitting(false)
    }
  }

  const onChangeAddress = (type: string, id: string) => {
    console.log('onChangeAddress', type, id)
    switch (type) {
      case 'district':
        dispatch(fetchDistricts({ city_id: id }))
        break
      case 'ward':
        dispatch(fetchWards({ district_id: id }))
        break
      default:
        break
    }
  }

  const fetchPhoneLocale = async () => {
    try {
      const { data } = await client.patient.getPhoneLocale()
      if (_.isArray(data)) {
        setPhoneLocale(data)
      }
    } catch (err) {
      console.log(err)
    }
  }

  const dataCreate = useMemo(() => {
    const phonePrefix = '+84'
    const iso = 'vi-VN'

    return {
      profession,
      country: [],
      nation,
      province,
      patient: {
        prefix: phonePrefix,
        iso,
        relativeMobileLocaleIso: iso
      },
      countries,
      relative: relative?.map((item) => {
        return {
          ...item,
          value: item.id,
          title: item.name
        }
      })
    }
  }, [profession, nation, province, countries, relative])

  const convertGenderCCCD = (gender: any, birthYear: any) => {
    if (birthYear > 1900 && birthYear < 2000) {
      return gender === 0 ? 1 : 0
    } else if (birthYear >= 2000 && birthYear < 2100) {
      return gender === 2 ? 1 : 0
    } else if (birthYear > 2200 && birthYear < 2300) {
      return gender === 4 ? 1 : 0
    } else if (birthYear > 2300 && birthYear < 2400) {
      return gender === 6 ? 1 : 0
    } else if (birthYear > 2400 && birthYear < 2500) {
      return gender === 8 ? 1 : 0
    }
  }

  const extractInformation = async (str: any) => {
    try {
      let name, address, birthDate, insuranceId, sex, identifyId
      if (!str) {
        return null
      }
      const arrayinfoqr = str.split('|')
      if (!arrayinfoqr.length) {
        return null
      }
      if (arrayinfoqr[0].length === 12) {
        name = arrayinfoqr[2]
        address = arrayinfoqr[5]
        identifyId = arrayinfoqr[0]
        const birthDateFormat = moment(arrayinfoqr[3], 'DDMMYYYY')
        birthDate = birthDateFormat.format('DD/MM/YYYY')
        sex = `${convertGenderCCCD(
          Number(identifyId[3]),
          birthDateFormat.year()
        )}`
        insuranceId = ''
      } else {
        name = decodeURIComponent(
          arrayinfoqr[1].replace(/\s+/g, '').replace(/[0-9a-f]{2}/g, '%$&')
        )
        address = decodeURIComponent(
          arrayinfoqr[4].replace(/\s+/g, '').replace(/[0-9a-f]{2}/g, '%$&')
        )
        birthDate = arrayinfoqr[2]
        insuranceId = arrayinfoqr[0]
        sex = arrayinfoqr[3] - 1 === 1 ? 0 : 1
      }
      const response = await client.patient.parseAddress({ address })
      const data = response?.data
      return {
        sex,
        name: name.split(' ')[name.split(' ').length - 1],
        surname: name
          .split(' ')
          .splice(0, name.split(' ').length - 1)
          .toString()
          .replace(/,/g, ' '),
        fullname:
          name
            .split(' ')
            .splice(0, name.split(' ').length - 1)
            .toString()
            .replace(/,/g, ' ') +
          ' ' +
          name.split(' ')[name.split(' ').length - 1],
        birthdate: moment(birthDate, 'DD/MM/YYYY').isValid()
          ? birthDate
          : '01/01/' + birthDate,
        country_code: 'VIE',
        country: 'Việt Nam',
        city_id: data?.cityId,
        city: data?.cityId
          ? address.split(', ').length > 2
            ? address.split(', ')[address.split(', ').length - 1].split('_')[0]
            : ''
          : undefined,
        district_id: data?.districtId,
        district: data?.districtId
          ? address.split(', ').length > 2
            ? address.split(', ')[address.split(', ').length - 2]
            : ''
          : undefined,
        ward_id: data?.wardId,
        ward: data?.wardId
          ? address.split(', ').length > 2
            ? address.split(', ')[address.split(', ').length - 3]
            : ''
          : undefined,
        address: data.address || '',
        insuranceId: insuranceId.toUpperCase(),
        cmnd: identifyId
      }
    } catch (error) {
      console.log('@@@extractInformation:', error)
      return null
    }
  }

  return (
    <>
      {partnerId === 'choray' ? (
        <MPCompleteProfileApp
          onSubmit={onCreateProfile}
          extractInformation={extractInformation}
          data={dataCreate}
          onChangeAddress={onChangeAddress}
          phoneLocale={phoneLocale}
          district={district}
          ward={ward}
          partnerId={partnerId}
          submitting={submmitting}
        />
      ) : (
        <MPCreateProfileApp
          province={province}
          extractInformation={extractInformation}
          onSubmit={onCreateProfile}
          showPopup={showPopup}
          partnerId={partnerId}
          idForUpdate={idForUpdate}
          data={dataCreate}
          district={district}
          ward={ward}
          onChangeAddress={onChangeAddress}
        />
      )}
    </>
  )
}
export default withAuth()(CreateProfileApp)
