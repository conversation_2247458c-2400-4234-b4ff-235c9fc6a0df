import { keyMemory } from '../utils.contants'
import * as cookie from 'cookie'
import { getCookie, deleteCookie, setCookie } from 'cookies-next'
import { OptionsType } from 'cookies-next/lib/types'

export const serializeCookies = ({
  ctx,
  key,
  value
}: {
  ctx: any
  key: any
  value: any
}) => {
  const _value = typeof value === 'object' ? JSON.stringify(value) : value
  ctx?.res && ctx?.res?.setHeader('Set-Cookie', cookie.serialize(key, _value))
}

export const parseCookies = ({ ctx, key }: { ctx: any; key: string }) => {
  const cook = ctx?.req ? ctx.req.headers.cookie || '' : document.cookie
  const dataCookie = cookie.parse(cook)

  if (dataCookie[key]) {
    if (JSON.parse(dataCookie[key])) {
      return JSON.parse(dataCookie[key])
    } else {
      return dataCookie[key]
    }
  }

  return null
}

export type KeyMemory = keyof typeof keyMemory

export const getKeyCookie = (key: KeyMemory, options?: OptionsType) => {
  const _key = keyMemory[key]
  return getCookie(_key, options)
}

export const setKeyCookie = (
  key: KeyMemory,
  value: string,
  options?: OptionsType
) => {
  setCookie(keyMemory[key], value, {
    ...options,
    secure: true,
    sameSite: 'strict',
  } as any)
}

export const deleteKeyCookie = (key: KeyMemory, options?: OptionsType) => {
  deleteCookie(keyMemory[key], options)
}
