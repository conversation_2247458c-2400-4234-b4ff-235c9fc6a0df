import { Carousel, CarouselProps, Skeleton } from 'antd'
import cx from 'classnames'
import { range, size } from 'lodash'
import Image from 'next/image'
import { RiArrowLeftSLine, RiArrowRightSLine } from 'react-icons/ri'
import { HiCheckBadge } from 'react-icons/hi2'
import IconDirectionLeft from './images/iconDirectionLeft.png'
import Link from 'next/link'
import styles from './styles.module.less'
import { FormattedText } from '../../common/func'

interface IF_CooperatedItem {
  partnerId?: string
  name?: string
  image?: string
  circleLogo?: string
  id?: string
  _id?: string
  slug?: string
  cta?: any
  listingPackagePaid?: boolean
  isViewAll?: boolean
}

const limmit = 6

export interface MPCooperatedCardProps {
  data: IF_CooperatedItem[]
  loading?: boolean
  isMobile: boolean
  hidden?: boolean
}

const MPCooperatedCard = (props: MPCooperatedCardProps) => {
  const { data: dataCooperate, loading, isMobile } = props
  const data = [
    ...(dataCooperate || []),
    { _id: 'viewall', isViewAll: true, name: '<PERSON>em tất cả' }
  ]
  const SampleNextArrow = (props_next: any) => {
    const { className, style, onClick, currentSlide } = props_next
    if (size(data) <= limmit) {
      return null
    } else if (currentSlide === size(data) - limmit) return null
    return (
      <div
        className={cx(className, styles['btnNext'])}
        style={{ ...style }}
        onClick={onClick}
      >
        <RiArrowRightSLine
          size={20}
          color='#003553'
          aria-labelledby='Next Icon'
        />
      </div>
    )
  }

  const SamplePrevArrow = (props_prev: any) => {
    const { className, style, onClick, currentSlide } = props_prev
    if (size(data) <= limmit) {
      return null
    } else if (currentSlide === 0) return null
    return (
      <div
        className={cx(className, styles['btnPrev'])}
        style={{ ...style }}
        onClick={onClick}
      >
        <RiArrowLeftSLine
          size={20}
          color='#003553'
          aria-labelledby='Previous Icon'
        />
      </div>
    )
  }

  const settings: CarouselProps = {
    slidesToShow: limmit,
    slidesToScroll: 1,
    dots: false,
    infinite: false,
    arrows: true,
    nextArrow: <SampleNextArrow />,
    prevArrow: <SamplePrevArrow />
  }

  if (props?.hidden) {
    return <></>
  }

  return (
    <div className={styles['Cooperated']}>
      <div className={styles['Cooperated__headline']}>
        <h2 className={styles['headline__title']}>
          Được tin tưởng hợp tác và đồng hành
        </h2>
      </div>
      {loading ? (
        <div className={styles['Hospitalseleketon']}>
          {range(isMobile ? 3 : 7).map((_, index) => {
            return (
              <div key={index} className={styles['item_Hos']}>
                <Skeleton.Avatar
                  active
                  shape='square'
                  style={{
                    width: !isMobile ? '64px' : '50px',
                    height: !isMobile ? '64px' : '50px'
                  }}
                />
                <Skeleton
                  paragraph={{ rows: 2, width: ['100%', '70%'] }}
                  title={false}
                  style={{ marginTop: '8px' }}
                />
              </div>
            )
          })}
        </div>
      ) : (
        <div className={styles['Cooperated__content']}>
          {isMobile ? (
            <div className={styles['content__scroll']}>
              {data?.map((item: IF_CooperatedItem) => {
                if (item._id === 'viewall') {
                  return (
                    <Link
                      key={item._id}
                      className={styles['content__carousel__item']}
                      href={`/co-so-y-te`}
                    >
                      <div className={styles['content__carousel__item--div']}>
                        <div
                          className={
                            styles['content__carousel__item--viewall-img']
                          }
                        >
                          <Image
                            src={IconDirectionLeft}
                            width={30}
                            height={30}
                            layout='fixed'
                            alt={item?.name}
                            priority
                          />
                        </div>
                        <div
                          className={styles['content__carousel__item--viewall']}
                        >
                          Xem tất cả
                        </div>
                      </div>
                    </Link>
                  )
                } else {
                  return (
                    <Link
                      key={item._id}
                      className={styles['content__carousel__item']}
                      href={item?.cta?.url ? item?.cta?.url : '#'}
                    >
                      <div className={styles['content__carousel__item--div']}>
                        <div
                          className={styles['content__carousel__item--image']}
                        >
                          <Image
                            src={item?.circleLogo || ''}
                            width={64}
                            height={64}
                            layout='fixed'
                            alt={item?.name}
                            priority
                          />
                        </div>
                        <h3
                          className={cx(
                            styles['content__carousel__item--name'],
                            item?.listingPackagePaid &&
                              styles['content__carousel__item--name--paid']
                          )}
                        >
                          {FormattedText(item?.name || '')}
                          {item?.listingPackagePaid && (
                            <HiCheckBadge color='#0097FF' size={16} />
                          )}
                        </h3>
                      </div>
                    </Link>
                  )
                }
              })}
            </div>
          ) : (
            <Carousel {...settings} className={styles['content__carousel']}>
              {data?.map((item: IF_CooperatedItem) => {
                if (item._id === 'viewall') {
                  return (
                    <Link
                      key={item._id}
                      className={styles['content__carousel__item']}
                      href={`/co-so-y-te`}
                    >
                      <div className={styles['content__carousel__item--div']}>
                        <div
                          className={
                            styles['content__carousel__item--viewall-img']
                          }
                        >
                          <Image
                            src={IconDirectionLeft}
                            width={30}
                            height={30}
                            layout='fixed'
                            alt={item?.name}
                            priority
                          />
                        </div>
                        <div
                          className={styles['content__carousel__item--viewall']}
                        >
                          Xem tất cả
                        </div>
                      </div>
                    </Link>
                  )
                } else {
                  return (
                    <Link
                      key={item._id}
                      className={styles['content__carousel__item']}
                      href={item?.cta?.url ? item?.cta?.url : '#'}
                    >
                      <div className={styles['content__carousel__item--div']}>
                        <div
                          className={styles['content__carousel__item--image']}
                        >
                          <Image
                            src={item?.circleLogo || ''}
                            width={64}
                            height={64}
                            layout='fixed'
                            alt={item?.name}
                            priority
                          />
                        </div>
                        <h3
                          className={cx(
                            styles['content__carousel__item--name'],
                            item?.listingPackagePaid &&
                              styles['content__carousel__item--name--paid']
                          )}
                        >
                          {item?.name}
                          {item?.listingPackagePaid && (
                            <HiCheckBadge color='#0097FF' size={16} />
                          )}
                        </h3>
                      </div>
                    </Link>
                  )
                }
              })}
            </Carousel>
          )}
        </div>
      )}
    </div>
  )
}
export default MPCooperatedCard
