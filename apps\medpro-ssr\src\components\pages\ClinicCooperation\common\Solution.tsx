import React from 'react'
import cx from 'classnames'
import Image from 'next/image'
import IconCheckVerified from '../images/iconCheckVerified.svg'
import Img01 from '../images/img01.png'
import Img02 from '../images/img02.png'
import IconRightArrow from '../images/iconRightArrow.png'
import Img03 from '../images/img03.png'
import Img04 from '../images/img04.png'
import styles from '../styles.module.less'

const Solution = (props: any) => {
  const { scrollToSection, windowWidth } = props
  return (
    <div className={cx(styles['wrapper'])}>
      <div className={styles['solution']}>
        <div className={styles['titleWrapper']}>
          {windowWidth && windowWidth <= 576 ? (
            <h3>
              Gi<PERSON>i pháp toàn diện cho khám sức khỏe <br /> doanh nghiệp
            </h3>
          ) : (
            <h3>
              <PERSON><PERSON><PERSON><PERSON> pháp toàn diện cho <br /> kh<PERSON>m sức khỏe doanh nghiệp
            </h3>
          )}
        </div>
        <div className={cx(styles['steps'])}>
          <div className={cx(styles['step'])}>
            <div className={styles['textWrapper']}>
              <h3 className={styles['title']}>
                <div className={cx(styles['number'], styles['numberDesk'])}>
                  1
                </div>
                <div className={styles['titleText']}>
                  <div className={cx(styles['number'], styles['numberMobi'])}>
                    1
                  </div>{' '}
                  Tùy chọn gói khám phù hợp
                </div>
              </h3>
              <div className={styles['itemList']}>
                <div className={styles['item']}>
                  <div className={styles['icon']}>
                    <Image src={IconCheckVerified} alt={''} />
                  </div>
                  <div className={styles['text']}>
                    Hỗ trợ doanh nghiệp tư vấn gói khám phù hợp theo quy định
                    thông tư, nhóm ngành nghề, bệnh nghề nghiệp.
                  </div>
                </div>
                <div className={styles['item']}>
                  <div className={styles['icon']}>
                    <Image src={IconCheckVerified} alt={''} />
                  </div>
                  <div className={styles['text']}>
                    Tuỳ chỉnh cho nhân viên văn phòng, cấp quản lý, lao động
                    trong môi trường đặc thù.
                  </div>
                </div>
                <div className={styles['item']}>
                  <div className={styles['icon']}>
                    <Image src={IconCheckVerified} alt={''} />
                  </div>
                  <div className={styles['text']}>
                    Linh hoạt về thời gian, hỗ trợ lấy mẫu tại doanh nghiệp, từ
                    cơ bản đến nâng cao theo nhu cầu và ngân sách.
                  </div>
                </div>
              </div>
            </div>
            <div className={styles['imgWrapper']}>
              <Image src={Img01} alt={''} />
            </div>
          </div>
          <div className={cx(styles['step'], styles['textRightToLeft'])}>
            <div className={styles['textWrapper']}>
              <h3 className={styles['title']}>
                <div className={cx(styles['number'], styles['numberDesk'])}>
                  2
                </div>
                <div className={styles['titleText']}>
                  <div className={cx(styles['number'], styles['numberMobi'])}>
                    2
                  </div>{' '}
                  Linh hoạt lựa chọn cơ sở y tế
                </div>
              </h3>
              <div className={styles['itemList']}>
                <div className={styles['item']}>
                  <div className={styles['icon']}>
                    <Image src={IconCheckVerified} alt={''} />
                  </div>
                  <div className={styles['text']}>
                    Tư vấn trung lập, giúp doanh nghiệp lựa chọn cơ sở y tế có
                    chuyên môn, trang thiết bị hiện đại phù hợp nhất với từng
                    doanh nghiệp/nhóm nhân viên khác nhau.
                  </div>
                </div>
                <div className={styles['item']}>
                  <div className={styles['icon']}>
                    <Image src={IconCheckVerified} alt={''} />
                  </div>
                  <div className={styles['text']}>
                    Doanh nghiệp có thể lựa chọn được một hay nhiều cơ sở y tế
                    theo nhu cầu của từng nhóm nhân viên, khu vực khác nhau từ
                    Nam tới Bắc trong cùng một đợt khám.
                  </div>
                </div>
              </div>
            </div>
            <div className={styles['imgWrapper']}>
              <Image src={Img02} alt={''} />
            </div>
          </div>
          <div className={cx(styles['step'])}>
            <div className={cx(styles['textWrapper'])}>
              <div className={styles['lineTop']}></div>
              <h3 className={styles['title']}>
                <div className={cx(styles['number'], styles['numberDesk'])}>
                  3
                </div>
                <div className={styles['titleText']}>
                  <div className={cx(styles['number'], styles['numberMobi'])}>
                    3
                  </div>{' '}
                  Chi phí hợp lý, chiết khấu cao
                </div>
              </h3>
              <div className={styles['itemList']}>
                <div className={styles['item']}>
                  <div className={styles['icon']}>
                    <Image src={IconCheckVerified} alt={''} />
                  </div>
                  <div className={styles['text']}>
                    Áp dụng mức chiết khấu đặc biệt cho doanh nghiệp khi đặt
                    lịch qua Medpro.
                  </div>
                </div>
                <div className={styles['item']}>
                  <div className={styles['icon']}>
                    <Image src={IconCheckVerified} alt={''} />
                  </div>
                  <div className={styles['text']}>
                    Linh hoạt hình thức hợp đồng, doanh nghiệp có thể ký trực
                    tiếp với cơ sở y tế hoặc qua Medpro.
                  </div>
                </div>
                <div className={styles['item']}>
                  <div
                    className={styles['btnQuote']}
                    onClick={() => scrollToSection('nhan-tu-van-bao-gia')}
                  >
                    <span>Nhận báo giá</span>
                    <div className={styles['btnIcon']}>
                      <Image src={IconRightArrow} alt={''} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className={styles['imgWrapper']}>
              <Image src={Img03} alt={''} />
            </div>
          </div>
          <div className={cx(styles['step'], styles['textRightToLeft'])}>
            <div className={styles['textWrapper']}>
              <h3 className={styles['title']}>
                <div className={cx(styles['number'], styles['numberDesk'])}>
                  4
                </div>
                <div className={styles['titleText']}>
                  <div className={cx(styles['number'], styles['numberMobi'])}>
                    4
                  </div>
                  {
                    <div>
                      Hỗ trợ toàn diện{' '}
                      {windowWidth && windowWidth <= 576 ? <br /> : null}
                      <span style={{ color: '#01B5F1' }}> Trước</span>,{' '}
                      <span style={{ color: '#01B5F1' }}>Trong</span> và
                      <span style={{ color: '#01B5F1' }}> Sau</span> khám
                    </div>
                  }
                </div>
              </h3>
              <div className={styles['itemList']}>
                <div className={styles['item']}>
                  <div className={styles['icon']}>
                    <Image src={IconCheckVerified} alt={''} />
                  </div>
                  <div className={styles['text']}>
                    Tư vấn gói khám, cơ sở khám, xử lý hợp đồng và thủ tục trước
                    khám.
                  </div>
                </div>
                <div className={styles['item']}>
                  <div className={styles['icon']}>
                    <Image src={IconCheckVerified} alt={''} />
                  </div>
                  <div className={styles['text']}>
                    Đặt lịch ưu tiên với các cơ sở y tế/bác sĩ chuyên trị tốt
                    nhất đối với các cá nhân có bệnh lý cụ thể.
                  </div>
                </div>
                <div className={styles['item']}>
                  <div className={styles['icon']}>
                    <Image src={IconCheckVerified} alt={''} />
                  </div>
                  <div className={styles['text']}>
                    Nhân viên Medpro hỗ trợ trực tiếp trong suốt quá trình khám,
                    đặc biệt tại cơ sở y tế công.
                  </div>
                </div>
                <div className={styles['item']}>
                  <div className={styles['icon']}>
                    <Image src={IconCheckVerified} alt={''} />
                  </div>
                  <div className={styles['text']}>
                    Tùy chọn bác sĩ riêng theo từng ngành đặc thù của doanh
                    nghiệp.
                  </div>
                </div>
                <div className={styles['item']}>
                  <div className={styles['icon']}>
                    <Image src={IconCheckVerified} alt={''} />
                  </div>
                  <div className={styles['text']}>
                    Hỗ trợ khám tại chỗ hoặc tư vấn qua video với bác sĩ chuyên
                    khoa.
                  </div>
                </div>
              </div>
            </div>
            <div className={styles['imgWrapper']}>
              <Image src={Img04} alt={''} />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Solution
