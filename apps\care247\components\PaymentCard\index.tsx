import React from 'react'
import styles from './styles.module.less'
import QRCodeGenerator from '../QRCodeGenerator'

interface PaymentData {
  orderCode: string
  customerName: string
  amount: number
  service: string
  bankAccount: string
  bankName: string
  transferCode: string
}

interface PaymentCardProps {
  paymentData: PaymentData
}

const PaymentCard: React.FC<PaymentCardProps> = ({ paymentData }) => {
  const {
    orderCode,
    customerName,
    amount,
    service,
    bankAccount,
    bankName,
    transferCode
  } = paymentData

  return (
    <div className={styles.paymentCard}>
      {/* Payment Tabs */}
      <div className={styles.paymentTabs}>
        <div className={`${styles.tab} ${styles.activeTab}`}>
          <span className={styles.tabIcon}>🏦</span>
          <span>THANH TOÁN</span>
        </div>
        <div className={styles.tab}>
          <span className={styles.tabIcon}>🏛️</span>
          <span>TOÀN NGÂN HÀNG</span>
        </div>
      </div>

      {/* Bank Info */}
      <div className={styles.bankInfo}>
        <h3>THÔNG TIN ĐƠN HÀNG</h3>
        <div className={styles.orderInfo}>
          <div className={styles.infoRow}>
            <span>Mã đơn hàng</span>
            <span>{orderCode}</span>
          </div>
          <div className={styles.infoRow}>
            <span>Tên khách hàng</span>
            <span>{customerName}</span>
          </div>
          <div className={styles.infoRow}>
            <span>Số tiền thanh toán</span>
            <span>{amount.toLocaleString()}₫</span>
          </div>
          <div className={styles.infoRow}>
            <span>Tên dịch vụ</span>
            <span>{service}</span>
          </div>
        </div>

        <h3>THÔNG TIN CHUYỂN KHOẢN</h3>
        <div className={styles.bankDetails}>
          <div className={styles.infoRow}>
            <span>Số tài khoản</span>
            <span>{bankAccount}</span>
          </div>
          <div className={styles.infoRow}>
            <span>Ngân hàng</span>
            <span>{bankName}</span>
          </div>
          <div className={styles.infoRow}>
            <span>Nội dung</span>
            <span>{transferCode}</span>
          </div>
        </div>

        <div className={styles.note}>
          <span>Lưu ý:</span>
          <span>Vui lòng ghi đúng nội dung chuyển khoản để đơn hàng được xử lý nhanh chóng</span>
        </div>
      </div>

      {/* QR Code */}
      <div className={styles.qrSection}>
        <h3>Quét mã bằng ứng dụng Ngân hàng/ Ví điện tử</h3>
        <div className={styles.qrCode}>
          <div className={styles.qrPlaceholder}>
            <QRCodeGenerator 
              value={`${bankAccount}|${amount}|${transferCode}|${bankName}`}
              size={160}
              className={styles.qrCodeImage}
            />
          </div>
          <div className={styles.vietorLogo}>VIETOR</div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className={styles.actionButtons}>
        <button className={styles.downloadBtn}>
          📥 Tải ảnh
        </button>
        <button className={styles.helpBtn}>
          ❓ Hỗ trợ giao dịch
        </button>
      </div>
    </div>
  )
}

export default PaymentCard
