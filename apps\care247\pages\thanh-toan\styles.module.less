.paymentPage {
  min-height: 100vh;
  background-image: url('/images/background.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;

  .mainContent {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    gap: 24px;

    .leftPanel {
      flex: 1;

      .title {
        font-size: 28px;
        font-weight: bold;
        color: #333;
        margin-bottom: 24px;
        line-height: 1.4;
      }

      .infoCard {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

        .infoHeader {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          margin-bottom: 24px;
          font-size: 14px;
          color: #666;

          .infoIcon {
            font-size: 20px;
            margin-top: -2px;
          }
        }

        .patientImage {
          margin-bottom: 24px;

          img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
          }
        }

        .doctorInfo {
          display: flex;
          align-items: center;
          gap: 12px;
          background: #f0f8ff;
          padding: 16px;
          border-radius: 8px;

          .doctorAvatar {
            font-size: 32px;
          }

          .doctorText {
            display: flex;
            flex-direction: column;
            font-size: 14px;
            color: #333;
          }
        }
      }
    }

    // Đảm bảo các column có chiều cao bằng nhau với Ant Design
    :global(.ant-row) {
      height: 100%;
    }

    :global(.ant-col) {
      display: flex;
      flex-direction: column;

      > * {
        flex: 1;
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .header {
      padding: 12px 16px;

      .logo .logoText {
        font-size: 20px;
      }

      .timer .timerValue {
        font-size: 16px;
      }
    }

    .mainContent {
      flex-direction: column;
      padding: 16px;
      gap: 16px;

      .leftPanel .title {
        font-size: 24px;
      }

      .rightPanel {
        max-width: none;

        .paymentCard {
          .bankInfo {
            padding: 16px;
          }

          .qrSection {
            padding: 0 16px 16px;

            .qrCode .qrPlaceholder {
              width: 160px;
              height: 160px;

              .qrCodeImage {
                width: 120px;
                height: 120px;
              }
            }
          }

          .actionButtons {
            flex-direction: column;
            padding: 0 16px 16px;

            button {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
