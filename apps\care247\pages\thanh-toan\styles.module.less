.paymentPage {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  .container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .paymentSelection {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .proceedButton {
      text-align: center;
      margin-top: 24px;

      .proceedBtn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 16px 32px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 200px;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
      }
    }
  }

  .leftColumn {
    background: white;
    border-radius: 16px;
    padding: 24px;
    height: fit-content;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .logo {
      text-align: center;
      margin-bottom: 24px;

      img {
        width: 120px;
        height: auto;
        margin-bottom: 12px;
      }

      h2 {
        font-size: 14px;
        color: #666;
        font-weight: 400;
        line-height: 1.4;
        margin: 0;
      }
    }

    .patientInfo {
      margin-bottom: 24px;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 16px;
        border-bottom: 2px solid #f0f0f0;
        padding-bottom: 8px;
      }

      .infoItem {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        padding: 8px 0;
        border-bottom: 1px solid #f5f5f5;

        .label {
          font-weight: 500;
          color: #666;
          flex: 1;
        }

        .value {
          font-weight: 600;
          color: #333;
          text-align: right;
          flex: 1;
        }
      }
    }

    .imageSection {
      text-align: center;

      img {
        width: 100%;
        max-width: 200px;
        height: auto;
        border-radius: 12px;
        margin-bottom: 16px;
      }

      .imageText {
        p {
          font-size: 12px;
          color: #666;
          line-height: 1.4;
          margin: 8px 0;
        }
      }
    }
  }

  .rightColumn {
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .paymentHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 2px solid #f0f0f0;

      h2 {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin: 0;
      }

      .statusBadges {
        display: flex;
        align-items: center;
        gap: 12px;

        .badge {
          background: #e8f4fd;
          color: #1890ff;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;
        }

        .timer {
          background: #fff2e8;
          color: #fa8c16;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
        }
      }
    }

    .paymentInfo {
      margin-bottom: 24px;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 16px;
        text-transform: uppercase;
      }

      .orderDetails {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 16px;

        .orderItem {
          display: flex;
          justify-content: space-between;
          margin-bottom: 12px;
          padding: 8px 0;

          &:last-child {
            margin-bottom: 0;
          }

          span:first-child {
            color: #666;
            font-weight: 500;
          }

          span:last-child {
            color: #333;
            font-weight: 600;
          }
        }
      }
    }

    .paymentMethods {
      margin-bottom: 24px;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 16px;
        text-transform: uppercase;
      }

      .transferInfo {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 16px;

        .bankInfo {
          display: flex;
          justify-content: space-between;
          margin-bottom: 12px;
          padding: 8px 0;

          &:last-child {
            margin-bottom: 0;
          }

          span:first-child {
            color: #666;
            font-weight: 500;
            flex: 1;
          }

          span:last-child {
            color: #333;
            font-weight: 600;
            text-align: right;
            flex: 1;
          }
        }
      }
    }

    .qrSection {
      margin-bottom: 24px;
      text-align: center;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
      }

      .qrCodeContainer {
        display: flex;
        flex-direction: column;
        align-items: center;
        background: #f8f9fa;
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 16px;

        .qrCode {
          width: 200px;
          height: 200px;
          background: white;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 16px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

          .qrPlaceholder {
            width: 180px;
            height: 180px;
            background: linear-gradient(45deg, #000 25%, transparent 25%),
                        linear-gradient(-45deg, #000 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, #000 75%),
                        linear-gradient(-45deg, transparent 75%, #000 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            border-radius: 8px;
            position: relative;

            .qrPattern {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 40px;
              height: 40px;
              background: white;
              border: 3px solid #000;
              border-radius: 4px;
            }
          }
        }

        .vietqrLogo {
          img {
            height: 40px;
            width: auto;
          }
        }
      }

      p {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }
    }

    .actionButtons {
      display: flex;
      gap: 16px;
      justify-content: center;

      .cancelBtn {
        background: white;
        border: 2px solid #ff4d4f;
        color: #ff4d4f;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: #ff4d4f;
          color: white;
        }
      }

      .confirmBtn {
        background: #52c41a;
        border: 2px solid #52c41a;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: #389e0d;
          border-color: #389e0d;
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 12px;

    .leftColumn,
    .rightColumn {
      padding: 16px;
    }

    .paymentHeader {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      h2 {
        font-size: 20px;
      }
    }

    .qrSection {
      .qrCodeContainer {
        padding: 16px;

        .qrCode {
          width: 160px;
          height: 160px;

          .qrPlaceholder {
            width: 140px;
            height: 140px;
          }
        }
      }
    }

    .actionButtons {
      flex-direction: column;

      .cancelBtn,
      .confirmBtn {
        width: 100%;
      }
    }
  }
}
