import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MPNew<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MPNewHospitalContact,
  PageRoutesV2,
  useWindowResize
} from '@medpro-libs/libs'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { size } from 'lodash'
import { GetStaticPropsContext } from 'next'
import { useRouter } from 'next/router'
import React, { useCallback, useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import client from '../../config/medproSdk'
import { PageProps } from '../../src/type'
import { bookingActions } from '../../store/booking/slice'
import { useAppSelector } from '../../store/hooks'
import { hospitalActions } from '../../store/hospital/hospitalSlice'
import { setBreadcrumb, totalDataActions } from '../../store/total/slice'
import { executeRecaptcha } from '../../utils/reCapcha/reCapcha'
import {
  LIST_TYPE_PARTNER,
  SSG_REVALIDATE_SECOND,
  TYPE_HOSPITAL
} from '../../utils/utils.contants'
import {
  openNotification,
  showError,
  showMessage
} from '../../utils/utils.notification'
// import { fetchHospitalsV6Server } from '../../utils/utils.query-server'
import DefautDrawer from '../../src/page-content/bookingApp/DefautDrawer'
import Radar from 'radar-sdk-js'
import { currentEnv } from '../../config/envs'
import { locationActions } from '../../store/location/locationSlice'
import styles from './styles.module.less'

export interface HospitalsProps extends PageProps {
  hospitals: any[]
  type: string
}

const Hospitals = (props: HospitalsProps) => {
  const { partnerInfoApp: appInfo } = props
  const router = useRouter()
  const dispatch = useDispatch()
  const provinces = useAppSelector((s) => s.total.provinces)
  const locationData = useAppSelector((s) => s.location.data)
  const isMobile = useWindowResize(576)
  const [openFeature, setOpenFeature] = useState<any>({ item: {}, open: false })
  const [features, setFeatures] = useState<any>([])
  const [hospitals, setHospitals] = useState<any>([])
  const fetchHospitals = useCallback(async (filter?: any) => {
    try {
      const { data } = await client.partner.getHospitalListByAppIdV6({
        latitude: filter?.latitude,
        longitude: filter?.longitude
      })
      const sortDistance = data.sort((a: any, b: any) => parseFloat(a.distance) - parseFloat(b?.distance))
      setHospitals(sortDistance)
    } catch (error: any) {
      showError(error)
    }
  }, [locationData])
  const fetchData = async () => {
    try {
      const { data } = await client.partner.getPartnerInfo({
        partnerid: openFeature.item.partnerId as any
      })
      return data
    } catch (error) {
      console.error('Error fetching data:', error)
      throw error // You might want to handle or log the error appropriately
    }
  }
  useEffect(() => {
    if (size(provinces) === 0) {
      dispatch(totalDataActions.fetchProvinces({ country_code: '203' }))
    }
  }, [provinces])
  useEffect(() => {
    if (size(openFeature?.item) > 0) {
      const loadData = async () => {
        try {
          const result = await fetchData()
          setFeatures(result.features?.filter((f) => f.status))
        } catch (error) {
          console.log(error)
        }
      }
      loadData()
    }
  }, [openFeature])
  useEffect(() => {
    const query = router.query?.type as string
    if (query) {
      const breadcrumb = LIST_TYPE_PARTNER.find(
        (item) => item.key === query
      ).title
      dispatch(setBreadcrumb([{ title: breadcrumb }]))
    } else {
      dispatch(setBreadcrumb([{ title: 'Cơ sở y tế' }]))
    }
  }, [router])
  useEffect(() => {
    const latitude = locationData?.latitude
    const longitude = locationData?.longitude
    void fetchHospitals({latitude, longitude})
  }, [fetchHospitals, locationData])
  const onSelectHospital = (item, setHospitalDescription) => {
    if (item?.slug) {
      client.partner
        .getHospitalDescription(item.slug)
        .then((res) => {
          setHospitalDescription(res.data)
        })
        .catch((err) => {
          console.error('err getHospitalDescription ', err)
        })
    }
  }
  const onBooking = (item: any, setMessageHospital: any) => {
    if (item.message) {
      setMessageHospital(item.message)
    } else {
      dispatch(hospitalActions.setPartnerId(item.partnerId))
      dispatch(bookingActions.resetBookingFlow({}))
      if (!isMobile) {
        router.push(
          PageRoutesV2.bookingType({
            appPartnerSlug: item.slug,
            query: {
              partnerId: item.partnerId,
              behavior: router.query?.behavior
            }
          })
        )
      } else {
        setOpenFeature({ item, open: true })
      }
    }
  }
  const handleContactUsToken = async (token: string, values: any) => {
    try {
      if (token) {
        const req = {
          email: values.email,
          note: values.note,
          fullname: values.fullname,
          phone: values.phone,
          type: 'contact',
          captchaResponse: token // Gửi token reCAPTCHA cùng với yêu cầu
        }
        await client.user.submitContactUs(req)
        showMessage(
          'Gửi thông tin thành công. Medpro sẽ liên hệ bạn sớm. Xin cảm ơn.'
        )
      } else {
        throw new Error('error reCapcha')
      }
    } catch (err) {
      showError(err)
    }
  }
  const onSubmitContactUs = async (values: any) => {
    try {
      await executeRecaptcha((token) => handleContactUsToken(token, values))
    } catch (err) {
      showError(err)
    }
  }
  const onFinish = async (item: any) => {
    client.setPartner(openFeature.item.partnerId)
    await dispatch(hospitalActions.setPartnerId(openFeature.item.partnerId))
    await dispatch(bookingActions.resetBookingFlow({}))
    await dispatch(
      bookingActions.setUrlParams({
        partnerId: openFeature.item.partnerId
      })
    )
    router.push({
      pathname: '/chon-lich-kham',
      query: {
        partnerId: openFeature.item.partnerId,
        feature: item?.type,
        behavior: router.query?.behavior
      }
    })
  }
  const handleDetectLocation = () => {
    Radar.initialize(currentEnv.RADA_KEY)
    Radar.getLocation()
      .then(async (result: any) => {
        const response = await Radar.reverseGeocode({
          latitude: result.latitude,
          longitude: result.longitude
        })
        dispatch(
          locationActions.setUserLocation({
            ...result,
            ...response?.addresses[0]
          })
        )
      })
      .catch((err) => {
        let errorMessage = 'Đã xảy ra lỗi không xác định'
        if (err?.message?.includes('permissions')) {
          errorMessage = 'Medpro không được phép sử dụng vị trí của bạn.'
        }
        openNotification(
          'info',
          {
            message: (
              <div>
                <ExclamationCircleOutlined /> <span>Thông báo</span>
              </div>
            ),
            description: errorMessage,
            placement: 'topLeft'
          },
          'primary'
        )
      })
  }

  return (
    <div className={styles['hospitals']}>
      <div id='top' />
      <MPListHospital
        appInfo={appInfo}
        data={hospitals}
        provinces={provinces}
        onSelectHospital={onSelectHospital}
        onBooking={onBooking}
        handleDetectLocation={handleDetectLocation}
        locationData={locationData}
      />

      <MPNewHospitalContact handleSubmit={onSubmitContactUs} />
      {openFeature.open && (
        <DefautDrawer
          onClose={() => setOpenFeature({ item: {}, open: false })}
          open={openFeature.open}
          title='Chọn hình thức đặt khám'
          height={'50%'}
          className={styles['mobileFeature']}
        >
          <div className={styles['title']}>{openFeature?.item?.name}</div>
          {features.map((item, index) => {
            return (
              <div
                className={styles['card']}
                key={index}
                onClick={() => onFinish(item)}
              >
                {item?.image && (
                  <figure className={styles['cardImage']}>
                    <img src={item?.image} width={18} height={18} alt={''} />
                  </figure>
                )}
                <div className={styles['name']}>{item?.name}</div>
              </div>
            )
          })}
        </DefautDrawer>
      )}
    </div>
  )
}

Hospitals.ssr = true
export default Hospitals

export async function getStaticProps(context: GetStaticPropsContext) {
  if (
    context.params?.type &&
    !TYPE_HOSPITAL.includes(context.params?.type as string)
  ) {
    return {
      redirect: {
        permanent: true,
        destination: '/404'
      }
    }
  }
  // const hospitals = await fetchHospitalsV6Server()
  return {
    props: {
      // hospitals: hospitals
    },
    revalidate: SSG_REVALIDATE_SECOND
  }
}
