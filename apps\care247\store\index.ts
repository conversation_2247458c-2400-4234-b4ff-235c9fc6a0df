import { configureStore } from '@reduxjs/toolkit'
import createSagaMiddleware from 'redux-saga'
import rootSaga from './sagas'
import paymentReducer from './slices/paymentSlice'
import { currentEnv } from '../config/envs'

const sagaMiddleware = createSagaMiddleware()

console.log('🏪 Setting up Care247 store...')
console.log('🔧 Environment config:', {
  API_BE: currentEnv.API_BE,
  RESTFULL_API_URL: currentEnv.RESTFULL_API_URL,
  environment: 'hotfix'
})

export const store = configureStore({
  reducer: {
    payment: paymentReducer
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE']
      }
    }).concat(sagaMiddleware),
  devTools: process.env.NODE_ENV !== 'production'
})

sagaMiddleware.run(rootSaga)

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

export default store
