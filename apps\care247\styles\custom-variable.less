/**
App custom variable, override ant default color
 */
//@primary-color: #1DA1F2;

@primary-color: #1da1f2;

@btn-height-base: 40px;

// <PERSON><PERSON><PERSON> phong chử
@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
  'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji',
  'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

@12: 0.75rem;
@13: 0.813rem;
@14: 0.875rem;
@16: 1rem;

// Card
@card-head-color: white;
@card-head-background: @primary-color;
@card-head-height: 20px;
@card-head-padding: 12px;

// Modal
@modal-header-bg: @primary-color;
@modal-heading-color: white;
@modal-close-color: white;
// global
@border-radius-base: 3px;

@breakpoint-xs: 576px;
@breakpoint-md: 768px;
@breakpoint-xl: 1200px;
.responsive-xs(@rules) {
  @media only screen and (max-width: @breakpoint-xs) {
    @rules();
  }
}
.responsive-md(@rules) {
  @media only screen and (max-width: @breakpoint-md) {
    @rules();
  }
}
.responsive-xl(@rules) {
  @media only screen and (max-width: @breakpoint-xl) {
    @rules();
  }
}
