{"$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/care247-e2e/src", "projectType": "application", "targets": {"e2e": {"executor": "@nrwl/cypress:cypress", "options": {"cypressConfig": "apps/care247-e2e/cypress.config.ts", "devServerTarget": "care247:serve:development", "testingType": "e2e"}, "configurations": {"production": {"devServerTarget": "care247:serve:production"}}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/care247-e2e/**/*.{js,ts}"]}}}, "tags": [], "implicitDependencies": ["care247"]}