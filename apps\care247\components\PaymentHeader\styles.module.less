.header {
  background: white;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .logo {
    display: flex;
    flex-direction: column;

    .logoText {
      font-size: 24px;
      font-weight: bold;
      color: #52c41a;
    }

    .logoSubtext {
      font-size: 12px;
      color: #666;
    }
  }

  .timer {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .timerText {
      font-size: 12px;
      color: #666;
    }

    .timerValue {
      font-size: 18px;
      font-weight: bold;
      color: #ff4d4f;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .header {
    padding: 12px 16px;
    
    .logo .logoText {
      font-size: 20px;
    }
    
    .timer .timerValue {
      font-size: 16px;
    }
  }
}
