import { Client } from 'medpro-sdk-v2'

const apiRoot = process.env.NEXT_PUBLIC_API_BE || 'https://api-hotfix.medpro.com.vn'

export const initOptions = {
  apiRoot,
  appid: 'care247',
  platform: 'pc',
  locale: 'vi'
}

const client = new Client(initOptions)

if (typeof window !== 'undefined') {
  const searchParams = new URLSearchParams(window.location.search)
  const refCode = searchParams.get('refCode') || localStorage.getItem('refCode') || ''
  client.setRefCode(refCode)
  client.setToken(localStorage.getItem('token'))
}

export default client
