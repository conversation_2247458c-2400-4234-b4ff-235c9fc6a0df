import { Client } from 'medpro-sdk-v2'
import { currentEnv } from './envs'

const apiRoot = currentEnv.API_BE

export const initOptions = {
  appid: 'care247',
  apiRoot,
  platform: 'pc',
  locale: 'vi'
}

const client = new Client(initOptions)

if (typeof window !== 'undefined') {
  const searchParams = new URLSearchParams(window.location.search)
  const refCode = searchParams.get('refCode') || localStorage.getItem('refCode') || ''
  client.setRefCode(refCode)
  client.setToken(localStorage.getItem('token'))
}

console.log('🔧 Care247 SDK initialized with:', {
  apiRoot,
  environment: 'hotfix'
})

export default client
