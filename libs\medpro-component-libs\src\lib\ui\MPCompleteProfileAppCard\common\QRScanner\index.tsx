import { Html5Qrcode, Html5QrcodeScanType } from 'html5-qrcode'
import { Button, Modal } from 'antd'
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState
} from 'react'
import Image from 'next/image'
import cx from 'classnames'
import styles from './styles.module.less'

const QRScanner = forwardRef(({ onScanSuccess }: any, ref) => {
  const html5QrCodeRef: any = useRef(null)
  const imageInputRef = useRef<HTMLInputElement>(null)
  const [isModalError, setIsModalError] = useState(false)
  const [isScanning, setIsScanning] = useState(false)
  const [messageError, setMessageError] = useState('')
  const [imagePreviewUrl, setImagePreviewUrl] = useState('')

  useImperativeHandle(ref, () => ({
    stopScanning: () => {
      if (isScanning && html5QrCodeRef.current) {
        html5QrCodeRef.current.stop().catch(notificationError)
        setIsScanning(false)
      }
    },
    stopScanningImg: () => {
      if (imagePreviewUrl) {
        setImagePreviewUrl('')
      }
    }
  }))

  useEffect(() => {
    setTimeout(() => {
      const readerElement = document.getElementById('reader')
      if (readerElement) {
        html5QrCodeRef.current = new Html5Qrcode('reader')
      }
    }, 500)

    return () => {
      void stopScanning()
    }
  }, [])

  const startScanning = async () => {
    if (!html5QrCodeRef.current) return
    setIsScanning(true)
    setImagePreviewUrl('')
    try {
      await html5QrCodeRef.current.start(
        { facingMode: 'environment' },
        {
          fps: 15,
          qrbox: { width: 250, height: 250 },
          aspectRatio: 1,
          supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],
          videoConstraints: {
            aspectRatio: 1,
            width: { ideal: 2080 },
            height: { ideal: 2080 },
            facingMode: 'environment'
          }
        },
        (decodedText: any) => {
          if (onScanSuccess) onScanSuccess(decodedText)
          stopScanning()
        },
        (errorMessage: any) => {
          console.log('QR Code Scan Error:', errorMessage)
        }
      )
    } catch (error) {
      notificationError(error)
    }
  }

  const stopScanning = async () => {
    if (!html5QrCodeRef.current) return
    setIsScanning(false)
    html5QrCodeRef.current.stop().catch(notificationError)
    const stream = await navigator.mediaDevices.getUserMedia({ video: true })
    const tracks = stream.getTracks()
    tracks.forEach((track) => track.stop())
  }

  const onUploadClick = () => {
    if (imageInputRef?.current) {
      imageInputRef.current?.click()
    }
  }

  const resizeImage = async (file: File, maxSize = 500): Promise<File> => {
    return new Promise((resolve, reject) => {
      const img = document.createElement('img') as HTMLImageElement
      const canvas = document.createElement('canvas')
      const reader = new FileReader()

      reader.onload = (e) => {
        img.src = e.target?.result as string
      }

      img.onload = () => {
        const scale = Math.min(maxSize / img.width, maxSize / img.height)
        canvas.width = img.width * scale
        canvas.height = img.height * scale
        const ctx = canvas.getContext('2d')
        ctx?.drawImage(img, 0, 0, canvas.width, canvas.height)
        canvas.toBlob((blob) => {
          if (!blob) return reject('Failed to resize image.')
          const resizedFile = new File([blob], file.name, { type: file.type })
          resolve(resizedFile)
        }, file.type || 'image/png')
      }

      img.onerror = reject
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }

  const onImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const resizedFile = await resizeImage(file)
    const readerPreview = new FileReader()
    readerPreview.onload = (e) => {
      setImagePreviewUrl(e.target?.result as string)
    }
    readerPreview.readAsDataURL(resizedFile)

    const html5QrCode = new Html5Qrcode('reader')
    html5QrCode
      .scanFile(resizedFile, true)
      .then((decodedText) => {
        onScanSuccess?.(decodedText)
      })
      .catch((fileError) => {
        notificationError(fileError?.message)
      })
      .finally(() => {
        html5QrCode.clear()
        if (imageInputRef.current) {
          imageInputRef.current.value = ''
        }
      })
  }

  const notificationError = (error: any) => {
    let message = error
    if (message?.includes('NotFoundError')) {
      message = 'Không tìm thấy thiết bị được yêu cầu!'
    }
    if (message?.includes('NotAllowedError')) {
      message = 'Camera thiết bị chưa được cấp quyền!'
    }
    if (message.includes('NotReadableError')) {
      message =
        'Không thể mở camera. Hãy đảm bảo không có ứng dụng nào khác đang sử dụng camera!'
    }
    if (message?.includes('detect the code')) {
      message =
        'Không phát hiện mã QR trong ảnh/ảnh mờ/ảnh không đạt yêu cầu. Vui lòng thử lại với ảnh khác!'
    }
    setMessageError(message)
    setIsModalError(true)
    setIsScanning(false)
  }

  return (
    <>
      <div className={styles['cameraContainer']}>
        <div className={styles['cameraWrapper']}>
          <div id='reader' className={styles['cameraFrame']} />
          {!isScanning && (
            <div className={styles['actionGroup']}>
              <Button
                className={cx(styles.btnScanning, styles.btn)}
                onClick={startScanning}
              >
                Bắt đầu quét
              </Button>
              <Button
                className={cx(
                  styles.btnImgScanning,
                  styles.btn,
                  imagePreviewUrl && styles.btnImagePreviewUrl
                )}
                onClick={onUploadClick}
              >
                <div className={styles['icon']}>
                  <Image src={require('../iconUploadImg.png')} alt='' />
                </div>
                Chọn từ ảnh
              </Button>
              <input
                type='file'
                accept='image/*'
                style={{ display: 'none' }}
                ref={imageInputRef}
                onChange={onImageUpload}
              />
            </div>
          )}
        </div>
      </div>
      {messageError && (
        <Modal
          centered
          title='Thông báo'
          open={isModalError}
          footer={false}
          onCancel={() => {
            setIsModalError(false)
            setMessageError('')
          }}
          className={styles['modalMessageContainer']}
        >
          <div className={styles['text']}>{messageError}</div>
        </Modal>
      )}
    </>
  )
})

export default QRScanner
