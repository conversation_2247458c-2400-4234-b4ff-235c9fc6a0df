import {
  MPCreatePatientForm,
  MPLoading,
  PatientFormSubmit
} from '@medpro-libs/libs'
import React, { useEffect, useMemo, useState } from 'react'
import { useDispatch } from 'react-redux'
import {
  fetchCountries,
  fetchDistricts,
  fetchProvinces,
  fetchRelative,
  fetchWards,
  setDistricts,
  setWards
} from '../../../../../../store/total/slice'
import { useAppSelector } from '../../../../../../store/hooks'
import {
  useFetchNation,
  useFetchProfession
} from '../../../../../../hooks/query'
import client from '../../../../../../config/medproSdk'
import { showErrorNotification } from '../../../../../../utils/utils.error'
import { openNotification } from '../../../../../../utils/utils.notification'
import router from 'next/router'
import { PageRoutes } from '../../../../../../utils/PageRoutes'
import { splitName } from '../../../../../../utils/utils.function'
import moment from 'moment'
import { PatientInsertQuery } from 'medpro-sdk-v2'
import { patientActions } from '../../../../../../store/patient/patientSlice'
import { getRoutePartnerIdQueryParams } from '@medpro-libs/libs'
import { size } from 'lodash'
import { extractInformationFromQR } from '../../common/func'

interface PatientFormProps {
  partnerId?: string
  patientId?: string
  patientUpdate?: any
  patientYearOldAccepted?: number
}

export const PatientForm = ({
  partnerId,
  patientUpdate,
  patientId,
  patientYearOldAccepted
}: PatientFormProps) => {
  const dispatch = useDispatch()

  const [submitting, setSubmitting] = useState(false)

  const countries = useAppSelector((s) => s.total.countries)
  const province = useAppSelector((s) => s.total.provinces)
  const district = useAppSelector((s) => s.total.districts)
  const ward = useAppSelector((s) => s.total.wards)
  const relative = useAppSelector((s) => s.total.relative)
  const redirectUrl = useAppSelector(
    (s) => s.patient.updatePatient?.redirectUrl
  )
  const userId = useAppSelector((s) => s.user.userInfo?.id)
  const redirectUrlMemo = useMemo(() => redirectUrl, [])

  const createPatientRedirectUrl = useAppSelector(
    (s) => s.patient.createPatientRedirectUrl
  )

  useEffect(() => {
    dispatch(fetchCountries({}))

    if (size(province) === 0) {
      dispatch(fetchProvinces({}))
    }

    if (size(relative) === 0) {
      dispatch(fetchRelative())
    }
  }, [])

  useEffect(() => {
    if (patientUpdate) {
      if (patientUpdate.city_id) {
        dispatch(fetchDistricts({ city_id: patientUpdate.city_id }))
      }

      if (patientUpdate.district_id) {
        dispatch(fetchWards({ district_id: patientUpdate.district_id }))
      }
    }
  }, [patientUpdate])

  const { data: profession, isLoading: loadingProfession } =
    useFetchProfession()
  const { data: nation, isLoading: loadingNation } = useFetchNation()

  function cleanDistrictsAndWards() {
    dispatch(setDistricts([]))
    dispatch(setWards([]))
  }

  const dataNewPatient = {
    description:
      'Vui lòng cung cấp thông tin chính xác để được phục vụ tốt nhất.',
    profession,
    countries: countries,
    nation,
    province,
    district,
    ward,
    patient: patientUpdate,
    relative
  }

  const onChangeAddress = (type: string, id: string) => {
    switch (type) {
      case 'district':
        dispatch(fetchDistricts({ city_id: id }))
        break
      case 'ward':
        dispatch(fetchWards({ district_id: id }))
        break
      default:
        break
    }
  }

  const onSubmit = async (params: PatientFormSubmit) => {
    const {
      name,
      ward_id,
      address,
      district_id,
      city_id,
      sex,
      profession_id,
      dantoc_id,
      mobile,
      day,
      month,
      year,
      relation = {},
      email,
      cmnd,
      country_code
    } = params

    const { relative_email, relative_mobile, relative_name, relative_type_id } =
      relation

    //Nếu có số điện thoại thân nhân thì gán vào cho mobile.
    const phone_number = relative_mobile || mobile

    //Params for submit.
    const body: PatientInsertQuery = {
      address,
      sex,
      ward_id,
      district_id,
      city_id,
      mobile: phone_number,
      ...splitName(name?.toUpperCase()),
      birthdate: moment(`${year}-${month}-${day}`, 'YYYY-MM-DD')
        .utc(true)
        .toISOString(),
      birthyear: year,
      profession_id,
      dantoc_id,
      country_code: country_code,
      relative_email,
      relative_mobile,
      relative_name: relative_name?.toUpperCase(),
      relative_type_id,
      force: false,
      id: patientId as any,
      email: email || '',
      cmnd: cmnd || ''
    }

    if (patientId) {
      try {
        setSubmitting(true)
        await client.patient.updatePatient(body)
        dispatch(patientActions.resetUpdatePatient())
        openNotification('success', {
          message: 'Cập nhật thông tin thành công.'
        })

        router.push(redirectUrlMemo || PageRoutes.user.path)
      } catch (err) {
        showErrorNotification(err)
      } finally {
        setSubmitting(false)
      }
    } else {
      try {
        setSubmitting(true)
        const { data } = await client.patient.insertPatient(body)
        const { isRecommended } = data

        if (isRecommended) {
          dispatch(patientActions.setNewPatientRecommendation(data))
          router.push(
            getRoutePartnerIdQueryParams(
              PageRoutes.patient.recommendation.path,
              partnerId
            )
          )
        } else {
          openNotification('success', { message: 'Thêm hồ sơ thành công.' })
          router.push(
            createPatientRedirectUrl ||
              getRoutePartnerIdQueryParams(PageRoutes.user.path, partnerId, {
                key: 'records'
              })
          )
        }
      } catch (err) {
        showErrorNotification(err)
      } finally {
        setSubmitting(false)
      }
    }
  }

  if (loadingNation || loadingProfession) {
    return (
      <MPLoading loading={{ description: 'Đang lấy thông tin bệnh nhân...' }} />
    )
  }

  return (
    <MPCreatePatientForm
      data={dataNewPatient}
      submitting={submitting}
      cleanDistrictsAndWards={cleanDistrictsAndWards}
      patientYearOldAccepted={patientYearOldAccepted}
      onSubmit={onSubmit}
      onChangeAddress={onChangeAddress}
      partnerId={partnerId}
      userId={userId}
      extractInformation={extractInformationFromQR}
      isCreate={!patientUpdate}
    />
  )
}
