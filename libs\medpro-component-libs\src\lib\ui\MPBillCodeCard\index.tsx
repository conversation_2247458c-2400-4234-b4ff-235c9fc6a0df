/* eslint-disable react-hooks/rules-of-hooks */
import { But<PERSON> } from 'antd'
import cx from 'classnames'
import Image from 'next/image'
import { useCallback } from 'react'
// import { HiBellSlash } from 'react-icons/hi2'
import { BOOKING_STATUS } from '../../common/constant'
import { renderDurationNumber, useCountDown } from './common/Hooks/useCountDown'
import MPBillCodeInfo from './common/MPBillCodeInfo'
import styles from './styles.module.less'
// import { useRouter } from 'next/router'
import { MdOutlineMonetizationOn } from 'react-icons/md'
import { getTimezoneBooking } from '../MPBookingInfoCard/common/func'

interface IF_RenderDescription {
  bookingCode: string
  status: number
  description: string
  bookingStatusStyle: any
  paymentStatus?: any
  refundText?: any
  refundTextStyle?: any
}

interface IF_RenderDescription {
  status: number
  description: string
  bookingStatusStyle: any
  paymentStatus?: any
  refundText?: any
  refundTextStyle?: any
  viewBooking?: any
}

export const MPBillCodeCard = ({
  data,
  repayment,
  shareToPay,
  isCSKHApp,
  viewBooking
}: any) => {
  const bookingInfoData = data?.bookingInfo
  const iconPhone = require('./img/Phone.svg')
  const reload = require('./img/reload.svg')
  const { duration } = useCountDown(
    bookingInfoData?.notPaymentYetInfo?.countdown,
    1000
  )
  const [DATE, TIME] = getTimezoneBooking({
    date: bookingInfoData?.date,
    time: bookingInfoData?.timeStr,
    awaitMessage: bookingInfoData?.awaitMessage,
    waitingConfirmDate: bookingInfoData?.waitingConfirmDate
  })
  const titleBookingTime =
    bookingInfoData?.partnerId === 'trungvuong'
      ? 'Giờ tiếp nhận dự kiến'
      : 'Giờ khám dự kiến'
  const renderChuaThanhToanComponent = useCallback(
    (status: number, bookingInfo: any) => {
      if (
        status === BOOKING_STATUS.CHUA_THANH_TOAN ||
        status === BOOKING_STATUS.THANH_TOAN_HO
      ) {
        return (
          <>
            {!viewBooking && shouldRenderNotPaymentMessage(bookingInfo) && (
              <div
                className={styles['notPaymentYetMessage']}
                dangerouslySetInnerHTML={{
                  __html:
                    bookingInfoData?.notPaymentYetInfo?.notPaymentYetMessage
                }}
              />
            )}
            {!isCSKHApp && !viewBooking && (
              <div className={styles['notPaymentYetButton']}>
                <Button
                  className={styles['sharePaymentBtn']}
                  onClick={shareToPay}
                >
                  <MdOutlineMonetizationOn size={17} color={'#11a2f3'} />
                  <label>Nhờ thanh toán</label>
                </Button>
                <Button onClick={repayment} className={styles['rePaymentBtn']}>
                  <Image src={reload} width={17} height={17} />
                  <label>Thanh toán lại</label>
                </Button>
              </div>
            )}
            {/* <div className={styles['notPaymentYetShortMessage']}>
              <Button style={{ display: 'flex', alignItems: 'center' }}>
                <Image src={iconPhone} width={32} height={32} />
                <div className={styles['notPaymentYetShortMessageCallText']}>
                  <a href='tel:19002115'>
                    <span>Gọi</span> 1900 2115
                  </a>
                  <p>để được hỗ trợ trực tiếp</p>
                </div>
              </Button>
            </div> */}
          </>
        )
      }
      return
    },
    [isCSKHApp, repayment, shareToPay]
  )

  const bookingInfo = {
    bookingStatus: bookingInfoData?.status,
    bookingStatusStyle: bookingInfoData?.bookingStatusStylem || {},
    treeId: bookingInfoData?.treeId,
    displayCodeBooking: bookingInfoData?.displayCodeBooking,
    bookingDescription: handleRenderDescription({
      bookingCode: bookingInfoData?.bookingCode,
      status: bookingInfoData?.status,
      description: bookingInfoData?.description,
      bookingStatusStyle: bookingInfoData?.bookingStatusStyle,
      paymentStatus: bookingInfoData?.paymentStatus,
      refundText: bookingInfoData?.refundText,
      refundTextStyle: bookingInfoData?.refundTextStyle
    }),
    cancelMessage: bookingInfoData?.cancelMessage,
    shortDescriptionService: bookingInfoData?.service?.shortDescription,
    shortDescription: bookingInfoData?.shortDescription,
    totalPaymentMessage: bookingInfoData?.totalPaymentMessage,
    totalMessageExtra: bookingInfoData?.totalMessageExtra,
    awaitMessage: bookingInfoData?.awaitMessage,
    sequenceNumber: bookingInfoData?.sequenceNumber,
    // Dua vao status ma render ra short description tuong ung
    isRenderShortDescription: handleIsRenderShortDescription(
      bookingInfoData?.status
    ),
    // Hiển thị thông tin chưa thanh toán dựa vào bookingStatus
    renderChuaThanhToanComponent: renderChuaThanhToanComponent(
      bookingInfoData?.status,
      bookingInfoData
    )
  }
  return (
    <div className={styles['printBillCode']}>
      {shouldRenderCountDown(bookingInfoData) && (
        <div className={styles['notPaymentYetCountDown']}>
          <span>Thời gian thanh toán còn lại</span>
          <div>
            {renderDurationNumber(duration.hours())}:
            {renderDurationNumber(duration.minutes())}:
            {renderDurationNumber(duration.seconds())}
          </div>
        </div>
      )}
      {/* Mã Phiếu khám bệnh */}
      <div
        className={cx(
          styles['segmentCode'],
          !bookingInfo.displayCodeBooking.value && styles['center']
        )}
      >
        {bookingInfo.displayCodeBooking.value && (
          <MPBillCodeInfo
            treeId={bookingInfo.treeId}
            status={bookingInfo.bookingStatus}
            code={bookingInfo.displayCodeBooking}
          />
        )}

        {![
          BOOKING_STATUS.CHUA_THANH_TOAN,
          BOOKING_STATUS.THANH_TOAN_HO,
          BOOKING_STATUS.DA_HUY
        ].includes(bookingInfo.bookingStatus) ? (
          <div className={styles['segmentInfo']}>
            {bookingInfo.awaitMessage ? (
              <div className={styles['awaitMessage']}>
                <p>{titleBookingTime}</p>
                <b>{bookingInfo.awaitMessage}</b>
              </div>
            ) : (
              <div className={styles['boxSegmentInfo']}>
                <div className={styles['segmentInfoTime']}>
                  <p>{titleBookingTime}</p> <b>{TIME}</b>
                </div>
                {bookingInfo?.sequenceNumber && (
                  <div className={styles['segmentInfoSTT']}>
                    <p>Số thứ tự tiếp nhận</p>{' '}
                    <b>{bookingInfo?.sequenceNumber}</b>
                  </div>
                )}
              </div>
            )}
          </div>
        ) : (
          <></>
        )}
      </div>

      {bookingInfo.bookingDescription}
      {bookingInfo?.totalPaymentMessage && (
        <div className={styles['segmentTotalFeeMessage']}>
          <p className={styles['totalPaymentMessage']}>
            {bookingInfo?.totalPaymentMessage}
          </p>
          <p className={styles['totalMessageExtra']}>
            {bookingInfo?.totalMessageExtra}
          </p>
        </div>
      )}
      {bookingInfo.renderChuaThanhToanComponent}
      {/* {bookingInfo.cancelMessage && (
        <div className={styles['cancelMessage']}>
          {bookingInfo.cancelMessage}
        </div>
      )} */}
      {bookingInfo.shortDescriptionService &&
        bookingInfo.isRenderShortDescription && (
          <div
            className={styles['cancelMessage']}
            dangerouslySetInnerHTML={{
              __html: bookingInfo.shortDescriptionService
            }}
          />
        )}
      {bookingInfo.shortDescription && (
        <div
          dangerouslySetInnerHTML={{
            __html: bookingInfo.shortDescription
          }}
        />
      )}
    </div>
  )
}

const handleIsRenderShortDescription = (status: number) => {
  const filterStatusCode = [
    BOOKING_STATUS.DA_HUY,
    BOOKING_STATUS.DA_KHAM,
    BOOKING_STATUS.CHUA_THANH_TOAN,
    BOOKING_STATUS.THANH_TOAN_HO
  ]
  return !filterStatusCode.includes(status)
}

const handleRenderDescription = ({
  bookingCode,
  status,
  description,
  bookingStatusStyle,
  paymentStatus,
  refundText,
  refundTextStyle
}: IF_RenderDescription) => {
  const classGreen = styles['greenNote']
  const classRed = styles['redNote']
  const classGrey = styles['greyNote']
  const classTimeNote = cx({
    [classGreen]: true,
    [classRed]:
      status === BOOKING_STATUS.CHUA_THANH_TOAN ||
      status === BOOKING_STATUS.THANH_TOAN_HO,
    [classGrey]: status === BOOKING_STATUS.DA_HUY
  })
  switch (status) {
    case BOOKING_STATUS.CHUA_THANH_TOAN:
    case BOOKING_STATUS.THANH_TOAN_HO:
      return (
        <div className={styles['notPaymentYetDescription']}>
          <Image
            src={
              'https://bo-api.medpro.com.vn/static/images/medpro/web/icon_payment_failed.svg?t=111111'
            }
            width={70}
            height={70}
          />
          <span>
            <p>
              Phiếu khám <b>{bookingCode}</b>
            </p>

            <p className={styles['YetDescriptionText']}>{description}</p>
          </span>
        </div>
      )
    default:
      return (
        <>
          <div
            style={bookingStatusStyle}
            className={cx(styles['billDescription'], classTimeNote)}
          >
            {description}
          </div>
          {paymentStatus === 2 && (
            <p style={{ color: refundTextStyle?.color }}>{refundText}</p>
          )}
        </>
      )
  }
}
export default MPBillCodeCard

function shouldRenderCountDown(bookingInfoData: any) {
  return (
    bookingInfoData?.notPaymentYetInfo &&
    bookingInfoData?.notPaymentYetInfo?.countdown
  )
}
function shouldRenderNotPaymentMessage(bookingInfoData: any) {
  return (
    bookingInfoData?.notPaymentYetInfo?.notPaymentYetMessage &&
    bookingInfoData?.notPaymentYetInfo
  )
}
