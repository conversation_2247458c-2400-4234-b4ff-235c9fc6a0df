import React, { useState } from 'react'
import styles from './styles.module.less'
import MPButton from '../MPButton'
import { Card, Modal, Result } from 'antd'
import { BiLeftArrowAlt } from 'react-icons/bi'
import { FaUserPlus, FaUser, FaPhone, FaBirthdayCake } from 'react-icons/fa'
import { FiEdit } from 'react-icons/fi'
import { HiOutlineDotsVertical, HiLocationMarker } from 'react-icons/hi'
import format from '../../common/format'
import RecordDetail from './detailProfile'
import DefautDrawer from '../DefaultDrawer'
import Image from 'next/image'
import action from './image/actiton.svg'
import EmptyProfile from './emptyProfile'
import { useRouter } from 'next/router'
export interface propChooseProfileService {
  listHandle: any[]
  data: any[]
  modalData: any
  onClose: () => void
  handleChangeEvent: (item: any) => void
  handleDelete: (item: any) => void
  handleCreate: (type: string) => void
  handleSelect: (item: any) => void
  partnerConfig: any
  userConfigBtn: any
}

const MPChooseProfileServicesCard = ({
  data,
  listHandle,
  modalData,
  onClose,
  handleChangeEvent,
  handleDelete,
  handleCreate,
  handleSelect,
  partnerConfig,
  userConfigBtn
}: propChooseProfileService) => {
  const router = useRouter()
  const partnerId = router.query.partnerId as string
  const [openCreate, setOpenCreate] = useState(false)

  const onOpenCreate = () => {
    setOpenCreate(true)
  }

  // const handleAddress = (item: any) => {
  //   let address
  //   if (
  //     item?.country_code === 'VIE' ||
  //     item?.country_code === 'medpro_VIE' ||
  //     item?.country_id === 'VIE' ||
  //     item?.country_id === 'medpro_VIE'
  //   ) {
  //     const list = [
  //       item?.address,
  //       item?.ward?.name,
  //       item?.district?.name,
  //       item?.city?.name
  //     ]
  //     address = list?.filter(Boolean).join(', ')
  //   } else {
  //     address = item?.address
  //   }
  //   return address ? address : 'Chưa cập nhật'
  // }
  const UserTabs = [
    {
      id: 'tao_ho_so_moi_btn',
      children: (
        <MPButton onClick={() => handleCreate('create')} type='primary'>
          Chưa từng khám, đăng ký mới
        </MPButton>
      )
    },
    {
      id: 'da_tung_khamt_btn',
      children: (
        <MPButton
          type='default'
          onClick={() => handleCreate('find')}
          className={styles['bookedBtn']}
        >
          Đã từng khám, nhập số hồ sơ
        </MPButton>
      )
    }
  ]
  console.log('userConfigBtn :>> ', userConfigBtn)
  const ActiveTabs = UserTabs.filter((tab) => {
    if (tab.id === 'da_tung_khamt_btn') {
      return !!partnerConfig?.da_tung_khamt_btn
    }
    return userConfigBtn?.[tab.id]
  }).map((tab) => tab.children)

  return (
    <>
      {data.length ? (
        <div className={styles['recordsCompoment']}>
          <div className={styles['titleRecord']}>
            <p>Danh sách hồ sơ bệnh nhân</p>
            <MPButton
              icon={<FaUserPlus size={18} color='red' />}
              className={styles['buttonAdd']}
              onClick={onOpenCreate}
            >
              <p>Thêm</p>
            </MPButton>
          </div>

          {data.map((item: any, index: number) => (
            <div
              className={styles['record']}
              key={index}
              onClick={() => handleSelect(item)}
            >
              <div className={styles['recordBody']}>
                <Card>
                  <div className={styles['title']}>
                    <div className={styles['info']}>
                      <FaUser size={18} />
                      <p>
                        {item?.fullname ||
                          `${item?.surname} ${item?.name}` ||
                          'Chưa cập nhật'}
                      </p>
                    </div>
                    <HiOutlineDotsVertical
                      size={18}
                      color='#000'
                      onClick={(e) => {
                        e.stopPropagation()
                        handleChangeEvent(item)
                      }}
                    />
                  </div>
                  <div className={styles['info']}>
                    <FaPhone size={16} />
                    <p>
                      {item?.mobile
                        ? `${format.concerPhone(item?.mobile)}`
                        : 'Chưa cập nhật'}
                    </p>
                  </div>
                  <div className={styles['info']}>
                    <FaBirthdayCake size={18} />
                    <p>
                      {item?.birthdate || item?.birthyear || 'Chưa cập nhật'}
                    </p>
                  </div>
                  <div className={styles['info']}>
                    <HiLocationMarker size={19} />
                    <p>{item.fullAddress}</p>
                  </div>
                </Card>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <EmptyProfile onOpenCreate={onOpenCreate} />
      )}
      {modalData.isOpen && (
        <DefautDrawer
          onClose={onClose}
          open={modalData.isOpen}
          title='Tùy chọn'
          height={'38%'}
          className={styles['optionDrawer']}
        >
          {listHandle.map((item) => (
            <div
              key={item.key}
              onClick={() => item.handle(modalData.data)}
              className={styles['itemHandle']}
            >
              {item.icon}
              <p>{item.title}</p>
            </div>
          ))}
        </DefautDrawer>
      )}
      {modalData.isDetail && (
        <DefautDrawer
          onClose={onClose}
          open={modalData.isDetail}
          title={
            <div className={styles['headerDetail']}>
              <BiLeftArrowAlt size={24} onClick={() => onClose()} />
              <p>Chi tiết hồ sơ</p>
              <FiEdit
                size={17}
                onClick={() =>
                  router.push({
                    pathname: '/cap-nhat-thong-tin',
                    query: {
                      partnerId: partnerId,
                      id: modalData.data.id,
                      editProfile: true,
                      prevPage: router.asPath
                    }
                  })
                }
              />
            </div>
          }
          className={styles['recordDetail']}
        >
          <RecordDetail data={modalData.data} />
        </DefautDrawer>
      )}
      {modalData.isDelete && (
        <Modal
          open={modalData.isDelete}
          className={styles['modal']}
          footer={false}
          onCancel={() => onClose()}
          centered
          width={400}
        >
          <Result
            status='error'
            icon={
              <Image
                className={styles['deleteImg']}
                src={action}
                width={100}
                height={120}
                alt=''
                objectFit='contain'
              />
            }
            title={
              <div className={styles['deleteTitle']}>
                Bạn chắc chắn muốn xóa?
              </div>
            }
            subTitle={
              <p className={styles['deleteContent']}>
                Lưu ý rằng các tin quan trọng về hồ sơ cũng sẽ bị xóa
              </p>
            }
            extra={[
              <MPButton
                onClick={() => {
                  onClose()
                }}
                type='primary'
                key='console'
                className={styles['btnCancel']}
              >
                Hủy
              </MPButton>,
              <MPButton
                onClick={() => handleDelete(modalData.data)}
                key='buy'
                className={styles['btnDel']}
              >
                Xóa
              </MPButton>
            ]}
          ></Result>
        </Modal>
      )}
      {openCreate && (
        <DefautDrawer
          className={styles['drawerCreateContainer']}
          onClose={() => setOpenCreate(false)}
          open={openCreate}
          title='Tạo hồ sơ'
          height='auto'
        >
          <div className={styles['drawerCreate']}>
            <p>Bạn được phép tạo tối đa 10 hồ sơ</p>
            <p>(cá nhân và người thân trong gia đình)</p>
            {ActiveTabs.map((item) => item)}
          </div>
        </DefautDrawer>
      )}
    </>
  )
}

export default MPChooseProfileServicesCard
