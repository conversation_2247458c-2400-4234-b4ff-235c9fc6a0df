import includeEnvs from './includeEnvs'
import {
  API_URL,
  BO_API_URL,
  CMS_API_URL,
  MEDPRO_ID_URL,
  RESTFULL_API_URL,
  nameEnv
} from './environment'

const envObj = includeEnvs[nameEnv]
export const currentEnv = {
  ...envObj,
  API_BE: API_URL || envObj.API_BE,
  LOGIN: MEDPRO_ID_URL || envObj.LOGIN,
  API_CMS: CMS_API_URL || envObj.API_CMS,
  BO_API: BO_API_URL || envObj.BO_API,
  RESTFULL_API_URL: RESTFULL_API_URL || envObj.RESTFULL_API_URL_ENV
}
