import React, { useState } from 'react'
import styles from './styles.module.less'

interface PaymentMethod {
  id: string
  name: string
  icon: string
  description?: string
}

interface PaymentMethodsProps {
  onMethodSelect: (methodId: string) => void
  selectedMethod?: string
}

const paymentMethods: PaymentMethod[] = [
  {
    id: 'bank',
    name: '<PERSON><PERSON> hàng',
    icon: '🏦',
    description: '<PERSON>yển khoản qua ngân hàng'
  },
  {
    id: 'wallet',
    name: '<PERSON><PERSON> điện tử',
    icon: '💳',
    description: 'Thanh toán qua ví điện tử'
  },
  {
    id: 'momo',
    name: '<PERSON><PERSON><PERSON>',
    icon: '📱',
    description: 'Thanh toán qua ví MoMo'
  },
  {
    id: 'zalopay',
    name: '<PERSON>alo<PERSON><PERSON>',
    icon: '💰',
    description: 'Thanh toán qua ZaloPay'
  }
]

export function PaymentMethods({ onMethodSelect, selectedMethod }: PaymentMethodsProps) {
  const [activeMethod, setActiveMethod] = useState<string>(selectedMethod || '')

  const handleMethodClick = (methodId: string) => {
    setActiveMethod(methodId)
    onMethodSelect(methodId)
  }

  return (
    <div className={styles.paymentMethodsContainer}>
      <h3>Chọn phương thức thanh toán</h3>
      <div className={styles.methodsList}>
        {paymentMethods.map((method) => (
          <div
            key={method.id}
            className={`${styles.methodCard} ${
              activeMethod === method.id ? styles.active : ''
            }`}
            onClick={() => handleMethodClick(method.id)}
          >
            <div className={styles.methodIcon}>
              <span>{method.icon}</span>
            </div>
            <div className={styles.methodInfo}>
              <h4>{method.name}</h4>
              {method.description && (
                <p>{method.description}</p>
              )}
            </div>
            <div className={styles.methodSelector}>
              <div className={styles.radioButton}>
                {activeMethod === method.id && (
                  <div className={styles.radioSelected}></div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {activeMethod && (
        <div className={styles.selectedMethodInfo}>
          <div className={styles.infoBox}>
            <h4>Phương thức đã chọn</h4>
            <p>
              {paymentMethods.find(m => m.id === activeMethod)?.name}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}

export default PaymentMethods
