.hospitals {
  padding-bottom: 14px;
  @media only screen and (max-width: 1024px) {
    padding-bottom: 0;
  }

  .hidden {
    display: none;
  }

  .header {
    text-align: center;

    .title {
      color: #00b5f1;
      font-weight: 700;
      font-size: 39px;
      margin-bottom: 8px;
      line-height: 48px;
      font-family: 'Montserrat', sans-serif !important;
      @media only screen and (max-width: 768px) {
        font-size: 28px;
        line-height: 30px;
        margin-bottom: 8px;
      }
      @media (max-width: 415px) {
        font-size: 32px;
        line-height: normal;
      }
    }

    .sub_title {
      font-size: 16px;
      font-weight: 400;
      line-height: 18.75px;
      text-align: center;
      color: #003553;
    }

    .partner_title {
      font-family: 'Roboto' !important;
      color: #003553;
      font-weight: 400;
      font-size: 20px;
      line-height: 23px;
      width: fit-content;
      margin: auto;
      color: #003553;
      padding: 8px 16px;
      border-radius: 12px;
      border: 1px solid #00b5f1;
      @media only screen and (max-width: 768px) {
        font-size: 16px;
        line-height: 20px;
      }
      @media (max-width: 415px) {
        margin-top: 8px;
        font-size: 14px;
        line-height: normal;
      }
    }

    .partner_address {
      font-family: 'Roboto' !important;
      font-size: 16px;
      font-weight: 400;
      line-height: 19px;
      color: #858585;
      margin: 12px auto 0;

      svg {
        margin-right: 4px;
      }
    }
  }

  .sticky {
    height: 78px;
    @media (max-width: 415px) {
      height: 50px;
    }
  }

  .body {
    margin-top: 28px;
    @media only screen and (max-width: 768px) {
      margin-top: 16px;
      padding-bottom: 0px;
    }

    .stickyFilter {
      padding-top: 16px;
    }

    .form {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16px;
      padding-bottom: 16px;
      @media only screen and (max-width: 590px) {
        flex-wrap: wrap;
        gap: 5px;
      }

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }

        .ant-select-selector {
          border: none;
          line-height: 32px;
          @media (max-width: 576px) {
            padding-right: 10px;
            padding-left: 7px !important;
          }

          .ant-select-selection-item .ant-select-selection-placeholder {
            line-height: 32px !important;
            height: 32px !important;
            display: flex;
            align-items: center;
          }
        }

        .ant-select-single.ant-select-show-arrow .ant-select-selection-item,
        .ant-select-single.ant-select-show-arrow
        .ant-select-selection-placeholder {
          padding-right: 0;
          color: #003553;
          line-height: 33px;
        }

        .ant-select-arrow {
          color: #003553;

          @media (max-width: 576px) {
            right: 0;
          }
        }

        .ant-input-affix-wrapper > input.ant-input {
          @media (max-width: 576px) {
            padding-left: 0;
          }
        }

        .ant-input-suffix {
          display: none;
        }
      }

      &.formMobile {
        @media only screen and (min-width: 768px) {
          .locationGroup {
            display: none;
          }
        }
        @media only screen and (max-width: 768px) {
          flex-direction: column;
          background: #EBF5FD;
          margin-bottom: 10px;
          padding: 12px;
          border-radius: 12px;
          gap: 8px;

          .formContent {
            background: #ffffff;
            height: 45px;
            width: 100%;

            .item {
              border: none !important;
            }
          }

          .locationGroup {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 100%;

            .locationNearby {
              display: flex;
              gap: 2px;
              cursor: pointer;

              .icon {
                width: 20px;
                height: 20px;
              }

              .info {
                font-weight: 400;
                font-size: 14px;
              }
            }
          }
        }
      }

      .formContent {
        display: flex;
        // align-items: center;
        justify-content: space-between;
        width: 660px;
        height: 52px;
        box-shadow: 4px 8px 30px 0 rgba(177, 196, 218, 0.35);
        overflow: hidden;
        border-radius: 12px;
        position: relative;
        padding: 14px;
        gap: 8px;

        @media (max-width: 576px) {
          width: 100%;
          justify-content: space-between;
          height: 44px;
          padding: 0;
          box-shadow: none;
        }

        .unProvince {
          border: 0 !important;
          padding-left: 0 !important;
        }

        .item {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;

          @media (max-width: 576px) {
            width: 100%;
            border: 1px solid rgba(177, 196, 218, 0.35) !important;
            padding: 11px 13px !important;
          }

          &:last-child {
            // border-left: 1px solid #e0e0e0;
            @media (max-width: 576px) {
              // border: none;
              //margin-left: 8px;
              padding-left: 12px !important;
            }

            @media (max-width: 576px) {
              position: relative;
              width: 40%;
              border-radius: 12px;
            }
          }

          &:first-child {
            @media (max-width: 576px) {
              // height: 35px;
              width: 100%;
              border-radius: 12px;
            }
          }

          .icon {
            display: flex;
            align-items: center;
          }

          .inputItem {
            width: 100%;
            overflow: hidden;
            @media (max-width: 576px) {
              border-radius: 12px;
            }
          }

          .suffixItem {
            border-left: 1px solid #E3E7EB;
            padding-left: 10px;
            margin-left: 4px;

            &.suffixItemDesktop {
              @media (max-width: 768px) {
                display: none;
              }
            }

            .suffixAddress {
              display: flex;
              gap: 4px;
              cursor: pointer;

              .icon {
                width: 24px;
                height: 24px;
              }

              .info {
                font-weight: 400;
                font-size: 16px;
                color: #003553;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 120px;
              }
            }
          }
        }
      }

      .isHiddenFilter {
        .item {
          &:last-child {
            border: none !important;
            padding-left: 0;
          }
        }
      }

      .button {
        display: flex;
        padding: 15px 14px;
        justify-content: center;
        align-items: center;
        border-radius: 12px !important;
        border: 1px solid var(--Text-40, #e3e7eb);
        background: #fff;
        color: #24313d;
        gap: 4px;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        /* sd */
        box-shadow: 0px 5px 15px 0px rgba(203, 203, 203, 0.15);
      }

      .btnSubmit {
        width: 110px;
        height: 52px;
        padding: 10px 30px;
        border-radius: 12px;
        background: linear-gradient(85.72deg, #00b5f1 33.77%, #00e0ff 111%);
        box-shadow: 4px 8px 30px rgba(192, 202, 214, 0.35);
        border: none;
        text-transform: uppercase;
        @media only screen and (max-width: 590px) {
          width: 100%;
          height: 40px;
        }
      }
    }
  }
}

.activite {
  border: 1px solid #00b5f1 !important;
  color: #00b5f1 !important;

  svg {
    fill: #00b5f1 !important;
  }
}

.textColor {
  color: #00b5f1 !important;

  svg {
    fill: #00b5f1 !important;
  }
}

.buttonSearch {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12px !important;
  border: 1px solid var(--Text-40, #e3e7eb);
  color: #24313d;
  gap: 4px;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  margin-left: 8px;
  /* sd */
  box-shadow: 0px 5px 15px 0px rgba(203, 203, 203, 0.15);

  button {
    padding: 11px !important;
    background: #fff;
    border-radius: 12px !important;
  }
}

.formItem {
  width: 100%;

  .selectItem {
    width: 100%;

    :global {
      .ant-select-selection-search {
        right: 0;
        left: 11px !important;
        @media (max-width: 576px) {
          left: 7px !important;
        }
      }

      .ant-select-selection-placeholder {
        color: #003553 !important;
        margin-top: 1px;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }
  }
}

.desktop {
  @media (max-width: 576px) {
    display: none !important;
  }
}

.mobile {
  display: none;
  @media (max-width: 576px) {
    display: block;
  }
}

.selectHospital {
  :global {
    .ant-select-arrow {
      display: none;
    }
  }
}

.inputAntd {
  :global {
    .ant-select-arrow {
      display: none;
    }

    .ant-input-affix-wrapper:focus,
    .ant-input-affix-wrapper-focused {
      box-shadow: none;
    }
  }
}

.option {
  display: flex;
  justify-content: right;

  // width: 100%;
  // height: 100%;
  // text-align: center;
  .btn {
    font-size: 16px !important;
    border: none;
    padding: 4px 0 0 0 !important;
    background-color: #ffffff !important;
    color: #003553;
  }

  .icon {
    fill: #003553;
    margin-left: 4px;
  }

  :hover {
    .icon {
      fill: #47bfff !important;
    }
  }

  :focus {
    .icon {
      fill: #47bfff !important;
    }
  }
}
