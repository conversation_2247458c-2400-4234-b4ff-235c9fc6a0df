/* eslint-disable react/no-children-prop */
import {
  <PERSON><PERSON><PERSON><PERSON>,
  MPNewAchievement,
  MPNewBannerHeader,
  MPNewHomeDownload,
  MPNewHomeInfo,
  MPCooperated,
  MPCarouselBanner,
  MPCarouselBannerMulti,
  MPNewBookingPackage,
  MPNewHomeInfoBooking,
  MPDoctorTelemed,
  MPFeelPeople,
  MPNewPackageMonth,
  MPNewHospitalDeploy,
  MPNewNews,
  MPNewServiceHeader,
  MPNewServiceMobileHeader,
  MPFeatureMobile,
  MPNewStatistic,
  MPNewSupportMethod,
  PageRoutesV2,
  useWindowResize,
  MPNewSupportMethodMobile,
  MPSpecialist,
  MPModalPopup
} from '@medpro-libs/libs'
import { Feature } from '@medpro-libs/types'
import { debounce, find, size, sortBy } from 'lodash'
import { GetServerSidePropsContext } from 'next'
import { useRouter } from 'next/router'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useQuery } from 'react-query'
import { useDispatch } from 'react-redux'
import client from '../config/medproSdk'
import { newDefaultLayout } from '../layout'
import SEOHead from '../src/components/SEOHead'
import FeatureDesktopSeleketon from '../src/components/pages/Home/selekon/FeatureDesktopSeleketon'
import FeatureSeleketon from '../src/components/pages/Home/selekon/FeatureSeleketon'
import NewsLoading from '../src/components/pages/Home/selekon/NewsLoading'
import {
  QR,
  dataModalDownload,
  downloadApp
} from '../src/components/pages/Home/staticData'
import Radar from 'radar-sdk-js'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { NextPageWithLayout, PageProps } from '../src/type'
import { bookingActions, selectFeature } from '../store/booking/slice'
import {
  featureActions,
  fetchFeatureListByPartner
} from '../store/feature/slice'
import { useAppSelector } from '../store/hooks'
import { IHospitalExInfo } from '../store/hospital/interface'
import { totalDataActions } from '../store/total/slice'
import { SEOPage, handleSEOTitle } from '../utils/SEOPage'
import { deleteKeyCookie } from '../utils/cookies'
import {
  dataHeaderBg,
  dataHomeInfoMedpro,
  dataInfoBooking,
  dataSliderHome,
  dataVideo,
  defautTraffics
} from '../utils/fakeData/dataNewHome'
import { keyQueries } from '../utils/keyQueries'
import { getSeoPageServer, getTraffics } from '../utils/method'
import {
  SEO_PAGE_PATHNAME,
  SSG_REVALIDATE_SECOND
} from '../utils/utils.contants'
import { getError } from '../utils/utils.error'
import { handleUpdateLinkFromPartner } from '../utils/utils.function'
import { openNotification, showError } from '../utils/utils.notification'
import styles from './index.module.less'
import { Modal, Skeleton } from 'antd'
import { HomePageSectionItem } from '../utils/utils.commonType'
import { currentEnv } from '../config/envs'
import cx from 'classnames'
import { locationActions } from '../store/location/locationSlice'
import { useWindowDimensions } from 'libs/medpro-component-libs/src/lib/hooks/useWindowDimesion'

interface NewHomeProps extends PageProps {
  features: Feature[]
  deliveredHospital: any[]
  news: any[]
  hospitalExtraInfo: IHospitalExInfo
  popupData: any
  meta: any
  clinicReport?: any
  traffics: any
}

declare const window: Window &
  typeof globalThis & {
    dataLayer: any
  }

let timeout
const NewHome: NextPageWithLayout = (props: NewHomeProps) => {
  const {
    appInfo: { appId },
    partnerInfoApp: partnerInfo,
    traffics
  } = props

  const router = useRouter()
  const behavior = router.query?.behavior
  const dispatch = useDispatch()
  const [isShowPopup, setIsShowPopup] = useState(true)
  const [searching, setSearching] = useState<any>(false)
  const [loading, setLoading] = useState<any>(true)
  const [searchData, setSearchData] = useState<any>({})
  const [homePageSection, setHomePageSection] =
    useState<HomePageSectionItem>(undefined)
  const [hospitalExtraInfo, setHospitalExtraInfo] = useState<IHospitalExInfo>()
  const [enableGetPopup, setEnableGetPopup] = useState(false)
  const [features, setFeatures] = useState<Feature[]>([])
  const [warningBooking, setWarningBooking] = useState({
    status: false,
    message: ''
  })
  const { windowWidth } = useWindowDimensions()
  const locationData = useAppSelector((s) => s.location.data)
  const news = useAppSelector((s) => s.total.news)
  const meta = find(SEOPage, {
    key: 'trang-chu'
  })

  // Tạo ref cho vùng download section để redierect tới khi có query = downloadApp
  const isDownloadApp = router.asPath.split('/')[1]
  const downloadAppSectionRef = useRef<HTMLDivElement>(null)

  const {
    data: _PopupQuery,
    isFetched: _PopupQuery_Fetched,
    isError: _PopupQuery_Error
  } = useQuery(
    [keyQueries.popup],
    async () => {
      const { data } = await client.partnerConfig.getPopup({
        platform: windowWidth > 576 ? 'pc' : 'mobile'
      })
      return data
    },
    {
      retry: false,
      enabled: enableGetPopup
    }
  )
  const getDataHompage = async () => {
    try {
      const [
        { data: _listData },
        { data: _featureInApp },
        { data: _hospitalExtraInfo }
      ] = await Promise.all([
        client.homePageGet.getListHomePage({}),
        client.appId.getFeatureInApp({
          appid: 'medpro'
        }),
        client.partner.getExtraInfo({ partnerid: 'medpro' })
      ])
      dispatch(featureActions.setFeatureListByPartner(_featureInApp))
      dispatch(totalDataActions.setExtraInfo(_hospitalExtraInfo))
      dispatch(totalDataActions.setCashBack(_listData.ads))
      setHomePageSection(_listData)
      setFeatures(sortBy(_featureInApp, ['priority']))
      setHospitalExtraInfo(_hospitalExtraInfo)
    } catch (err) {
      setHomePageSection(undefined)
      dispatch(featureActions.setFeatureListByPartner([]))
      setHospitalExtraInfo({})
      showError(err)
    } finally {
      setSearching(false)
      setLoading(false)
    }
  }

  useEffect(() => {
    getDataHompage()
    dispatch(totalDataActions.getNews({ appId }))
    setEnableGetPopup(sessionStorage.getItem('showModalDownloadApp') !== 'true')
  }, [])

  useEffect(() => {
    if (isDownloadApp === '#download') {
      setIsShowPopup(false)
      // run this function from an event handler or an effect to execute scroll
      downloadAppSectionRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  })

  const onSearchDebounce = useCallback(
    async (event: any) => {
      clearTimeout(timeout)
      const kw = event.target.value
      setSearching(true)
      void onSearchHistory(kw)
    },
    [router.query]
  )
  const onSearchHistory = useCallback(
    debounce(async (kw) => {
      try {
        const { data } = await client.searchService.search(
          {
            search_key: kw,
            category: 'all',
            offset: 0,
            limit: 3,
            latitude: locationData?.latitude,
            longitude: locationData?.longitude
          },
          { appid: '', partnerid: '' }
        )
        const newData = data.reduce((c, i) => {
          c[i.category] = i.results
          return c
        }, {})
        return setSearchData(newData)
      } catch (err) {
        setSearchData({})
        showError(err)
      } finally {
        clearTimeout(timeout)
        setSearching(false)
      }
    }, 500),
    [setSearchData, setSearching]
  )

  const onSelectFeature = (data: any) => {
    dispatch(selectFeature(data?.feature))
    if (data?.feature?.customUrl) {
      window.open(data?.feature?.customUrl, '_blank')
    } else if (data?.feature?.slug === 'dat-kham-chuyen-khoa') {
      router.push(`/dich-vu-y-te/${data?.feature?.slug}`)
    } else {
      router.push(
        PageRoutesV2.hospitalList({
          appFeatureSlug: data?.feature?.slug || data?.feature?.id
        })
      )
    }
    window.dataLayer.push({
      event: 'Click Feature Đặt Khám',
      Action: 'Click',
      Category: 'Button-Action',
      Label: 'Đồng ý',
      Event: data?.feature?.name,
      PartnerId: 'medpro'
    })
  }

  // Check nếu có link trả về từ partnerInfo thì gán lại
  const downloadAppUponPartnerInfo = handleUpdateLinkFromPartner(
    downloadApp,
    partnerInfo
  )

  const handleBooking = async (type, item) => {
    windowWidth >= 576
      ? await dispatch(bookingActions.handleBookingCta({ type, item }))
      : await dispatch(
          bookingActions.handleBookingCta({ type: 'bookingApp', item })
        )
  }

  const handleDetail = (pathname, query) => {
    if (behavior === 'AnKhang') {
      return
    }
    router.push({
      pathname: pathname,
      query: query
    })
  }

  const handleViewAll = (pathname, query) => {
    router.push({
      pathname: pathname,
      query: {
        ...query,
        ...(behavior && { behavior })
      }
    })
  }

  const handleBookingSearch = async ({ type, item }: any) => {
    if (item.category === 'doctor' && item?.description?.disabled) {
      setWarningBooking((preState) => ({
        ...preState,
        status: true,
        message: item.description.message
      }))
    } else {
      windowWidth >= 576
        ? await dispatch(bookingActions.handleBookingCta({ type, item }))
        : await dispatch(
            bookingActions.handleBookingCta({ type: 'bookingApp', item })
          )
    }
  }

  const renderFeature = () => {
    return (
      <>
        <MPContainer medproSeo className={styles['mobileFeatures']}>
          <MPFeatureMobile
            data={features}
            handleClick={onSelectFeature}
            limit={6}
          />
        </MPContainer>
        <MPContainer medproSeo className={styles['service']}>
          <MPNewServiceHeader
            data={features}
            handleClick={onSelectFeature}
            limit={7}
          />
        </MPContainer>
      </>
    )
  }

  const SkeletonLoader = () => {
    const isMobile = windowWidth < 576
    return (
      <div className={styles['skeleton']}>
        <Skeleton.Node
          children=''
          style={
            isMobile
              ? { width: 168, height: 260, borderRadius: 8 }
              : { width: 299, height: 385, borderRadius: 12 }
          }
        />
        <Skeleton.Node
          children=''
          style={
            isMobile
              ? { width: 168, height: 260, borderRadius: 8 }
              : { width: 299, height: 385, borderRadius: 12 }
          }
        />
        <Skeleton.Node
          children=''
          style={
            isMobile
              ? { width: 168, height: 260, borderRadius: 8 }
              : { width: 299, height: 385, borderRadius: 12 }
          }
        />
        <Skeleton.Node
          children=''
          style={
            isMobile
              ? { width: 168, height: 260, borderRadius: 8 }
              : { width: 299, height: 385, borderRadius: 12 }
          }
        />
      </div>
    )
  }

  const handleDetectLocation = () => {
    Radar.initialize(currentEnv.RADA_KEY)
    Radar.getLocation()
      .then(async (result: any) => {
        const response = await Radar.reverseGeocode({
          latitude: result.latitude,
          longitude: result.longitude
        })
        dispatch(
          locationActions.setUserLocation({
            ...result,
            ...response?.addresses[0]
          })
        )
      })
      .catch((err) => {
        let errorMessage = 'Đã xảy ra lỗi không xác định'
        if (err?.message?.includes('permissions')) {
          errorMessage = 'Medpro không được phép sử dụng vị trí của bạn.'
        }
        openNotification(
          'info',
          {
            message: (
              <div>
                <ExclamationCircleOutlined /> <span>Thông báo</span>
              </div>
            ),
            description: errorMessage,
            placement: 'topLeft'
          },
          'primary'
        )
      })
  }

  return (
    <>
      <div className={cx(styles['main'], styles['start'])}>
        <div className={styles['header']}>
          <MPNewBannerHeader
            data={dataHeaderBg}
            onSearchDebounce={onSearchDebounce}
            handleBookingSearch={handleBookingSearch}
            handleDetectLocation={handleDetectLocation}
            locationData={locationData}
            locationType={'banner'}
            searchData={searchData}
            searching={searching}
          />
          <div style={{ zIndex: 10 }}>
            {loading ? <FeatureSeleketon /> : renderFeature()}
          </div>
        </div>

        <MPContainer medproSeo className={styles['homeContainer']}>
          <MPCooperated
            data={homePageSection?.partner?.data}
            loading={loading}
            isMobile={windowWidth < 576}
            hidden={homePageSection?.partner?.data?.length === 0}
          />
          <MPCarouselBanner
            isMobile={windowWidth < 576}
            data={homePageSection?.banners?.data}
            hidden={homePageSection?.partner?.data?.length === 0}
          />
        </MPContainer>
      </div>
      <div className={styles['Rectangle3462']} />
      <div className={cx(styles['main'], styles['main-ecomerce'])}>
        <MPContainer
          medproSeo
          className={cx(styles['homeContainer'], styles['ecomerce'])}
        >
          <MPNewPackageMonth
            isMobile={windowWidth < 576}
            data={homePageSection?.hospitals?.data}
            handleBooking={handleBooking}
            hidden={homePageSection?.hospitals?.data?.length === 0}
            skeleton={SkeletonLoader}
            handleDetail={handleDetail}
            handleViewAll={handleViewAll}
          />
        </MPContainer>
      </div>
      <div className={cx(styles['main'])}>
        <div className={styles['Rectangle4123']} />
        <MPContainer medproSeo className={styles['homeContainer']}>
          <MPCarouselBannerMulti
            isMobile={windowWidth < 576}
            data={homePageSection?.bannersMulti?.data}
            hidden={homePageSection?.bannersMulti?.data?.length === 0}
          />
        </MPContainer>
        <div className={styles['Rectangle3462']} />
      </div>
      <div className={cx(styles['main'], styles['main-ecomerce'])}>
        <MPContainer
          medproSeo
          className={cx(styles['homeContainer'], styles['ecomerce'])}
        >
          <MPDoctorTelemed
            isMobile={windowWidth < 576}
            data={homePageSection?.telemed_doctor_in_month?.data}
            handleBooking={handleBooking}
            hidden={
              homePageSection?.telemed_doctor_in_month?.data?.length === 0
            }
            skeleton={SkeletonLoader}
            handleDetail={handleDetail}
            handleViewAll={handleViewAll}
          />
          <MPNewBookingPackage
            isMobile={windowWidth < 576}
            data={homePageSection?.service_in_month?.data}
            handleBooking={handleBooking}
            hidden={homePageSection?.service_in_month?.data?.length === 0}
            skeleton={SkeletonLoader}
            handleDetail={handleDetail}
            handleViewAll={handleViewAll}
          />
        </MPContainer>
      </div>
      <div className={styles['Rectangle4123']} />
      <div className={cx(styles['main'], styles['end'])}>
        {homePageSection && (
          <MPContainer medproSeo className={styles['homeContainer']}>
            <MPSpecialist
              isMobile={windowWidth < 576}
              data={homePageSection?.subjects}
            />
          </MPContainer>
        )}
        {/* Tải ứng dụng MEDPRO */}
        <div className={styles['section_download']}>
          <MPContainer className={styles['dowloadApp']}>
            <MPNewHomeDownload
              isMobile={windowWidth < 576}
              data={{
                downloadApp: {
                  android: downloadAppUponPartnerInfo?.android?.link,
                  ios: downloadAppUponPartnerInfo?.ios?.link
                },
                downloadAppSectionRef,
                ...hospitalExtraInfo
              }}
            />
          </MPContainer>
        </div>
        {/*  Cảm nhận từ khách hàng  */}
        {homePageSection && (
          <MPContainer className={styles['homeContainer']}>
            <MPFeelPeople
              isMobile={windowWidth < 576}
              data={homePageSection?.testimonials?.data}
              hidden={homePageSection?.testimonials?.data?.length === 0}
            />
          </MPContainer>
        )}
        <MPContainer className={styles['Achievement']}>
          <MPNewAchievement data={dataVideo} />
        </MPContainer>
        {/* Thống kê */}
        <div className={styles['Rectangle3462']}></div>
        <div style={{ background: '#E8F4FD' }}>
          <div className={styles['homeStatistic']}>
            <MPNewStatistic
              data={typeof traffics === 'object' ? traffics : defautTraffics}
            />
          </div>
          <div className={styles['homeContainer']}>
            {news.loading ? (
              <NewsLoading />
            ) : (
              <MPNewNews isMobile={windowWidth < 576} data={news.data} />
            )}
            <div className={styles['supportMP']}>
              {windowWidth < 576 ? (
                <MPNewSupportMethodMobile />
              ) : (
                <MPNewSupportMethod qr={QR} />
              )}
            </div>
          </div>
        </div>

        {!_PopupQuery_Error ? (
          _PopupQuery_Fetched &&
          isShowPopup &&
          appId === 'medpro' && (
            <MPModalPopup {..._PopupQuery} handleBooking={handleBooking} />
          )
        ) : (
          <></>
        )}

        {warningBooking.status && (
          <Modal
            title={'Thông báo'}
            open={warningBooking.status}
            footer={null}
            centered
            onCancel={() =>
              setWarningBooking((preState) => ({
                ...preState,
                status: false,
                message: ''
              }))
            }
            className={styles['modal']}
          >
            <div
              className={styles['description']}
              dangerouslySetInnerHTML={{
                __html: warningBooking.message
              }}
            />
          </Modal>
        )}
      </div>
    </>
  )
}

export async function getStaticProps(context: GetServerSidePropsContext) {
  const startTime = new Date()
  let props = {}
  const appId = 'medpro'

  deleteKeyCookie('partnerId')

  try {
    const traffics = await getTraffics({})
    props = {
      traffics,
      executionTime: new Date().getTime() - startTime.getTime()
    }
  } catch (error) {
    console.log('error get server side props failed: ', error)
    props = {
      error: getError(error, {
        reason: 'get server side props failed!'
      }),
      news: [],
      features: [],
      executionTime: new Date().getTime() - startTime.getTime()
    }
  }

  return { props, revalidate: SSG_REVALIDATE_SECOND }
}

NewHome.displayName = 'HomePage'
NewHome.ssr = true
NewHome.getLayout = newDefaultLayout
NewHome.breadcrumb = []

export default NewHome
