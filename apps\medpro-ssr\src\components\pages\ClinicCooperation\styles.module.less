.wrapper {
  padding: 0 120px;
  background: #ffffff;

  @media only screen and (max-width: 1024px) {
    padding: 0 35px;
  }

  @media only screen and (max-width: 991px) {
    padding: 0 15px;
  }

  .titleWrapper {
    color: #292e35;
  }
}

.canvasBanner {
  position: relative;
  background: #d8f4ff;
}

.banner {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  width: 100%;
  min-height: 640px;

  @media (max-width: 991px) {
    align-items: unset;
    flex-direction: column;
  }

  .captionContent {
    max-width: 700px;
    padding-left: 112px;
    color: #292e35;
    position: absolute;
    z-index: 9;
    top: 25%;

    @media (max-width: 1024px) {
      padding-left: 56px;
    }

    @media (max-width: 991px) {
      position: relative;
      padding-left: 16px;
      padding-right: 16px;
      padding-top: 40px;
      margin-bottom: 24px;
    }

    h1 {
      font-weight: 700;
      font-size: 52px;
      line-height: 60px;
      text-transform: capitalize;
      margin-bottom: 24px;

      @media (max-width: 991px) {
        font-size: 29px;
        line-height: 40px;
        text-transform: uppercase;
        margin-bottom: 12px;
      }
    }

    .captionText {
      p {
        margin-bottom: 0;
        font-weight: 400;
        font-size: 20px;
        line-height: 24px;
        color: #292e35;

        @media (max-width: 991px) {
          font-size: 16px;
        }
      }
    }

    .btnContact {
      margin-top: 44px;
      width: 180px;
      min-height: 56px;
      padding: 12px;
      border-radius: 30px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      gap: 5px;
      background: linear-gradient(86.64deg, #00b5f1 33.96%, #00e0ff 137.73%);
      background-size: 200% 100%;
      transition: background-position 0.5s ease-in-out;

      @media (max-width: 991px) {
        margin-top: 24px;
      }

      &:hover {
        background-position: -100% 0;
      }

      span {
        font-weight: 600;
        font-size: 16px;
        line-height: 100%;
      }

      .btnIcon {
        display: flex;
        margin-bottom: 2px;
      }
    }
  }

  .captionImg {
    flex: 1 0 auto;
    display: flex;
    justify-content: flex-end;
  }
}

.aboutUsTitle {
  padding-top: 120px;

  @media only screen and (max-width: 1024px) {
    padding-top: 40px;
  }

  &.titleWrapper {
    text-align: center;

    h3 {
      font-weight: 700;
      font-size: 32px;
      line-height: 50px;
      text-transform: capitalize;
      margin-bottom: 64px;

      @media only screen and (max-width: 1024px) {
        line-height: 40px;
        vertical-align: middle;
        text-transform: capitalize;
        margin-bottom: 40px;
      }
    }
  }
}

.aboutUs {
  padding-bottom: 120px;
  max-width: 1220px;
  margin: 0 auto;

  @media only screen and (max-width: 1024px) {
    padding-bottom: 40px;
  }

  @media only screen and (max-width: 1000px) {
    max-width: 1200px;
    margin: 0 auto;
    overflow: auto;
    scroll-behavior: smooth;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .cards {
    display: flex;
    gap: 16px;
    align-items: center;
    padding: 15px;

    @media only screen and (max-width: 1000px) {
      min-width: 1200px;
      overflow: auto;
      padding: 5px 15px;
    }

    :global {
      .slick-track {
        display: flex;
        gap: 15px;
        padding-bottom: 10px;
        padding-top: 10px;
      }
    }

    .card {
      width: 290px;
      height: 290px;
      border-radius: 12px;
      border: 1.5px solid #f2f2f2;
      box-shadow: 0 2px 2px rgba(0, 0, 0, 0.1), 0 2px 5px rgba(0, 0, 0, 0.05);
      padding: 38px 20px;
      background: #ffffff;
      transition: transform 0.5s ease;
      cursor: pointer;

      &:hover {
        transform: scale(1.05);
        background: #25b2e7 url('/images/business/bgCardAboutUs.png');

        .logo {
          animation: wiggle 0.5s ease-in-out;
        }

        .content {
          color: #ffffff;
        }
      }

      .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24px;
      }

      .content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        color: #292e35;

        .label {
          font-weight: 700;
          font-size: 20px;
          line-height: 32px;
          vertical-align: middle;
        }

        .paraphrase {
          font-weight: 500;
          font-size: 16px;
          line-height: 24px;
          text-align: center;
        }
      }
    }

    .btnPrev,
    .btnNext {
      position: absolute;
      top: 50% !important;
      transform: translateY(-50%);
      width: 35px !important;
      height: 35px !important;
      border-radius: 50%;
      background: #fff !important;
      box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.04), 0 2px 6px 0 rgba(0, 0, 0, 0.04),
        0 10px 20px 0 rgba(0, 0, 0, 0.04);
      backdrop-filter: blur(5px);
      display: flex !important;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      z-index: 10;

      @media only screen and (max-width: 1024px) {
        display: none !important;
      }

      &:hover {
        background: #ffffff;
      }

      &:before {
        content: '';
      }
    }

    .btnNext {
      right: 5px !important;
    }

    .btnPrev {
      left: 5px !important;
    }
  }
}

.solution {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding-top: 80px;
  padding-bottom: 112px;

  @media only screen and (max-width: 1024px) {
    padding-top: 72px;
    padding-left: 50px;
    padding-bottom: 68px;
  }

  @media only screen and (max-width: 991px) {
    padding-left: 15px;
    padding-right: 15px;
  }

  .titleWrapper {
    text-align: center;

    h3 {
      font-weight: 700;
      font-size: 32px;
      line-height: 50px;
      text-transform: capitalize;
      margin-bottom: 64px;

      @media only screen and (max-width: 1024px) {
        line-height: 40px;
        vertical-align: middle;
        margin-bottom: 40px;
      }
    }
  }

  .steps {
    .step {
      display: grid;
      grid-template-columns: 1fr 1fr;
      align-items: center;
      gap: 30px;
      min-height: 360px;

      @media only screen and (max-width: 991px) {
        grid-template-columns: 1fr;
        gap: 24px;
        margin-bottom: 80px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      &.textRightToLeft {
        direction: rtl;

        .textWrapper {
          direction: ltr;

          &:after {
            display: none;
          }
        }
      }

      .textWrapper {
        position: relative;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;

        @media only screen and (max-width: 991px) {
          margin: 0 auto;
        }

        &:before {
          content: '';
          position: absolute;
          top: 0;
          left: -30px;
          height: 100%;
          border-left: 2px dashed #000;

          @media only screen and (max-width: 991px) {
            display: none;
          }
        }

        &:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: -30px;
          width: calc(100% + 30px);
          border-bottom: 2px dashed #000;

          @media only screen and (max-width: 991px) {
            display: none;
          }
        }

        .lineTop {
          position: absolute;
          width: calc(100% + 30px);
          border-top: 2px dashed #000;
          left: -30px;
          top: 0;

          @media only screen and (max-width: 991px) {
            display: none;
          }
        }

        .title {
          position: relative;

          .number {
            background-color: #01b5f1;
            color: white;
            font-size: 18px;
            font-weight: bold;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            position: absolute;
            left: -50px;
            bottom: 0;
            z-index: 9;

            &.numberDesk {
              @media only screen and (max-width: 991px) {
                display: none;
              }
            }

            &.numberMobi {
              display: none;

              @media only screen and (max-width: 991px) {
                display: flex;
                align-items: flex-end;
                justify-content: center;
                position: unset;
                margin-right: 8px;
                flex: 1 0 auto;
                max-width: 40px;
              }

              @media only screen and (max-width: 576px) {
                max-width: 32px;
                max-height: 32px;
                font-size: 16px;
              }
            }
          }

          .titleText {
            font-weight: 700;
            font-size: 24px;
            line-height: 128%;
            color: #141513;

            @media only screen and (max-width: 991px) {
              display: flex;
              align-items: flex-start;
            }
          }
        }

        .itemList {
          width: 100%;
          max-width: 510px;
          margin-top: 20px;

          .item {
            display: flex;
            margin-bottom: 15px;
            gap: 8px;

            &:last-child {
              margin-bottom: 0;
            }

            .icon {
              flex: 1 0 auto;
              width: 20px;
              max-width: 20px;
              height: 20px;
              max-height: 20px;
            }

            .text {
              font-weight: 500;
              font-size: 16px;
              line-height: 24px;
              color: #141513;
            }

            .btnQuote {
              min-width: 190px;
              min-height: 48px;
              padding: 12px;
              border-radius: 30px;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              gap: 5px;
              background: linear-gradient(
                86.64deg,
                #00b5f1 33.96%,
                #00e0ff 137.73%
              );
              background-size: 200% 100%;
              transition: background-position 0.5s ease-in-out;

              &:hover {
                background-position: -100% 0;
              }

              span {
                font-weight: 600;
                font-size: 16px;
                line-height: 100%;
              }

              .btnIcon {
                display: flex;
                margin-bottom: 2px;
              }
            }
          }
        }
      }

      .imgWrapper {
        max-width: 480px;
        display: flex;
        margin: 0 20px 0 auto;

        @media only screen and (max-width: 991px) {
          margin: 0 auto;
        }
      }
    }
  }
}

.quote {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 740px;

  .card {
    display: flex;
    justify-content: center;
    width: 100%;
    max-width: 1000px;
    height: 100%;
    min-height: 580px;

    @media only screen and (max-width: 768px) {
      flex-direction: column;
      padding-top: 40px;
      padding-bottom: 40px;
    }

    .left {
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      width: 45%;
      max-width: 420px;
      padding: 24px;
      background: #01b5f1;
      color: white;
      border-bottom-left-radius: 16px;
      border-top-left-radius: 16px;

      @media only screen and (max-width: 768px) {
        width: 100%;
        max-width: unset;
        border-bottom-left-radius: 0;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
        gap: 24px;
      }

      .captionText {
        h2 {
          color: white;
          font-weight: 700;
          font-size: 32px;
          line-height: 50px;
          vertical-align: middle;
          text-transform: capitalize;
          margin-bottom: 16px;

          @media only screen and (max-width: 768px) {
            // font-size: 26px;
            line-height: 40px;
          }
        }

        p {
          margin-bottom: 0;
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          vertical-align: middle;
        }
      }

      .captionImg {
        img {
          border-radius: 8px;
        }
      }
    }

    .right {
      width: 55%;
      background: white;
      padding: 28px;
      border-bottom-right-radius: 16px;
      border-top-right-radius: 16px;

      @media only screen and (max-width: 768px) {
        width: 100%;
        border-bottom-right-radius: 16px;
        border-top-right-radius: 0;
        border-bottom-left-radius: 16px;
      }

      .formCard {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;

        :global {
          .ant-form-item {
            &:last-child {
              margin-bottom: 0;
            }

            .ant-form-item-label > label {
              height: auto;
              font-size: 16px;
              font-weight: 700;
              line-height: 19.36px;
              letter-spacing: 0.02em;
              flex-direction: row-reverse;
            }

            .ant-input {
              border-radius: 10px;
            }

            .ant-select-selector {
              border-radius: 10px;
            }

            .ant-input-number {
              width: 100%;
              border-radius: 10px;

              .ant-input-number-handler-wrap {
                display: none;
              }

              input {
                padding: 6.5px 11px;
              }
            }
          }
        }

        .groupFormItem {
          display: flex;
          gap: 16px;

          @media only screen and (max-width: 768px) {
            flex-direction: column;
            gap: 0;
          }

          :global {
            .ant-form-item {
              flex: 1;

              &:last-child {
                @media only screen and (max-width: 768px) {
                  margin-bottom: 24px;
                }
              }
            }
          }
        }

        .btnContactForm {
          width: 100%;
          height: 48px;
          padding: 15px 36px 15px 36px;
          border-radius: 44px;
          gap: 5px;

          font-size: 16px;
          color: #fff;
          background: linear-gradient(
            86.64deg,
            #01b5f1 33.96%,
            #0084e9 137.73%
          );
          background-size: 200% 100%;
          transition: background-position 0.5s ease-in-out;
          &:hover {
            background-position: -100% 0;
          }
        }
      }
    }
  }
}

.statisticalContainer {
  padding: 92px 112px;

  @media only screen and (max-width: 1244px) {
    padding-left: 15px;
    padding-right: 15px;
  }

  @media only screen and (max-width: 991px) {
    padding-bottom: 40px;
    padding-top: 40px;
  }

  .statisticalWrapper {
    max-width: 1300px;
    margin: 0 auto;

    .title {
      font-size: 32px;
      font-weight: 700;
      line-height: 38.73px;
      letter-spacing: 0.02em;
      text-align: center;
      margin-bottom: 64px;
      color: #ffffff;
    }

    .cardWrapper {
      position: relative;
      display: flex;
      justify-content: space-between;
      gap: 8px;

      @media only screen and (max-width: 867px) {
        gap: 20px;
        flex-wrap: wrap;
      }

      @media only screen and (max-width: 450px) {
        justify-content: center;
      }

      .statItem {
        color: #ffffff;
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;

        @media only screen and (max-width: 867px) {
          width: 300px;
        }

        @media only screen and (max-width: 667px) {
          width: 200px;
        }

        h2 {
          font-size: 40px;
          font-weight: 700;
          line-height: 48.41px;
          letter-spacing: 0.02em;
          text-align: center;
          color: #ffffff;
          margin-bottom: 0;
        }

        p {
          font-size: 16px;
          font-weight: 600;
          line-height: 20px;
          letter-spacing: 0.02em;
          text-align: center;
        }
      }

      .divider {
        width: 1px;
        background-color: rgba(255, 255, 255, 0.5);

        @media only screen and (max-width: 867px) {
          display: none;
        }
      }
    }
  }
}

.highlightTitle {
  text-align: center;
  padding-top: 80px;

  @media (max-width: 991px) {
    padding-top: 40px;
  }

  h3 {
    font-weight: 700;
    font-size: 32px;
    line-height: 50px;
    margin-bottom: 0;
    color: #292e35;
  }
}

.highlight {
  padding-top: 80px;
  min-height: 880px;
  height: 100%;
  width: 100%;
  max-width: 1140px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 84px;

  @media (max-width: 991px) {
    min-height: unset;
    padding-top: 70px;
  }

  .tableWrapper {
    width: 100%;
    background: linear-gradient(to bottom, #0099cc, #00a8e8);
    border-radius: 20px;
    padding: 50px 15px 15px 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

    @media (max-width: 991px) {
      overflow: auto;
      -webkit-overflow-scrolling: touch;

      &::-webkit-scrollbar {
        display: block;
      }
    }

    @media (max-width: 576px) {
      padding-top: 0;
    }

    .comparisonContainer {
      position: relative;

      @media (max-width: 475px) {
        width: 475px;
      }

      .headerRow {
        display: flex;
        width: 100%;
        position: absolute;
        top: -88px;

        @media (max-width: 991px) {
          position: unset;
        }

        .headerCell {
          padding: 30px 15px;
          color: white;
          font-weight: 700;
          font-size: 20px;
          line-height: 28px;
          text-align: center;
          vertical-align: middle;

          @media (max-width: 991px) {
            padding: 12px 10px;
            font-size: 14px;
            line-height: 20px;
          }

          &.empty {
            width: 20%;
            background-color: transparent;
            border-top-left-radius: 15px;

            @media (max-width: 991px) {
              width: 20.66666%;
            }

            @media (max-width: 475px) {
              width: 21.33333%;
            }
          }

          &.selfContact {
            width: 37.5%;
            background-color: #ff7a7a;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-top-left-radius: 15px;
            border-top-right-radius: 15px;

            @media (max-width: 991px) {
              border-top-left-radius: 0;
              border-top-right-radius: 0;
            }
          }

          &.medpro {
            width: 37.5%;
            background-color: #00a8e8;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-top-left-radius: 15px;
            border-top-right-radius: 15px;

            @media (max-width: 991px) {
              border-top-left-radius: 0;
              border-top-right-radius: 0;
            }
          }

          &.dataEmpty {
            width: 5%;
            background-color: transparent;

            @media (max-width: 991px) {
              width: 5.88888%;
            }

            @media (max-width: 575px) {
              width: 6.88888%;
            }
          }
        }
      }

      .contentRow {
        display: flex;
        width: 100%;
        background-color: #ffffff;
        border-bottom: 1px solid #d6f1fd;
        min-height: 115px;

        @media (max-width: 991px) {
          min-height: unset;
        }

        &:nth-child(2) {
          border-top-left-radius: 20px;
          border-top-right-radius: 20px;
        }

        &:last-child {
          border-bottom: none;
          border-bottom-left-radius: 20px;
          border-bottom-right-radius: 20px;
        }

        &:nth-child(even) {
          background-color: #eff7fe;
        }

        &:nth-child(old) {
          background-color: #ffffff;
        }

        .contentCell {
          padding: 15px;
          display: flex;
          align-items: center;
          font-weight: 500;
          font-size: 16px;
          line-height: 24px;
          text-align: center;
          vertical-align: middle;

          @media (max-width: 991px) {
            font-weight: 500;
            font-size: 12px;
            line-height: 16px;
          }

          &.category {
            width: 30%;
            font-weight: 700;
            padding-left: 30px;
            padding-right: 30px;
            text-align: left;
            margin: autor;
            justify-content: flex-start;

            @media (max-width: 991px) {
              padding: 15px 8px !important;
            }
          }

          &.dataOne {
            width: 58%;
            padding-left: 30px;
            padding-right: 30px;
            text-align: center;
            justify-content: center;
            box-shadow: -8px 0 8px 4px #60606014;
            border-right: 1px solid #d6f1fd;
          }

          &.dataTwo {
            width: 58%;
            text-align: center;
            justify-content: center;
            box-shadow: 8px 0 8px 0 #60606014;
          }

          &.dataOne,
          &.dataTwo {
            @media (max-width: 991px) {
              text-align: left;
              justify-content: flex-start;
              padding: 15px !important;
            }
          }

          &.dataEmpty {
            width: 6.33333%;
          }
        }
      }
    }
  }
}

.partner {
  padding-top: 55px;

  .titleWrapper {
    text-align: center;
    margin-bottom: 48px;

    h3 {
      font-weight: 700;
      font-size: 32px;
      line-height: 50px;
      text-align: center;
      text-transform: capitalize;
      color: #ffffff;
    }
  }

  .slidesWrapper {
    :global {
      .slick-track {
        display: flex;
        gap: 15px;
      }

      .slick-dots {
        li {
          margin: 2px;

          button {
            &:before {
              font-size: 9px;
              color: #ffffff;
            }
          }
        }
      }
    }

    .slideItem {
      width: 100%;
      max-width: 310px;
      height: 100px;
      border-radius: 12px;
      background: #ffffff;
      display: flex !important;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
    }
  }
}

.partnerContainer {
  font-family: 'Roboto', sans-serif !important;
  padding: 60px 73px 0;
  background-color: #f1f8ff;

  @media (max-width: 768px) {
    padding: 40px 16px 0;
  }

  .header {
    text-align: center;

    .title {
      font-weight: 700;
      font-size: 32px;
      line-height: 50px;
      text-align: center;
      text-transform: capitalize;
      margin-bottom: 10px;

      @media (max-width: 768px) {
        margin-bottom: 24px;
      }
    }
  }

  .listLogo {
    margin-top: 50px;
    padding: 0 36px;

    @media (max-width: 576px) {
      padding: 0 16px;
      margin-top: 16px;
      white-space: nowrap;
    }

    :global {
      .ant-row {
        @media (max-width: 576px) {
          column-gap: 0 !important;
          row-gap: 12px !important;
        }
      }
    }

    .logoSlider {
      width: 100%;
      height: 160px;
      border-radius: 12px;
      display: flex !important;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
      @media (max-width: 576px) {
        padding: 0 10px;
      }

      :global {
        .slick-slide {
          padding: 0 8px;
        }

        .slick-dots {
          bottom: -25px;

          li {
            margin: 0 2px;

            button {
              background: #d9d9d9;
              width: 8px;
              height: 8px;
              border-radius: 50%;
              padding: 0;

              &:before {
                display: none;
              }
            }

            &.slick-active {
              button {
                background: #01b5f1;
              }
            }
          }
        }

        .slick-arrow {
          width: 30px !important;
          height: 30px !important;
          border-radius: 50%;
          background: rgba(0, 181, 241, 0.1);
          display: flex !important;
          align-items: center;
          justify-content: center;
          z-index: 2;

          &:before {
            display: none;
          }

          &.slick-prev {
            left: -15px;
          }

          &.slick-next {
            right: -15px;
          }
        }
      }

      .logoItem {
        display: flex !important;
        height: 40px;
        position: relative;
        margin: 0 auto;
      }
    }

    .logoItem {
      height: 52.47px;
      align-items: center !important;
      display: inline-block;
      justify-content: center;
      @media (max-width: 576px) {
        display: inline-block;
        justify-items: center;
        height: 25px;
      }
    }

    > span {
      position: unset !important;
    }

    .imageLogo {
      width: 100% !important;
      object-fit: contain;
      max-height: 52.47px !important;
      position: unset !important;
      max-width: 100%;
    }
  }

  .slider {
    @media (max-width: 576px) {
      margin-top: 20px;
    }
    margin-top: 64px;
    width: 100%;

    :global {
      .slick-arrow {
        @media (max-width: 576px) {
          width: 36px !important;
        }
        position: absolute;
        height: 100%;
        width: 140px;
        border: none;
        z-index: 2;
        background: transparent;

        &.slick-prev {
          @media (max-width: 992px) {
            left: -55px;
          }
          @media (max-width: 576px) {
            left: -15px;
          }
          left: 0;
        }

        &.slick-next {
          @media (max-width: 992px) {
            right: -55px;
          }
          @media (max-width: 576px) {
            right: -15px;
          }
          right: 0;
        }
      }
    }

    .item {
      @media (max-width: 992px) {
        padding: 0;
        aspect-ratio: 16/9;
        height: fit-content;
        scale: 0.9;
      }
      padding: 0 142px;
      height: 413px;
      width: 100%;
      aspect-ratio: 16/9;

      iframe {
        height: 100%;
        width: 100%;
        border-radius: 16px;
      }
    }
  }
}

@keyframes wiggle {
  0%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(5deg);
  }
  50% {
    transform: rotate(-5deg);
  }
  75% {
    transform: rotate(3deg);
  }
}
