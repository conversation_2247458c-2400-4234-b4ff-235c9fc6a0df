/* eslint-disable react-hooks/rules-of-hooks */
import { Col, Divider, Form, Modal, Row, notification } from 'antd'
import moment from 'moment'
import { useEffect, useState } from 'react'
import { PatientFormData } from '../../component/MPCreatePatientFormComponent/types'
import MPButton from '../MPButton'
import { handleAddressFields } from './common/handleAddressFields'
import { handleDetails } from './common/handleDetails'
import { handleRelative } from './common/handleRelative'
import styles from './styles.module.less'
import { matchFullAddress } from '../../common/func'
import IconScanQR from './common/iconScanQR.svg'
import Image from 'next/image'
import cx from 'classnames'
import QRScanner from '../MPCompleteProfileAppCard/common/QRScanner'
import cancelIcon from '../../common/images/cancel_icon.jpg'
import okIcon from '../../common/images/ok_icon.jpg'
import { useWindowDimensions } from '../../hooks/useWindowDimesion'
declare const window: Window &
  typeof globalThis & {
    dataLayer: any
  }
interface Props {
  data: {
    title: string
    description: string
    patient: PatientFormData
    profession: any[]
    nation: any[]
    relative: any[]
    province: any[]
    district: any[]
    ward: any[]
  }
  handleSubmit: any
  handleChangeAddress: any
  submitting?: boolean
  isCSKHApp: boolean
  patientYearOldAccepted?: number
  cleanDistrictsAndWards?: () => void
  partnerId?: string
  userId?: string
  extractInformation?: (values: any) => Promise<any>
  isCreate?: boolean
}

export const MPCreatePatientFormCard = ({
  data,
  handleSubmit,
  handleChangeAddress,
  submitting,
  patientYearOldAccepted = 16,
  cleanDistrictsAndWards,
  partnerId,
  userId,
  extractInformation,
  isCreate
}: Props) => {
  const patient = data.patient
  const date = new Date()
  const [form] = Form.useForm()
  const { windowWidth } = useWindowDimensions()

  // #region State
  const [confirmAddress, setConfirmAddress] = useState(false)
  const [birthDay, setBirthDay] = useState({
    day: patient?.day,
    month: patient?.month,
    year: patient?.year
  })

  useEffect(() => {
    if (patient) {
      setBirthDay({
        day: patient.day,
        month: patient.month,
        year: patient.year
      })

      form.setFieldsValue(patient)

      if (patient?.dantoc_id === 'medpro_82') {
        setIsForeign(true)
      }
      if (patient?.relation) {
        //Lỗi select không hiện place holder
        if (!patient?.relation?.relative_type_id) {
          form.setFieldValue(['relation', 'relative_type_id'], undefined)
        }
      }
    }
  }, [form, patient])

  const [showRelative, setShowRelative] = useState(false)
  const [age, setAge] = useState(-1)
  const [country, setCountry] = useState<string>(
    data?.patient?.country_code || 'VIE'
  )
  const [isForeign, setIsForeign] = useState(false)
  const [openScanQR, setOpenScanQR] = useState(false)
  const [scanMessage, setScanMessage] = useState<any>(undefined)
  const [isScanMessage, setIsScanMessage] = useState<boolean>(false)
  useEffect(() => {
    const { year, month, day } = birthDay
    let age
    if (month && day) {
      const dateMM = moment({
        day,
        month: month - 1,
        year
      })
      age = moment().diff(dateMM, 'years')
    } else {
      age = moment().year() - year
    }
    setShowRelative(age < patientYearOldAccepted)
    setAge(age)
    if (0 <= age && age < patientYearOldAccepted) {
      form.setFieldValue('profession_id', 'medpro_952')
    } else if (form.getFieldValue('profession_id') === 'medpro_952') {
      form.resetFields(['profession_id'])
    }
  }, [birthDay, form, patientYearOldAccepted])

  function resetForm() {
    form.resetFields()

    // scroll to first field when reset form
    const firstField = [Object.keys(form.getFieldsValue())[0]]
    form.scrollToField(firstField, {
      behavior: (actions) =>
        actions.forEach(({ el, top, left }) => {
          // 100 is height of element
          const adjustedTop = top - 100
          el.scrollTop = adjustedTop
          el.scrollLeft = left
        })
    })
    setBirthDay({ day: 0, month: 0, year: 0 })

    // Syntax này là optional chaining => google
    cleanDistrictsAndWards?.()
  }

  const onValuesChange = (changes: any) => {
    const { day, month, year, city_id, district_id, country_code } = changes

    // Handle birthday changes
    const updateBirthDay = (field: 'day' | 'month' | 'year', value: number) => {
      if (value) {
        setBirthDay((prev) => ({ ...prev, [field]: value }))
      }
    }

    updateBirthDay('year', year)
    updateBirthDay('month', month)
    updateBirthDay('day', day)

    // Handle country code changes
    if (country_code) {
      setCountry(country_code)
      const isVietnam = ['VN', 'VIE'].includes(country_code)

      form.setFieldValue('dantoc_id', isVietnam ? 'medpro_1' : 'medpro_82')
      setIsForeign(!isVietnam)
    }

    // Handle address changes
    if (city_id) {
      handleChangeAddress('district', city_id)

      form.resetFields(['district_id', 'ward_id'])
    }

    if (district_id) {
      handleChangeAddress('ward', district_id)
      form.resetFields(['ward_id'])
    }
  }

  const handleInitFormValues = (userCountry: string) => {
    switch (userCountry) {
      case 'VIE':
        return {
          country_code: 'VIE',
          dantoc_id: 'medpro_1'
        }
      default:
        return {
          country_code: userCountry,
          dantoc_id: 'medpro_82'
        }
    }
  }

  const handleCheckDay = (values: any) => {
    // Kiểm tra năm
    if (Number(values?.year) <= Number(date.getFullYear()))
      if (Number(values?.year) < Number(date.getFullYear())) return true
      // Nếu năm bằng nhau thì kiểm tra tháng
      else if (Number(values?.month) <= Number(date.getMonth()) + 1)
        if (Number(values?.month) < Number(date.getMonth()) + 1) return true
        // Nếu tháng bằng nhau thì kiểm tra ngày
        else if (Number(values?.day) < Number(date.getDate())) return true
        else return false
      else return false
    else return false
  }
  const onFinish = (values: any) => {
    if (handleCheckDay(values)) {
      handleSubmit(values)
    } else {
      notification.error({
        message: 'Ngày sinh không được lớn hơn ngày hiện tại!'
      })
    }
    // setFormValues(values)
    // setConfirmAddress(true)
  }
  const handleConfirmAddress = () => {
    form.submit()
  }

  const toggleConfirmAddress = () => {
    setConfirmAddress((preState) => !preState)
  }

  const onResetFieldDay = () => {
    form.resetFields(['day'])
  }

  const onSendEventTagManager = (e: any) => {
    form
      .validateFields()
      .then(() => {
        if (!patient?.id) {
          window.dataLayer.push({
            event: 'Tạo mới hồ sơ chưa từng khám',
            Action: 'Click',
            Category: 'Button-Action',
            Event: 'Tạo mới hồ sơ chưa từng khám',
            Label: e?.currentTarget?.textContent,
            PartnerId: partnerId,
            UserId: userId
          })
        }
        toggleConfirmAddress()
      })
      .catch((errorInfo) => {
        console.log('Validation failed:', errorInfo)
        const firstErrorField = errorInfo.errorFields[0].name
        form.scrollToField(firstErrorField, {
          behavior: (actions) =>
            actions.forEach(({ el, top, left }) => {
              // 100 is height of element
              const adjustedTop = top - 100
              el.scrollTop = adjustedTop
              el.scrollLeft = left
            })
        })
      })
  }

  const DividerCustomer = (title: string) => {
    return (
      <Divider
        orientation='left'
        plain
        orientationMargin={0}
        className={styles['Divider']}
      >
        {title}
      </Divider>
    )
  }

  const handleClickScanQR = () => {
    setOpenScanQR(true)
  }

  const handleCancelScanQR = () => {
    setOpenScanQR(false)
  }

  const handleCancelScanMessage = () => {
    setIsScanMessage(false)
    setScanMessage(undefined)
  }

  const handleScanSuccess = async (decodedText: any) => {
    setOpenScanQR(false)
    if (!extractInformation) {
      setScanMessage('Chức năng quét mã QR không khả dụng!')
      setIsScanMessage(true)
      return
    }

    const extractInfo = (await extractInformation(decodedText)) as any
    if (extractInfo) {
      // Format birthdate to day, month, year for the form
      const birthParts = extractInfo?.birthdate?.split('/') || []

      onValuesChange({
        city_id: extractInfo?.city_id,
        district_id: extractInfo?.district_id,
        country_code:
          extractInfo?.country_code === 'VIE'
            ? 'VIE'
            : extractInfo?.country_code
      })

      if (birthParts.length === 3) {
        form.setFieldsValue({
          day: Number(birthParts[0]),
          month: Number(birthParts[1]),
          year: Number(birthParts[2]),
          name: extractInfo?.fullname,
          sex: Number(extractInfo?.sex),
          insuranceCode: extractInfo?.insuranceId,
          cmnd: extractInfo?.cmnd,
          country_code:
            extractInfo?.country_code === 'VIE'
              ? 'VIE'
              : extractInfo?.country_code,
          city_id: extractInfo?.city_id,
          district_id: extractInfo?.district_id,
          ward_id: extractInfo?.ward_id,
          address: extractInfo?.address
        })

        // Update birthDay state
        setBirthDay({
          day: Number(birthParts[0]),
          month: Number(birthParts[1]),
          year: Number(birthParts[2])
        })
      }

      // Trigger form change to update derived values
    } else {
      setScanMessage('Mã QR không hợp lệ!')
      setIsScanMessage(true)
    }
  }

  return (
    <div className={styles['createPatientCard']}>
      <div className={styles['desc']}>{data.description}</div>
      <div className={styles['noteRequired']}>(*) Thông tin bắt buộc nhập</div>
      <Form
        form={form}
        layout='vertical'
        initialValues={handleInitFormValues(country)}
        onValuesChange={onValuesChange}
        onFinish={onFinish}
        className={styles['listContact']}
        onReset={resetForm}
      >
        <Row gutter={12}>
          {isCreate && (
            <div className={cx([styles['scanButtons']])}>
              <MPButton
                className={cx([styles['scanButton']])}
                onClick={handleClickScanQR}
              >
                <Image src={IconScanQR} alt={''} />{' '}
                <span>Quét mã BHYT/CCCD</span>
              </MPButton>
              <div className={styles['orText']}>Hoặc nhập thủ công</div>
            </div>
          )}
        </Row>
        <Row gutter={10}>
          {DividerCustomer('Thông tin chung')}
          {handleDetails({
            data,
            age,
            birthDay,
            onResetFieldDay,
            patientYearOldAccepted,
            partnerId,
            isForeign
          }).map((item, index) => {
            return (
              <Col
                key={index}
                span={24}
                sm={24}
                md={item.width === 'fuild' ? 24 : 12}
              >
                <div className={styles['inputItem']}>
                  {item?.enter && item?.enter(item)}
                </div>
              </Col>
            )
          })}
          {DividerCustomer(
            isForeign ? 'Thông tin lưu trú' : 'Địa chỉ theo CCCD'
          )}
          {handleAddressFields({ data, isMobile: windowWidth < 576 }).map(
            (item, index) => {
              return (
                <Col
                  key={index}
                  span={24}
                  sm={24}
                  md={item.width === 'fuild' ? 24 : 12}
                  hidden={item.hidden}
                >
                  <div className={styles['inputItem']}>
                    {item?.enter && item?.enter(item)}
                  </div>
                </Col>
              )
            }
          )}
          {showRelative && (
            <>
              {DividerCustomer('Thông tin thân nhân')}
              {handleRelative(data).map((item, index) => {
                return (
                  <Col
                    key={index}
                    span={24}
                    sm={24}
                    md={item.width === 'fuild' ? 24 : 12}
                  >
                    <div className={styles['inputItem']}>
                      {item?.enter && item?.enter(item)}
                    </div>
                  </Col>
                )
              })}
            </>
          )}
        </Row>
        <div className={styles['btnWrapper']}>
          {!patient?.id && (
            <MPButton
              className={styles['btnReset']}
              type='primary'
              htmlType='reset'
            >
              Nhập lại
            </MPButton>
          )}
          <MPButton
            className={styles['btnSubmit']}
            type='primary'
            loading={submitting}
            onClick={onSendEventTagManager}
          >
            {patient?.id ? 'Cập nhật' : 'Tạo mới'}
          </MPButton>
        </div>
      </Form>
      {/* Modal thông báo xác nhận địa chỉ (Luồng website riêng)  */}
      {confirmAddress && (
        <Modal
          open={confirmAddress}
          footer={null}
          closable={false}
          centered
          className={styles['modalAddress']}
        >
          <>
            <div className={styles['title']}>Thông báo</div>
            <p className={styles['desc']}>
              Vui lòng kiểm tra địa chỉ bạn đã cung cấp. Nếu khác với{' '}
              {isForeign ? 'thông tin lưu trú' : 'địa chỉ theo CCCD'}, nhấn
              'Chỉnh sửa lại' để cập nhật{' '}
            </p>
          </>
          <div className={styles['input']}>
            <p className={styles['add']}>Địa chỉ:</p>
            <p className={styles['address']}>
              {matchFullAddress(form.getFieldsValue(), data)}
            </p>
          </div>
          <div className={styles['guide']}>
            <label>Hướng dẫn:</label>
            <div className={styles['guideContent']}>
              <Image src={cancelIcon} width={20} height={20} layout='fixed' />
              <p>
                123 Bùi Đình Túy, quận Bình Thạnh, TPHCM Bùi Đình Túy, quận Bình
                Thạnh, TPHCM
              </p>
            </div>
            <div className={styles['guideContent']}>
              <Image src={okIcon} width={20} height={20} layout='fixed' />
              <p>123 Bùi Đình Túy, quận Bình Thạnh, TPHCM</p>
            </div>
          </div>
          <div className={styles['btnWrapper']}>
            <MPButton
              className={styles['btnCancel']}
              type='primary'
              onClick={toggleConfirmAddress}
            >
              Chỉnh sửa lại
            </MPButton>
            <MPButton
              className={styles['btnConfirm']}
              type='primary'
              onClick={handleConfirmAddress}
              loading={submitting}
            >
              Đồng ý
            </MPButton>
          </div>
        </Modal>
      )}
      {openScanQR && (
        <Modal
          centered
          title=''
          open={openScanQR}
          closable={true}
          footer={false}
          onCancel={handleCancelScanQR}
          className={styles['modalScanQR']}
        >
          <QRScanner onScanSuccess={handleScanSuccess} />
        </Modal>
      )}
      {isScanMessage && scanMessage ? (
        <Modal
          title={<div style={{ textAlign: 'center' }}>Thông báo</div>}
          centered
          open={isScanMessage}
          closable={true}
          footer={false}
          onCancel={handleCancelScanMessage}
        >
          {scanMessage}
        </Modal>
      ) : null}
    </div>
  )
}

export default MPCreatePatientFormCard
