.qrPaymentContainer {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  .paymentHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f0f0f0;

    h2 {
      font-size: 24px;
      font-weight: 700;
      color: #333;
      margin: 0;
    }

    .statusBadges {
      display: flex;
      align-items: center;
      gap: 12px;

      .badge {
        background: #e8f4fd;
        color: #1890ff;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }

      .timer {
        background: #fff2e8;
        color: #fa8c16;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
      }
    }
  }

  .orderInfo,
  .transferInfo {
    margin-bottom: 24px;

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      text-transform: uppercase;
    }

    .orderDetails,
    .bankDetails {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 16px;

      .orderItem,
      .bankItem {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        padding: 8px 0;

        &:last-child {
          margin-bottom: 0;
        }

        span:first-child {
          color: #666;
          font-weight: 500;
          flex: 1;
        }

        span:last-child {
          color: #333;
          font-weight: 600;
          text-align: right;
          flex: 1;
        }
      }
    }
  }

  .qrSection {
    margin-bottom: 24px;
    text-align: center;

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
    }

    .qrContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
      background: #f8f9fa;
      border-radius: 16px;
      padding: 24px;
      margin-bottom: 16px;

      .qrCode {
        width: 200px;
        height: 200px;
        background: white;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        position: relative;

        .qrPattern {
          width: 180px;
          height: 180px;
          position: relative;

          .qrGrid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            grid-template-rows: repeat(5, 1fr);
            gap: 2px;
            width: 100%;
            height: 100%;

            .qrDot {
              background: #f0f0f0;
              border-radius: 2px;

              &.filled {
                background: #000;
              }
            }
          }

          .qrCorners {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;

            .corner {
              position: absolute;
              width: 30px;
              height: 30px;
              border: 3px solid #000;
              border-radius: 4px;

              &:nth-child(1) {
                top: 10px;
                left: 10px;
              }

              &:nth-child(2) {
                top: 10px;
                right: 10px;
              }

              &:nth-child(3) {
                bottom: 10px;
                left: 10px;
              }

              &::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 12px;
                height: 12px;
                background: #000;
                border-radius: 2px;
              }
            }
          }
        }
      }

      .vietqrLogo {
        .logoPlaceholder {
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
          color: white;
          padding: 8px 16px;
          border-radius: 8px;
          font-weight: bold;
          font-size: 14px;
        }
      }
    }

    .transactionCode {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .actionButtons {
    display: flex;
    gap: 16px;
    justify-content: center;

    .cancelBtn {
      background: white;
      border: 2px solid #ff4d4f;
      color: #ff4d4f;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;

      &:hover {
        background: #ff4d4f;
        color: white;
      }
    }

    .confirmBtn {
      background: #52c41a;
      border: 2px solid #52c41a;
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;

      &:hover {
        background: #389e0d;
        border-color: #389e0d;
      }
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .paymentHeader {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      h2 {
        font-size: 20px;
      }
    }

    .qrSection {
      .qrContainer {
        padding: 16px;

        .qrCode {
          width: 160px;
          height: 160px;

          .qrPattern {
            width: 140px;
            height: 140px;
          }
        }
      }
    }

    .actionButtons {
      flex-direction: column;

      .cancelBtn,
      .confirmBtn {
        width: 100%;
      }
    }
  }
}
