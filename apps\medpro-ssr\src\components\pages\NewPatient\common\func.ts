import client from '../../../../../config/medproSdk'
import { showError } from '../../../../../utils/utils.notification'
import moment from 'moment'

const convertGenderCCCD = (gender: any, birthYear: any) => {
  if (birthYear > 1900 && birthYear < 2000) {
    return gender === 0 ? 1 : 0
  } else if (birthYear >= 2000 && birthYear < 2100) {
    return gender === 2 ? 1 : 0
  } else if (birthYear > 2200 && birthYear < 2300) {
    return gender === 4 ? 1 : 0
  } else if (birthYear > 2300 && birthYear < 2400) {
    return gender === 6 ? 1 : 0
  } else if (birthYear > 2400 && birthYear < 2500) {
    return gender === 8 ? 1 : 0
  }
}
export const extractInformationFromQR = async (str: any) => {
  try {
    let name, address, birthDate, insuranceId, sex, identifyId
    if (!str) {
      return null
    }
    const arrayinfoqr = str.split('|')
    if (!arrayinfoqr.length) {
      return null
    }
    if (arrayinfoqr[0].length === 12) {
      name = arrayinfoqr[2]
      address = arrayinfoqr[5]
      identifyId = arrayinfoqr[0]
      const birthDateFormat = moment(arrayinfoqr[3], 'DDMMYYYY')
      birthDate = birthDateFormat.format('DD/MM/YYYY')
      sex = `${convertGenderCCCD(
        Number(identifyId[3]),
        birthDateFormat.year()
      )}`
      insuranceId = ''
    } else {
      name = decodeURIComponent(
        arrayinfoqr[1].replace(/\s+/g, '').replace(/[0-9a-f]{2}/g, '%$&')
      )
      address = decodeURIComponent(
        arrayinfoqr[4].replace(/\s+/g, '').replace(/[0-9a-f]{2}/g, '%$&')
      )
      birthDate = arrayinfoqr[2]
      insuranceId = arrayinfoqr[0]
      sex = arrayinfoqr[3] - 1 === 1 ? 0 : 1
    }
    const response = await client.patient.parseAddress({ address })
    const data = response?.data
    return {
      sex,
      name: name.split(' ')[name.split(' ').length - 1],
      surname: name
        .split(' ')
        .splice(0, name.split(' ').length - 1)
        .toString()
        .replace(/,/g, ' '),
      fullname:
        name
          .split(' ')
          .splice(0, name.split(' ').length - 1)
          .toString()
          .replace(/,/g, ' ') +
        ' ' +
        name.split(' ')[name.split(' ').length - 1],
      birthdate: moment(birthDate, 'DD/MM/YYYY').isValid()
        ? birthDate
        : '01/01/' + birthDate,
      country_code: 'VIE',
      country: 'Việt Nam',
      city_id: data?.cityId,
      city: data?.cityId
        ? address.split(', ').length > 2
          ? address.split(', ')[address.split(', ').length - 1].split('_')[0]
          : ''
        : undefined,
      district_id: data?.districtId,
      district: data?.districtId
        ? address.split(', ').length > 2
          ? address.split(', ')[address.split(', ').length - 2]
          : ''
        : undefined,
      ward_id: data?.wardId,
      ward: data?.wardId
        ? address.split(', ').length > 2
          ? address.split(', ')[address.split(', ').length - 3]
          : ''
        : undefined,
      address: data.address || '',
      insuranceId: insuranceId.toUpperCase(),
      cmnd: identifyId
    }
  } catch (error) {
    showError(error)
  }
}
