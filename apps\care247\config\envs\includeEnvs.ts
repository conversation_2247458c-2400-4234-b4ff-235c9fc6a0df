import * as production from './production'
import * as testing from './testing'
import * as development from './development'
import * as hotfix from './hotfix'
import * as beta from './beta'

export type ENVObj = {
  development: typeof development
  testing: typeof testing
  production: typeof production
  hotfix: typeof hotfix
  beta: typeof beta
}

const exportedObject: ENVObj = {
  development,
  testing,
  production,
  hotfix,
  beta
}

export default exportedObject
