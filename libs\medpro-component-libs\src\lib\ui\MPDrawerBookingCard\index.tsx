import { Form, Input, Select } from 'antd'
import cx from 'classnames'
import { size } from 'lodash'
import { useState } from 'react'
import { CiSearch } from 'react-icons/ci'
import { HiOutlineLocationMarker } from 'react-icons/hi'
import { getReplaceUTF8, useWindowResize } from '../../common/func'
import MPButton from '../MPButton'
import DrawerProvince from './common/DrawerProvince'
import SvgFilter from './common/images/filter'
import styles from './styles.module.less'

const { Option } = Select

export interface DrawerBookingProps {
  province?: any[]
  title: string
  placeholder?: string
  subTitle?: string
  isHidden?: boolean
  handleSearch: (values: any) => void
  MPFillterDoctorCard?: any
  onSearch?: (kw: any) => void
  hospital?: string
  isHiddenFilter?: boolean
  keySearch?: any
  form: any
}

const MPDrawerBookingCard = (props: DrawerBookingProps) => {
  const {
    title,
    province = [],
    placeholder,
    handleSearch,
    isHiddenFilter = false,
    form
  } = props
  const isMobile = useWindowResize(577)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [dataFilter, setDataFilter] = useState([])
  return (
    <div className={styles['hospitals']}>
      <div
        className={cx(styles['header'], props.isHidden ? styles['hidden'] : '')}
        // , props.isHidden ? styles['hidden'] : ''
      >
        <h1 className={styles['title']}>{title}</h1>
        <div className={styles['description']}>{props.subTitle}</div>
      </div>
      <div className={styles['body']}>
        <Form
          form={form}
          className={cx(styles.form, props.isHidden && styles['stickyFilter'])}
        >
          <div
            className={cx(
              styles['formContent'],
              isHiddenFilter && styles['isHiddenFilter']
            )}
          >
            <div
              className={cx(
                styles['item'],
                size(province) === 0 && styles.unProvince
              )}
            >
              <div className={styles['icon']}>
                <CiSearch size={20} color='#B1B1B1' />
              </div>
              <div className={styles['inputItem']}>
                <Form.Item
                  name={'search'}
                  className={cx(styles['formItem'], styles['inputAntd'])}
                >
                  <Input
                    className={styles['selectItem']}
                    placeholder={placeholder || 'Vui lòng nhập...'}
                    onChange={(e) => handleSearch(e.target.value)}
                    defaultValue={props.keySearch || ''}
                    allowClear
                    style={{ border: 'none' }}
                  />
                </Form.Item>
              </div>
            </div>
            {!isHiddenFilter &&
              (isMobile ? (
                <MPButton
                  onClick={() => setIsModalOpen(true)}
                  className={cx(
                    styles.button,
                    size(dataFilter) > 0 && styles.activite
                  )}
                >
                  {size(dataFilter) > 0 ? (
                    <SvgFilter fill='#00b5f1' />
                  ) : (
                    <SvgFilter />
                  )}
                  Lọc
                </MPButton>
              ) : (
                size(province) > 0 && (
                  <div className={styles.item}>
                    <div className={styles.icon}>
                      <HiOutlineLocationMarker size={20} />
                    </div>
                    <div className={styles.inputItem}>
                      <div className={styles.formItem}>
                        <Select
                          onChange={(v) => {
                            handleSearch({ province: v })
                          }}
                          className={styles.selectItem}
                          placeholder='Tất cả địa điểm'
                          showSearch
                          allowClear
                          filterOption={(input, option) =>
                            getReplaceUTF8(
                              (
                                option?.children as unknown as string
                              ).toLowerCase()
                            ).includes(getReplaceUTF8(input.toLowerCase()))
                          }
                          dropdownMatchSelectWidth={false}
                        >
                          {province?.map((item, index) => (
                            <Option key={index} value={item.id}>
                              {item?.name}
                            </Option>
                          ))}
                        </Select>
                      </div>
                    </div>
                  </div>
                )
              ))}
            {isModalOpen && (
              <DrawerProvince
                open={isModalOpen}
                setOpen={setIsModalOpen}
                dataFilter={dataFilter}
                setDataFilter={setDataFilter}
                listFilter={province}
                handleFillterTabs={handleSearch}
              />
            )}
          </div>
        </Form>
        {/*</Form>*/}
        {props.MPFillterDoctorCard && <div>{props.MPFillterDoctorCard}</div>}
      </div>
    </div>
  )
}

export default MPDrawerBookingCard
