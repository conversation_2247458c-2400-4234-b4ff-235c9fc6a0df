import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  MP<PERSON><PERSON>er<PERSON><PERSON>pit<PERSON>,
  SEARCH_LIST,
  getLimitTab,
  useIsMount
} from '@medpro-libs/libs'
import cx from 'classnames'
import { debounce, identity, pickBy, size } from 'lodash'
import { GetServerSidePropsContext } from 'next'
import { useRouter } from 'next/router'
import React, { useCallback, useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import client from '../../config/medproSdk'
import SearchDoctor from '../../src/components/pages/SearchPage/SearchDoctor'
import SearchHopitals from '../../src/components/pages/SearchPage/SearchHopitals'
import SearchPackage from '../../src/components/pages/SearchPage/SearchPackage'
import SearchSpecialist from '../../src/components/pages/SearchPage/SearchSpecialist'
import SearchViewAll from '../../src/components/pages/SearchPage/SearchViewAll'
import DoctorSkeleton from '../../src/components/pages/SearchPage/selekon/DoctorSkeleton'
import HopitalsSkeleton from '../../src/components/pages/SearchPage/selekon/HopitalsSkeleton'
import SearViewAllSelekon from '../../src/components/pages/SearchPage/selekon/SearchViewAllSelekon'
import SpecialistSkeleton from '../../src/components/pages/SearchPage/selekon/SpecialistSkeleton'
import { AppInfo } from '../../src/type'
import { useAppSelector } from '../../store/hooks'
import { selectAppInfo } from '../../store/hospital/selector'
import { setBreadcrumb, totalDataActions } from '../../store/total/slice'
import { getAppInfo } from '../../utils/method'
import { openNotification, showError } from '../../utils/utils.notification'
import { fetchNewsCategory } from '../../utils/utils.query-server'
import styles from './styles.module.less'
import { PackageSkeleton } from '../../src/components/pages/SearchPage/selekon/packageSkeleton'
import Radar from 'radar-sdk-js'
import { currentEnv } from '../../config/envs'
import { locationActions } from '../../store/location/locationSlice'
import { ExclamationCircleOutlined } from '@ant-design/icons'

interface timKiemPageProps {
  partnerId?: string
  token?: string
  appLoading?: boolean
  // Đây này là thông tin của partnerId chạy app. Ví dụ medpro.
  isWebView?: boolean
  hospitals: any[]
  packages: any[]
  type: string
  data: any[]
  service: any[]
  news: any[]
  appInfo: AppInfo
}

let timeout
const timKiemPage = (props: timKiemPageProps) => {
  const { hospitals, news, data } = props
  const router = useRouter()
  const isMount = useIsMount()
  const kw = router.query.kw as string
  const tab = router.query.tab as string
  const page = router.query.page as any
  const treeId = router.query.treeId as any
  const locationData = useAppSelector((s) => s.location.data)
  const provinces = useAppSelector((s) => s.total.provinces)
  const appInfo = useAppSelector(selectAppInfo)
  const dispatch = useDispatch()
  const [keySearch, setKeySearch] = useState<any>(kw || '')
  const [searching, setSearching] = useState(false)
  const [listData, setListData] = useState<any>({})
  const [typeSearch, setTypeSearch] = useState<any>(SEARCH_LIST[0])
  const [pageIndex, setPageIndex] = useState(1)
  const [isMobile, setIsMobile] = useState(false)
  const [count, setCount] = useState(getLimitTab(typeSearch))
  const [loading, setLoading] = useState(false)
  useEffect(() => {
    dispatch(setBreadcrumb([{ title: 'Tìm kiếm' }]))
    if (tab) {
      setTypeSearch(tab)
    }
    if (kw) {
      setKeySearch(kw)
    }
    if (page) {
      setPageIndex(Number(page))
    }
    if (window.innerWidth < 577) {
      setIsMobile(true)
    }
  }, [])

  useEffect(() => {
    if (size(provinces) === 0) {
      dispatch(totalDataActions.fetchProvinces({ country_code: '203' }))
    }
  }, [provinces])

  useEffect(() => {
    clearTimeout(timeout)
    setSearching(true)
    const limit = getLimitTab(typeSearch)
    const offset = 1
    if (isMobile) setCount(limit)
    timeout = setTimeout(() => {
      void onSearch({
        keysearch: keySearch,
        limit,
        page: offset,
        treeId: treeId
      })
    }, 500)
  }, [keySearch, typeSearch])
  useEffect(() => {
    if (isMount) return
    const limit = getLimitTab(typeSearch)
    const offset = count / limit
    setLoading(true)
    timeout = setTimeout(() => {
      void onSearch({ keysearch: keySearch, limit, page: offset })
    }, 500)
  }, [count])
  const onSearchDebounce = useCallback(
    async (event: any) => {
      clearTimeout(timeout)
      const kw = event.target.value.normalize('NFC')
      setKeySearch(kw)
      const _query = { ...router.query, kw }

      router.replace(
        {
          pathname: router.pathname,
          query: { ..._query, page: 1 }
        },
        undefined,
        { shallow: true }
      )

      setSearching(true)
      setPageIndex(1)
      const limit = getLimitTab(typeSearch)
      const offset = 1
      timeout = setTimeout(() => {
        void onSearch({
          keysearch: kw,
          limit,
          page: offset
        })
      }, 500)
    },
    [router.query]
  )

  const onChangePage = ({ limit, page }: any) => {
    setSearching(true)
    timeout = setTimeout(() => {
      void onSearch({ keysearch: kw, limit, page, treeId: treeId })
    }, 500)
  }

  const onSearch = useCallback(
    debounce(
      async ({
        keysearch = '',
        limit,
        page,
        treeId
      }: {
        keysearch?: string
        limit: any
        page: any
        treeId?: any
      }) => {
        try {
          const { data } = await client.searchService.search(
            {
              search_key: decodeURIComponent(keysearch || keySearch),
              category: 'all',
              limit: limit,
              treeIds: treeId,
              offset: page,
              latitude: locationData?.latitude,
              longitude: locationData?.longitude
            },
            { appid: '', partnerid: '' }
          )
          let newData
          console.log(count > getLimitTab(typeSearch))

          if (count > getLimitTab(typeSearch) && isMobile) {
            newData = data.reduce((c, i) => {
              c[i.category] = {
                ...i,
                results: listData[i.category]?.results.concat(...i.results)
              }
              return c
            }, {})
          } else {
            newData = data.reduce((c, i) => {
              c[i.category] = { ...i, results: i.results }
              return c
            }, {})
          }

          return setListData(newData)
        } catch (err) {
          setListData({})
          showError(err)
        } finally {
          clearTimeout(timeout)
          setSearching(false)
          setLoading(false)
        }
      }
    ),
    [setListData, setSearching, count, locationData]
  )

  const handleSearch = (v2: any) => {
    const { hospital } = v2
    setKeySearch(hospital)
  }

  const renderLoading = (type: any) => {
    switch (type) {
      case SEARCH_LIST[1]:
        return <HopitalsSkeleton />
      case SEARCH_LIST[2]:
        return <DoctorSkeleton />
      case SEARCH_LIST[3]:
        return <SpecialistSkeleton />
      case SEARCH_LIST[4]:
        return <PackageSkeleton />

      default:
        return <SearViewAllSelekon />
    }
  }

  const renderFilter = (array: any[]) => {
    return (
      <ul className={cx(styles['tag'])}>
        {array.flatMap((t) => {
          return (
            <li key={t.key}>
              <MPButton
                type='primary'
                className={cx({
                  [styles['tagItem']]: true,
                  [styles['active']]: typeSearch === t.key
                })}
                onClick={() => {
                  const { page, ...paramsState } = router.query
                  router.replace(
                    {
                      pathname: router.pathname,
                      query: pickBy(
                        {
                          ...paramsState,
                          tab: t.key,
                          page: 1
                        },
                        identity
                      )
                    },
                    undefined,
                    { shallow: true }
                  )
                  setTypeSearch(t.key)
                }}
              >
                <h2>{t.label}</h2>
              </MPButton>
            </li>
          )
        })}
      </ul>
    )
  }

  const listMenu: any[] = [
    {
      key: SEARCH_LIST[0],
      label: `Tất cả (${
        listData[SEARCH_LIST[1]]?.total +
          listData[SEARCH_LIST[2]]?.total +
          listData[SEARCH_LIST[3]]?.total +
          listData[SEARCH_LIST[4]]?.total || 0
      })`,
      children: (
        <div className={styles['listHospital']}>
          <SearchViewAll data={listData} setTabs={setTypeSearch} />
        </div>
      )
    },
    {
      key: SEARCH_LIST[1],
      label: `Cơ sở y tế (${listData[SEARCH_LIST[1]]?.total || 0})`,
      children: typeSearch === SEARCH_LIST[1] && (
        <div className={styles['listHospital']}>
          <SearchHopitals
            data={listData[SEARCH_LIST[1]]?.results}
            totalPage={listData[SEARCH_LIST[1]]?.total}
            appInfo={appInfo}
            isMobile={isMobile}
            count={count}
            setCount={setCount}
            loading={loading}
            onSearch={onChangePage}
            setPageIndex={setPageIndex}
            pageIndex={pageIndex}
          />
        </div>
      )
    },
    {
      key: SEARCH_LIST[2],
      label: `Bác sĩ (${listData[SEARCH_LIST[2]]?.total || 0})`,
      children: (
        <div className={styles['listHospital']}>
          <SearchDoctor
            data={listData[SEARCH_LIST[2]]?.results}
            news={news}
            totalPage={listData[SEARCH_LIST[2]]?.total}
            isMobile={isMobile}
            count={count}
            setCount={setCount}
            loading={loading}
            onSearch={onChangePage}
            setPageIndex={setPageIndex}
            pageIndex={pageIndex}
          />
        </div>
      )
    },
    {
      key: SEARCH_LIST[3],
      label: `Chuyên khoa (${listData[SEARCH_LIST[3]]?.total || 0})`,
      children: (
        <div className={styles['listHospital']}>
          <SearchSpecialist
            data={listData[SEARCH_LIST[3]]?.results}
            totalPage={listData[SEARCH_LIST[3]]?.total}
            isMobile={isMobile}
            count={count}
            setCount={setCount}
            loading={loading}
            onSearch={onChangePage}
            setPageIndex={setPageIndex}
            pageIndex={pageIndex}
          />
        </div>
      )
    },
    {
      key: SEARCH_LIST[4],
      label: `Gói khám (${listData[SEARCH_LIST[4]]?.total || 0})`,
      children: (
        <div className={styles['listHospital']}>
          <SearchPackage
            data={listData[SEARCH_LIST[4]]?.results}
            totalPage={listData[SEARCH_LIST[4]]?.total}
            appInfo={appInfo}
            isMobile={isMobile}
            count={count}
            setCount={setCount}
            loading={loading}
            onSearch={onChangePage}
            setPageIndex={setPageIndex}
            pageIndex={pageIndex}
          />
        </div>
      )
    }
  ]

  const handleDetectLocation = () => {
    Radar.initialize(currentEnv.RADA_KEY)
    Radar.getLocation()
      .then(async (result: any) => {
        const response = await Radar.reverseGeocode({
          latitude: result.latitude,
          longitude: result.longitude
        })
        dispatch(
          locationActions.setUserLocation({
            ...result,
            ...response?.addresses[0]
          })
        )
      })
      .catch((err) => {
        let errorMessage = 'Đã xảy ra lỗi không xác định'
        if (err?.message?.includes('permissions')) {
          errorMessage = 'Medpro không được phép sử dụng vị trí của bạn.'
        }
        openNotification(
          'info',
          {
            message: (
              <div>
                <ExclamationCircleOutlined /> <span>Thông báo</span>
              </div>
            ),
            description: errorMessage,
            placement: 'topLeft'
          },
          'primary'
        )
      })
  }

  return (
    <div>
      <div className={styles['headerHospitals']}>
        <MPHeaderHospitals
          province={provinces}
          isHiddenFilter={true}
          handleSearch={handleSearch}
          onSearch={onSearchDebounce}
          handleDetectLocation={handleDetectLocation}
          locationData={locationData}
          keySearch={kw}
          title='Kết quả tìm kiếm'
          titleSearch='Tìm kiếm cơ sở y tế, bác sĩ, chuyên khoa, gói khám'
        />

        {renderFilter(listMenu)}
      </div>

      <MPContainer partner>
        {searching && (
          <div className={cx(styles['loader-container'])}>
            {renderLoading(typeSearch)}
          </div>
        )}
        <div
          className={cx(
            styles['results-container'],
            !searching ? styles['fade-in'] : ''
          )}
        >
          {listMenu.find((item: any) => {
            return item?.key === typeSearch
          })?.children || <div>Đang cập nhật</div>}
        </div>
      </MPContainer>
    </div>
  )
}
export default timKiemPage
timKiemPage.breadcrumbBgColor = 'white'

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const appInfo = await getAppInfo({ ctx: context })

  const [news] = await Promise.all([
    fetchNewsCategory({
      partnerId: 'medpro',
      page: 1,
      limit: 10000,
      cate: 'tin-dich-vu'
    })
  ])
  return {
    props: {
      news: news || []
    }
  }
}
