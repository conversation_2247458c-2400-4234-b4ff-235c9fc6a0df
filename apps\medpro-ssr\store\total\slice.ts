/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-empty-function */
import { createSlice } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'
import { BreadCrumbItem, TotalParams, TotalState } from './../interface'

const defaultBanner = {
  _id: '',
  status: false,
  fromDate: '',
  toDate: '',
  appid: '',
  alt: '',
  display: false,
  cta: {
    browser: false,
    url: '',
    target: ''
  },
  imageDesktopUrl: '',
  imageMobileUrl: '',
  repo: '',
  createdAt: '',
  updatedAt: ''
}

const initialState: TotalState = {
  listPatient: [],
  bookingOfUser: [],
  bookingAllUser: [],
  notiOfUser: [],
  groupNotiOfUser: [],
  bookingBill: {},
  breadcrumb: [],
  transactionInfo: [],
  loading: false,
  news: {
    loading: false,
    data: []
  },
  extraInfo: {
    _id: '',
    partnerId: '',
    createdAt: '',
    updatedAt: '',
    features: [],
    supportMethods: [],
    backgrounds: []
  },
  cashBack: {},
  banner: {
    header: defaultBanner,
    stickyBig: defaultBanner,
    stickySmall: defaultBanner
  },
  areaFilterData: null
}

export const totalSlice = createSlice({
  name: 'total',
  initialState,
  reducers: {
    setPatientByUserId: (
      state,
      action: PayloadAction<TotalParams.ListPatient.Response>
    ) => {
      state.listPatient = action.payload
    },
    unlinkPatient(
      state,
      action: PayloadAction<TotalParams.UnlinkPatient.Request>
    ) {},
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    getAllBookingOfUser() {},
    setAllBookingOfUser: (
      state,
      action: PayloadAction<TotalParams.BookingOfUser.Response>
    ) => {
      state.bookingOfUser = action.payload
    },
    getAllBooking() {},
    setAllBooking: (
      state,
      action: PayloadAction<TotalParams.BookingOfUser.Response>
    ) => {
      state.bookingAllUser = action.payload
    },
    getNotiOfUser() {},
    setNotiOfUser: (
      state,
      action: PayloadAction<TotalParams.NotiOfUser.Response>
    ) => {
      state.notiOfUser = action.payload
    },
    getGroupNotiOfUser() {},
    setGroupNotiOfUser: (
      state,
      action: PayloadAction<TotalParams.NotiOfUser.Response>
    ) => {
      state.groupNotiOfUser = action.payload
    },
    getUnReadNotif() {
      // run saga
    },
    setUnReadNoti(state, action) {
      state.unreadNotiCount = action.payload
    },
    setMarkViewedNoti: (
      state,
      action: PayloadAction<TotalParams.MarkViewedNoti.Request>
    ) => {},
    setMarkViewedAllNoti: (
      state,
      action: PayloadAction<TotalParams.MarkViewedNoti.Request>
    ) => {},
    deleteAllNoti(
      state,
      action: PayloadAction<TotalParams.UnlinkPatient.Request>
    ) {},
    getBookingByTransaction(
      state,
      action: PayloadAction<TotalParams.BookingBill.Request>
    ) {},
    setBookingByTransaction: (
      state,
      action: PayloadAction<TotalParams.BookingBill.Response>
    ) => {
      state.bookingBill = action.payload
    },
    getTransactionInfo(
      state,
      action: PayloadAction<TotalParams.TransactionInfo.Request>
    ) {},
    setTransactionInfo: (
      state,
      action: PayloadAction<TotalParams.TransactionInfo.Response>
    ) => {
      state.transactionInfo = action.payload
    },
    setBreadcrumb(state, action: PayloadAction<BreadCrumbItem[]>) {
      state.breadcrumb = action.payload
    },
    fetchCountries(state, action: PayloadAction<any>) {},
    fetchProvinces(state, action: PayloadAction<any>) {},
    fetchDistricts(state, action: PayloadAction<any>) {},
    fetchWards(state, action: PayloadAction<any>) {},
    fetchRelative(state) {},
    setCountries(state, action: PayloadAction<any[]>) {
      state.countries = action.payload
    },
    setProvinces(state, action: PayloadAction<any[]>) {
      state.provinces = action.payload
    },
    setDistricts(state, action: PayloadAction<any[]>) {
      state.districts = action.payload
    },
    setWards(state, action: PayloadAction<any[]>) {
      state.wards = action.payload
    },
    setRelative(state, action: PayloadAction<any[]>) {
      state.relative = action.payload
    },
    setLoadingTrue(state) {
      state.loading = true
    },
    setLoadingFalse(state) {
      state.loading = false
    },
    getNews(state, action: PayloadAction<{ appId: string }>) {
      state.news = { loading: true, data: [] }
    },
    getNewsSuccess(state, action: PayloadAction<any>) {
      state.news = { loading: false, data: action.payload }
    },
    getNewsFailed(state, action: PayloadAction<any>) {
      state.news = { loading: false, data: [], error: action.payload }
    },
    setExtraInfo(state, action: PayloadAction<any>) {
      state.extraInfo = action.payload
    },
    setCashBack(state, action: PayloadAction<any>) {
      state.cashBack = action.payload
    },
    getBannerNews(state, action: PayloadAction<any>) {
      // run saga
    },
    setBannerNews(state, action: PayloadAction<any>) {
      state.banner = action.payload
    },
    setAreaFilter: (state, action: PayloadAction<any>) => {
      state.areaFilterData = action.payload
    }
  }
})

// Action creators are generated for each case reducer function
export const {
  unlinkPatient,
  getAllBookingOfUser,
  setAllBookingOfUser,
  getAllBooking,
  setAllBooking,
  getNotiOfUser,
  setNotiOfUser,
  getGroupNotiOfUser,
  setGroupNotiOfUser,
  setMarkViewedNoti,
  setMarkViewedAllNoti,
  deleteAllNoti,
  getBookingByTransaction,
  setBookingByTransaction,
  getTransactionInfo,
  setTransactionInfo,
  setBreadcrumb,
  fetchCountries,
  setCountries,
  fetchProvinces,
  fetchDistricts,
  fetchWards,
  fetchRelative,
  setProvinces,
  setDistricts,
  setWards,
  setLoadingTrue,
  setLoadingFalse,
  setAreaFilter
} = totalSlice.actions

export const totalReducer = totalSlice.reducer
export const totalDataActions = totalSlice.actions
