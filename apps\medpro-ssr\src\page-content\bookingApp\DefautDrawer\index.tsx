import { Drawer } from 'antd'
import cx from 'classnames'
import { ReactNode } from 'react'
import { IoCloseSharp } from 'react-icons/io5'
import styles from './styles.module.less'
interface drawerProps {
  onClose: () => void
  open: boolean
  title?: any
  width?: number | string
  height?: number | string
  extra?: ReactNode
  placement?: any
  children: ReactNode
  className?: any
  destroyOnClose?: boolean
  closeIcon?: any
  [key: string]: any
}
const DefautDrawer = ({
  onClose,
  open,
  title,
  width,
  extra,
  height,
  placement,
  children,
  className,
  destroyOnClose,
  closeIcon = true,
  ...rest
}: drawerProps) => {
  return (
    open && (
      <Drawer
        title={
          <span className={styles['titleDrawer']}>
            {title || 'Vui lòng chọn'}
          </span>
        }
        placement={placement || 'bottom'}
        onClose={onClose}
        open={open}
        height={height || '100%'}
        className={cx(styles['drawer'], className)}
        extra={extra}
        destroyOnClose={destroyOnClose}
        closeIcon={closeIcon ? <IoCloseSharp size={22} /> : closeIcon}
        {...rest}
      >
        {children}
      </Drawer>
    )
  )
}

export default DefautDrawer
