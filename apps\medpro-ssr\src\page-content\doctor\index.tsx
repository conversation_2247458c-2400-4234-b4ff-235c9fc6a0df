import {
  <PERSON><PERSON><PERSON><PERSON>,
  MP<PERSON><PERSON>r,
  MPHeaderHospitals,
  MPListNewsDoctorCard,
  MPNewListDoctorCard,
  SEARCH_TAB,
  getReplaceUTF8,
  useIsMount,
  useWindowResize
} from '@medpro-libs/libs'
import { Col, Modal, Pagination, Row, Select } from 'antd'
import cx from 'classnames'
import { debounce, size } from 'lodash'
import Image from 'next/image'
import { useRouter } from 'next/router'
import React, { useCallback, useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { totalDataActions } from '../../../store/total/slice'
import EmptyList from './../../../public/images/EmptyList.png'
import styles from './styles.module.less'
import { useAppSelector } from '../../../store/hooks'
import { selectExtraConfig } from '../../../store/hospital/selector'
import client from '../../../config/medproSdk'
import { openNotification, showError } from '../../../utils/utils.notification'
import { bookingActions } from '../../../store/booking/slice'
import derectionUp from '../../../public/common/derectionUp.svg'
import DoctorSkeleton from './doctorSkeleton'
import { hospitalActions } from '../../../store/hospital/hospitalSlice'
import imgFilter from '../service-price/common/filter.svg'
import Radar from 'radar-sdk-js'
import { currentEnv } from '../../../config/envs'
import { locationActions } from '../../../store/location/locationSlice'
import { ExclamationCircleOutlined } from '@ant-design/icons'

interface BacSiPageProps {
  token?: string
  appLoading?: boolean
  // Đây này là thông tin của partnerId chạy app. Ví dụ medpro.
  isWebView?: boolean
  packages: any[]
  type: string
  data: any
  service: any[]
  news: any[]
  total: number
  searchKeyWords: any[]
  partnerInfo?: any
}

let timeout

const PAGE_SIZE = 5

const { Option } = Select

const BacSiPage = (props: BacSiPageProps) => {
  const { news, data, total, searchKeyWords = [] } = props
  const router = useRouter()
  const isMount = useIsMount()
  const isMobile = useWindowResize(576)
  const page = Number(router.query.page || 1)
  const kw = router.query.kw as string
  const partner_slug = router.query.partner_slug as string
  const locationData = useAppSelector((s) => s.location.data)
  const provinces = useAppSelector((s) => s.total.provinces)
  const dispatch = useDispatch()
  const [keySearch, setKeySearch] = useState<string>(kw)
  const [cityId, setCityId] = useState<string>('')
  const [pageIndex, setPageIndex] = useState(page)
  const [isHidden, setIsHidden] = useState(false)
  const [count, setCount] = useState(PAGE_SIZE)
  const [typeSearch, setTypeSearch] = useState<string[]>([])
  const [doctorData, setDoctorData] = useState<any>(data || [])
  const [totalRows, setTotalRows] = useState(total)
  const [searching, setSearching] = useState(false)
  const [loading, setLoading] = useState(false)
  const extraConfig = useAppSelector(selectExtraConfig)
  const [warningBooking, setWarningBooking] = useState({
    status: false,
    message: ''
  })
  const [listHospital, setListHospital] = useState([])
  const [listFilter, setListFilter] = useState<any>()
  const [listFilterSearch, setListFilterSearch] = useState<{
    partnerId: string
    subject_ids: string
    role_ids: string
  }>({
    partnerId: '',
    subject_ids: '',
    role_ids: ''
  })

  useEffect(() => {
    if (!props.data) {
      onSearch({ kw: keySearch, pageIndex: page })
    }
  }, [])
  useEffect(() => {
    if (isMount) return
    setLoading(true)
    timeout = setTimeout(() => {
      onSearch({
        kw: kw,
        pageIndex: count / PAGE_SIZE,
        limit: count,
        paramsSearch: listFilterSearch
      })
    }, 500)
  }, [count])
  useEffect(() => {
    if (size(provinces) === 0) {
      dispatch(totalDataActions.fetchProvinces({ country_code: '203' }))
    }
  }, [provinces])

  const fetchListHospital = async () => {
    try {
      const { data } = await client.partner.getHospitalListByAppIdV6()
      setListHospital(data)
    } catch (error) {
      showError(error)
    }
  }

  useEffect(() => {
    fetchListHospital()
  }, [])

  useEffect(() => {
    setPageIndex(1)
    router.replace(
      {
        pathname: router.pathname,
        query: { ...router.query, page: 1 }
      },
      undefined,
      { shallow: true }
    )
    onSearch({
      kw: keySearch,
      pageIndex: pageIndex,
      limit: count,
      paramsSearch: listFilterSearch
    })
  }, [listFilterSearch])

  const onSearch = useCallback(
    debounce(
      async ({
        kw,
        pageIndex,
        limit,
        paramsSearch
      }: {
        kw: string
        pageIndex?: number | string
        limit?: number
        paramsSearch?: any
      }) => {
        try {
          const { data } = await client.doctor.doctor(
            {
              offset: Number(pageIndex || 1),
              limit: limit || PAGE_SIZE,
              search_key: kw,
              subject_ids: paramsSearch.subject_ids,
              role_ids: paramsSearch.role_ids,
              latitude: locationData?.latitude,
              longitude: locationData?.longitude
            },
            {
              appid: '',
              partnerid:
                props.partnerInfo?.partnerId ||
                partner_slug ||
                paramsSearch.partnerId
            }
          )
          count > PAGE_SIZE
            ? setDoctorData((prev: any) => prev.concat(data?.results))
            : setDoctorData(data?.results || [])

          setTotalRows(data?.total)
          setListFilter({
            subjects: data?.subjects,
            roles: data?.roles
          })
        } catch (err) {
          showError(err)
        } finally {
          clearTimeout(timeout)
          setSearching(false)
          setLoading(false)
        }
      }
    ),
    [pageIndex, count, setDoctorData, setTotalRows, setSearching, locationData]
  )

  const handleBooking = async (item: any) => {
    if (window.innerWidth < 576) {
      await dispatch(
        bookingActions.handleBookingCta({
          type: 'bookingApp',
          item
        })
      )
      return
    }
    if (item.category === 'doctor' && item.doctorDescription.disabled) {
      setWarningBooking((preState) => ({
        ...preState,
        status: true,
        message: item.doctorDescription.message
      }))
    } else {
      await dispatch(
        bookingActions.handleBookingCta({
          type: !isMobile ? SEARCH_TAB.DOCTOR : 'bookingApp',
          item
        })
      )
    }
  }
  const onSearchDebounce = useCallback(
    async (event: any) => {
      clearTimeout(timeout)
      const kw = event.target.value
      setKeySearch(kw)
      const _query = { ...router.query, kw, page: 1 }

      router.replace(
        {
          pathname: router.pathname,
          query: _query
        },
        undefined,
        { shallow: true }
      )

      setSearching(true)
      timeout = setTimeout(() => {
        setPageIndex(1)
        void onSearch({
          kw: kw,
          pageIndex: pageIndex,
          limit: count,
          paramsSearch: listFilterSearch
        })
      }, 500)
    },
    [typeSearch, router.query]
  )

  useEffect(() => {
    if (cityId) {
      const newData = data.filter((item: any) => {
        const cityMatches =
          !cityId ||
          item?.partner?.city_id.toLowerCase().includes(cityId.toLowerCase())

        return cityMatches
      })
      setDoctorData(newData)
    } else {
      setDoctorData(data)
    }
  }, [cityId])

  useEffect(() => {
    const tags = typeSearch.join(',')
    const _query = { ...router.query, tags } as any

    !tags && delete _query.tags

    router.replace(
      {
        pathname: router.pathname,
        query: _query
      },
      undefined,
      { shallow: true }
    )
  }, [typeSearch])

  const handleFiller = (item: any) => {
    console.log(item)
  }
  const handleSearch = (v2: any) => {
    const { province } = v2
    setCityId(province)
    setPageIndex(1)
  }

  const dataSelect = [
    {
      id: 'partnerId',
      label: 'Cơ sở y tế',
      data: listHospital,
      hidden: !!props?.partnerInfo
    },
    {
      id: 'subject_ids',
      label: 'Chuyên khoa',
      data: listFilter?.subjects,
      hidden: false
    },
    {
      id: 'role_ids',
      label: 'Học hàm/ Học vị',
      data: listFilter?.roles,
      hidden: false
    }
  ].filter((item: any) => !item.hidden)

  const getValueSelect = (id: string) => {
    if (id === 'partnerId') {
      return listFilterSearch.partnerId || null
    }
    if (id === 'subject_ids') {
      return listFilterSearch.subject_ids || null
    }
    if (id === 'role_ids') {
      return listFilterSearch.role_ids || null
    }
  }

  const handleDetectLocation = () => {
    Radar.initialize(currentEnv.RADA_KEY)
    Radar.getLocation()
      .then(async (result: any) => {
        const response = await Radar.reverseGeocode({
          latitude: result.latitude,
          longitude: result.longitude
        })
        dispatch(
          locationActions.setUserLocation({
            ...result,
            ...response?.addresses[0]
          })
        )
      })
      .catch((err) => {
        let errorMessage = 'Đã xảy ra lỗi không xác định'
        if (err?.message?.includes('permissions')) {
          errorMessage = 'Medpro không được phép sử dụng vị trí của bạn.'
        }
        openNotification(
          'info',
          {
            message: (
              <div>
                <ExclamationCircleOutlined /> <span>Thông báo</span>
              </div>
            ),
            description: errorMessage,
            placement: 'topLeft'
          },
          'primary'
        )
      })
  }

  return (
    <div>
      <div className={styles['headerHospitals']}>
        <MPHeaderHospitals
          hospital={keySearch}
          // province={partner_slug ? [] : provinces}
          handleSearch={(e) =>
            handleSearch({ kw: keySearch, province: e.province })
          }
          handleDetectLocation={handleDetectLocation}
          locationData={locationData}
          onSearch={(e) => onSearchDebounce(e)}
          title='Danh sách bác sĩ'
          titleSearch='Tìm kiếm bác sĩ'
          dataAddress={{
            name: props?.partnerInfo?.name,
            address: props?.partnerInfo?.address
          }}
          isHidden={isHidden}
        />
        <ul
          className={cx(
            styles['searchTab'],
            !!props?.partnerInfo && styles['partnerSearchTag']
          )}
        >
          {dataSelect?.map((el: any, index: number) => (
            <li key={el.id}>
              <Select
                showSearch
                className={cx(
                  styles['tagItem'],
                  !!props?.partnerInfo && styles['partnerTagItem']
                )}
                placeholder={el.label}
                allowClear
                filterOption={(input, option) =>
                  getReplaceUTF8(
                    (option?.children as unknown as string).toLowerCase()
                  ).includes(getReplaceUTF8(input.toLowerCase()))
                }
                dropdownRender={(menu) => {
                  return <div className={styles['drop']}>{menu}</div>
                }}
                dropdownStyle={{ borderRadius: '12px' }}
                onChange={(v) => {
                  if (el.id === 'partnerId') {
                    setCount(PAGE_SIZE)
                    !v
                      ? setListFilterSearch({
                          ...listFilterSearch,
                          partnerId: v
                        })
                      : setListFilterSearch({
                          partnerId: v,
                          subject_ids: '',
                          role_ids: ''
                        })
                  }
                  el.id === 'subject_ids' &&
                    setListFilterSearch({
                      ...listFilterSearch,
                      subject_ids: v
                    })
                  el.id === 'role_ids' &&
                    setListFilterSearch({ ...listFilterSearch, role_ids: v })
                  setPageIndex(1)
                }}
                value={getValueSelect(el.id)}
                dropdownMatchSelectWidth={false}
                options={el.data?.map((item) => ({
                  label: (
                    <div className={styles['tagItem_data']}>
                      <p>{item?.name}</p>
                      <span>{item.address}</span>
                    </div>
                  ),
                  value: item?.partnerId || item?.id,
                  children: item?.name
                }))}
              />
            </li>
          ))}
        </ul>
      </div>
      <MPContainer partner>
        <div
          className={cx(
            styles['listHospital'],
            size(doctorData) === 0 && styles['noneData']
          )}
        >
          {searching ? (
            <DoctorSkeleton />
          ) : size(doctorData) > 0 ? (
            <Row gutter={26}>
              <Col span={24} lg={{ span: 16, order: 1 }}>
                <MPNewListDoctorCard
                  data={doctorData}
                  partnerDetail={listFilterSearch.partnerId === 'umc'}
                  handleBooking={handleBooking}
                />
              </Col>
              {size(doctorData) > 0 && (
                <>
                  <Col
                    span={24}
                    lg={{ span: 24, order: 3 }}
                    className={styles['desktop']}
                  >
                    <div className={styles['pagination']}>
                      <Pagination
                        pageSize={5}
                        current={pageIndex}
                        onChange={(p) => {
                          window.scrollTo(0, 0)
                          setPageIndex(p)
                          setCount(PAGE_SIZE)
                          setSearching(true)
                          void onSearch({
                            kw: keySearch,
                            pageIndex: p,
                            limit: count,
                            paramsSearch: listFilterSearch
                          })

                          router.replace(
                            {
                              pathname: router.pathname,
                              query: { ...router.query, page: p }
                            },
                            undefined,
                            { shallow: true }
                          )
                        }}
                        total={totalRows}
                        showSizeChanger={false}
                      />
                    </div>
                  </Col>
                  {totalRows > count &&
                    (loading ? (
                      <div className={styles['loadingRing']}>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                      </div>
                    ) : (
                      <div
                        onClick={() =>
                          setCount((prev: any) => prev + PAGE_SIZE)
                        }
                        className={styles['viewMove']}
                      >
                        <p>Xem tiếp</p>
                        <span>
                          <Image
                            src={derectionUp}
                            width={13}
                            height={13}
                            objectFit='contain'
                            alt='Icon Derection Up'
                            layout='fixed'
                          />
                        </span>
                      </div>
                    ))}
                </>
              )}

              <Col
                span={24}
                lg={{ span: 8, order: 2 }}
                // className={styles[`${device}`]}
              >
                <MPListNewsDoctorCard
                  data={data}
                  news={news}
                  handleFiller={handleFiller}
                  subjects={subjects}
                />
                <div className={styles['image']}>
                  <Image
                    src={
                      'https://bo-api.medpro.com.vn/static/images/medpro/web/salebooking.png'
                    }
                    width={368}
                    height={588}
                    alt='Banner tải App Medpro'
                    objectFit='contain'
                    layout='responsive'
                  />
                </div>
              </Col>
            </Row>
          ) : size(keySearch) || size(cityId) || size(typeSearch) ? (
            <div className={styles['error']}>
              <p>Không tìm thấy bác sĩ cần tìm</p>
              <Image src={EmptyList} alt='errorImg' priority />
            </div>
          ) : (
            <div className={styles['error']}>
              <p>Danh sách sẽ cập nhật trong thời gian tới</p>
              <Image src={EmptyList} alt='errorImg' priority />
            </div>
          )}
          {warningBooking.status && (
            <Modal
              title={'Thông báo'}
              open={warningBooking.status}
              footer={null}
              centered
              onCancel={() =>
                setWarningBooking((preState) => ({
                  ...preState,
                  status: false,
                  message: ''
                }))
              }
              className={styles['modal']}
            >
              <div
                className={styles['description']}
                dangerouslySetInnerHTML={{
                  __html: warningBooking.message
                }}
              />
            </Modal>
          )}
        </div>
      </MPContainer>
    </div>
  )
}
export default BacSiPage
BacSiPage.breadcrumbBgColor = 'white'
const subjects = [
  'Bệnh lý tuyến giáp',
  'Hô hấp',
  'Gây mê - điều trị đau',
  'Huyết học'
]
