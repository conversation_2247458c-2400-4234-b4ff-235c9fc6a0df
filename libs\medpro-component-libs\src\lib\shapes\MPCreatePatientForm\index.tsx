import React from 'react'
import MPCreatePatientFormComponent from '../../component/MPCreatePatientFormComponent'
import MPCreatePatientFormCard from '../../ui/MPCreatePatientFormCard'

interface Props {
  data: any
  onSubmit: (values: any) => void
  onChangeAddress: (type: string, id: string) => void
  cleanDistrictsAndWards?: () => void
  appId?: string
  submitting?: boolean
  userCountry?: string
  patientYearOldAccepted?: number
  partnerId?: string
  userId?: string
  extractInformation?: (values: any) => Promise<any>
  isCreate?: boolean
}

export const MPCreatePatientForm = ({
  data,
  onSubmit,
  onChangeAddress,
  submitting,
  patientYearOldAccepted,
  cleanDistrictsAndWards,
  appId = 'ssr',
  partnerId,
  userId,
  extractInformation,
  isCreate
}: Props) => {
  return (
    <MPCreatePatientFormComponent
      onSubmit={onSubmit}
      onChangeAddress={onChangeAddress}
      appId={appId}
      renderItem={(
        handleSubmit: (values: any) => void,
        handleChangeAddress: (type: string, id: string) => void,
        isCSKHApp: () => boolean
      ) => {
        return (
          <MPCreatePatientFormCard
            data={data}
            patientYearOldAccepted={patientYearOldAccepted}
            isCSKHApp={isCSKHApp()}
            cleanDistrictsAndWards={cleanDistrictsAndWards}
            handleSubmit={handleSubmit}
            handleChangeAddress={handleChangeAddress}
            submitting={submitting}
            partnerId={partnerId}
            userId={userId}
            extractInformation={extractInformation}
            isCreate={isCreate}
          />
        )
      }}
    />
  )
}

export default MPCreatePatientForm
