import * as React from 'react'
import { useEffect, useState } from 'react'
import Image from 'next/image'
import { Col, Form, Modal, Row } from 'antd'
import { BiLeftArrowAlt } from 'react-icons/bi'
import { useRouter } from 'next/router'
import moment from 'moment'
import cx from 'classnames'
import MPButton from '../MPButton'
import SexSelectDrawer from '../common/SexSelectDrawer'
import MobileSelectDrawer from '../common/MobileSelectDrawer'
import { handleDetailQRScan, handleDetails } from './common/handleDetails'
import IconScanQR from './common/iconScanQR.svg'
import QRScanner from './common/QRScanner'
import styles from './styles.module.less'
import { matchFullAddress } from '../../common/func'
import cancelIcon from '../../common/images/cancel_icon.jpg'
import okIcon from '../../common/images/ok_icon.jpg'

export interface Props {
  province: any[]
  handleSubmit: (values: any) => void
  extractInformation: (values: any) => void
  showPopup: boolean
  idForUpdate: string
  partnerId?: string
  data: any
  district: any[]
  ward: any[]
  handleChangeAddress: (type: string, id: string) => void
}

const MPCreateProfileAppCard = ({
  province,
  handleSubmit,
  extractInformation,
  showPopup,
  idForUpdate,
  partnerId,
  data,
  district,
  ward,
  handleChangeAddress
}: Props) => {
  const router = useRouter()
  const scannerRef = React.useRef<any>(null)
  const [dataSelect, setDataSelect] = useState<any>({})
  const [openPickSex, setOpenPickSex] = useState(false)
  const [openPickCity, setOpenPickCity] = useState(false)
  const [openScanQR, setOpenScanQR] = useState(false)
  const [scanMessage, setScanMessage] = useState<any>(undefined)
  const [isScanMessage, setIsScanMessage] = useState<boolean>(false)
  const [age, setAge] = useState(-1)

  const [form] = Form.useForm()

  const [showNotePopup, setShowNotePopup] = useState(showPopup)
  const [isFormQRScan, setFormQRScan] = useState(false)
  const [confirmAddress, setConfirmAddress] = useState(false)
  const [isForeign, setIsForeign] = useState(false)

  useEffect(() => {
    setShowNotePopup(showPopup)
  }, [showPopup])

  const handleFormChange = (fieldId?: string) => {
    const valueForm = form.getFieldsValue()
    if (fieldId === 'country_code' && valueForm.country_code) {
      form.setFieldValue('city_id', null)
      form.setFieldValue('district_id', null)
      form.setFieldValue('ward_id', null)
      form.setFieldValue('address', '')
    }
    if (fieldId === 'city_id' && valueForm.city_id) {
      handleChangeAddress('district', valueForm.city_id)
      form.setFieldValue('district_id', null)
      form.setFieldValue('ward_id', null)
    }
    if (fieldId === 'district_id' && valueForm.district_id) {
      handleChangeAddress('ward', valueForm.district_id)
      form.setFieldValue('ward_id', null)
      form.validateFields(['district_id'])
    }
    if (fieldId === 'ward_id' && valueForm.ward_id) {
      form.validateFields(['ward_id'])
    }
    if (valueForm.country_code) {
      switch (valueForm.country_code) {
        case 'VIE':
          form.setFieldValue(
            'dantoc_id',
            valueForm.dantoc_id === 'medpro_82'
              ? 'medpro_1'
              : valueForm.dantoc_id
          )
          break
        default:
          form.setFieldValue('dantoc_id', 'medpro_82')
          break
      }
    }
    if (partnerId === 'bvmathcm' && age < 14) {
      delete valueForm.cmnd
    }
  }

  const openSelect = (field: any) => {
    switch (field.id) {
      case 'sex':
        setOpenPickSex(true)
        return
      case 'dantoc_id':
      case 'city_id':
      case 'district_id':
      case 'ward_id':
      case 'country_code':
        setDataSelect({ ...field })
        setOpenPickCity(true)
        if (field.id === 'country_code') {
          setIsForeign(field.value !== 'VIE')
        }
        return
      default:
        return
    }
  }

  const onFinish = (values: any) => {
    const day = values.birthdate.split('/')[0]
    const month = values.birthdate.split('/')[1]
    const year = values.birthdate.split('/')[2]

    const data = {
      ...values,
      birthdate: moment(`${year}-${month}-${day}`, 'YYYY-MM-DD')
        .utc(true)
        .toISOString()
    }
    handleSubmit(data)
  }

  const onFinishFailed = (errorInfo: any) => {
    const firstErrorField = errorInfo.errorFields[0].name
    form.scrollToField(firstErrorField, {
      behavior: (actions) =>
        actions.forEach(({ el, top, left }) => {
          // 100 is height of element
          const adjustedTop = top - 100
          el.scrollTop = adjustedTop
          el.scrollLeft = left
        })
    })
  }

  const toggleSexDrawer = () => {
    setOpenPickSex((prev) => !prev)
  }

  const toggleCityDrawer = () => {
    setOpenPickCity((prev) => !prev)
  }

  const toggleAge = (value: string) => {
    const isValid = moment(value, 'DD/MM/YYYY', true).isValid()
    if (isValid) {
      setAge(moment().diff(moment(value, 'DD/MM/YYYY'), 'years'))
    }
  }

  const handleClickScanQR = () => {
    setOpenScanQR(true)
  }

  const handleCancelScanQR = () => {
    if (scannerRef.current?.stopScanning) {
      scannerRef.current.stopScanning()
    }
    if (scannerRef.current?.stopScanningImg) {
      scannerRef.current.stopScanningImg()
    }
    setOpenScanQR(false)
  }

  const handleCancelScanMessage = () => {
    setIsScanMessage(false)
    setScanMessage(undefined)
  }

  const handleScanSuccess = async (decodedText: any) => {
    setOpenScanQR(false)
    if (scannerRef.current?.stopScanningImg) {
      scannerRef.current.stopScanningImg()
    }
    const extractInfo = (await extractInformation(decodedText)) as any
    if (extractInfo) {
      handleChangeAddress('district', extractInfo?.city_id)
      handleChangeAddress('ward', extractInfo?.district_id)
      form.setFieldsValue({
        name: extractInfo?.fullname,
        birthdate: extractInfo?.birthdate,
        sex: Number(extractInfo?.sex),
        insuranceId: extractInfo?.insuranceId,
        cmnd: extractInfo?.cmnd,
        country_code: extractInfo?.country_code,
        city_id: extractInfo?.city_id,
        district_id: extractInfo?.district_id,
        ward_id: extractInfo?.ward_id,
        address: extractInfo?.address
      })
      handleFormChange()
      setFormQRScan(true)
      if (extractInfo?.country_code !== 'VIE') {
        setIsForeign(true)
      }
    } else {
      setScanMessage('Mã QR không hợp lệ!')
      setIsScanMessage(true)
    }
  }

  const toggleConfirmAddress = () => {
    if (
      form.getFieldValue('city_id') &&
      form.getFieldValue('district_id') &&
      form.getFieldValue('ward_id')
    ) {
      form
        .validateFields()
        .then(() => {
          setConfirmAddress((preState) => !preState)
        })
        .catch((errorInfo) => {
          const firstErrorField = errorInfo.errorFields[0].name
          form.scrollToField(firstErrorField, {
            behavior: (actions) =>
              actions.forEach(({ el, top, left }) => {
                // 100 is height of element
                const adjustedTop = top - 100
                el.scrollTop = adjustedTop
                el.scrollLeft = left
              })
          })
        })
    } else {
      form.submit()
    }
  }

  const handleConfirmAddressInfo = () => {
    form.submit()
  }

  return (
    <div className={styles['main']}>
      <div className={styles['headerTitle']}>
        <BiLeftArrowAlt
          size={24}
          onClick={() => router.back()}
          className={styles['headerSide']}
        />
        <p>Tạo hồ sơ</p>
        <div className={styles['headerSide']} />
      </div>
      <Form
        form={form}
        layout='vertical'
        initialValues={{
          country_code: 'VIE',
          dantoc_id: 'medpro_1'
        }}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        onFieldsChange={() => handleFormChange()}
        className={styles['listInfo']}
      >
        <Row gutter={12}>
          <div className={cx([styles.scanButtons])}>
            <MPButton
              className={cx([styles.scanButton])}
              onClick={handleClickScanQR}
            >
              <Image src={IconScanQR} alt={''} /> <span>Quét mã BHYT/CCCD</span>
            </MPButton>
            <div className={styles.orText}>Hoặc nhập thủ công</div>
          </div>
        </Row>
        <Row gutter={12}>
          {(!isFormQRScan
            ? handleDetails({
                data,
                openSelect,
                form,
                toggleAge
              })
            : handleDetailQRScan({
                openSelect,
                province,
                form,
                toggleAge,
                data,
                ward,
                district,
                isForeign
              })
          ).map((item, index) => {
            return (
              !item.hidden && (
                <Col key={index} span={item.width === '100%' ? 24 : 12}>
                  <div
                    className={cx(
                      styles['inputItem'],
                      item.id === 'birthdate' && styles['inputItemHalfLeft'],
                      item.id === 'sex' && styles['inputItemHalfRight']
                    )}
                  >
                    {item?.enter && item?.enter(item)}
                  </div>
                </Col>
              )
            )
          })}
        </Row>
        <div className={styles['footerBtn']}>
          <MPButton
            className={cx({
              [styles['btnSubmit']]: true
            })}
            type='primary'
            full='true'
            onClick={toggleConfirmAddress}
          >
            Tạo mới
          </MPButton>
        </div>
      </Form>

      {openPickSex && (
        <SexSelectDrawer
          isOpen={openPickSex}
          toggleDrawer={toggleSexDrawer}
          form={form}
          handleFormChange={handleFormChange}
        />
      )}
      {openPickCity && (
        <MobileSelectDrawer
          isOpen={openPickCity}
          toggleDrawer={toggleCityDrawer}
          form={form}
          handleFormChange={handleFormChange}
          dataSelect={dataSelect}
        />
      )}
      {showNotePopup && (
        <Modal
          title={'Lưu ý'}
          open={showNotePopup}
          footer={null}
          centered
          closable={false}
          className={styles['notePopup']}
        >
          <p>
            Bạn cần bổ sung thêm thông tin theo yêu cầu của cơ sở y tế. Bạn muốn
            hoàn tất thông tin này hay để sau khi đặt khám thành công?
          </p>
          <div className={styles['btnContainer']}>
            <MPButton
              type='default'
              onClick={() => {
                router.push({
                  pathname: '/cap-nhat-thong-tin',
                  query: {
                    ...router.query,
                    id: idForUpdate
                  }
                })
              }}
            >
              Hoàn tất thông tin
            </MPButton>
            <MPButton
              onClick={() => {
                router.push({
                  pathname: '/chon-lich-kham',
                  query: {
                    ...router.query,
                    step: 'chon-ho-so'
                  }
                })
              }}
              type='primary'
            >
              Để sau
            </MPButton>
          </div>
        </Modal>
      )}
      {openScanQR && (
        <Modal
          centered
          title=''
          open={openScanQR}
          closable={true}
          footer={false}
          onCancel={handleCancelScanQR}
          className={styles['modalScanQR']}
        >
          <QRScanner ref={scannerRef} onScanSuccess={handleScanSuccess} />
        </Modal>
      )}
      {isScanMessage && scanMessage ? (
        <Modal
          title={<div style={{ textAlign: 'center' }}>Thông báo</div>}
          centered
          open={isScanMessage}
          closable={true}
          footer={false}
          onCancel={handleCancelScanMessage}
        >
          {scanMessage}
        </Modal>
      ) : null}
      {confirmAddress && (
        <Modal
          open={confirmAddress}
          footer={null}
          closable={false}
          centered
          className={styles['modalAddress']}
        >
          <>
            <div className={styles['title']}>Thông báo</div>
            <p className={styles['desc']}>
              Vui lòng kiểm tra địa chỉ bạn đã cung cấp. Nếu khác với{' '}
              {isForeign ? 'thông tin lưu trú' : 'địa chỉ theo CCCD'}, nhấn
              'Chỉnh sửa lại' để cập nhật
            </p>
          </>
          <div className={styles['input']}>
            <p className={styles['add']}>Địa chỉ:</p>
            <p className={styles['address']}>
              {matchFullAddress(form.getFieldsValue(), {
                ...data,
                district,
                ward
              })}
            </p>
          </div>
          <div className={styles['guide']}>
            <label>Hướng dẫn:</label>
            <div className={styles['guideContent']}>
              <Image src={cancelIcon} width={20} height={20} layout='fixed' />
              <p>
                123 Bùi Đình Túy, quận Bình Thạnh, TPHCM Bùi Đình Túy, quận Bình
                Thạnh, TPHCM
              </p>
            </div>
            <div className={styles['guideContent']}>
              <Image src={okIcon} width={20} height={20} layout='fixed' />
              <p>123 Bùi Đình Túy, quận Bình Thạnh, TPHCM</p>
            </div>
          </div>
          <div className={styles['btnWrapper']}>
            <MPButton
              className={styles['btnCancel']}
              onClick={toggleConfirmAddress}
            >
              Chỉnh sửa lại
            </MPButton>
            <MPButton
              className={styles['btnConfirm']}
              type='primary'
              onClick={handleConfirmAddressInfo}
            >
              Đồng ý
            </MPButton>
          </div>
        </Modal>
      )}
    </div>
  )
}

export default MPCreateProfileAppCard
