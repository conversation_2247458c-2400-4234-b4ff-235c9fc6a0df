import React, { useState, useEffect } from 'react'
import { Col, Row } from 'antd'
import Link from 'next/link'
import Image from 'next/image'
import Slider from 'react-slick'
import { MPButton } from '@medpro-libs/libs'
import LiteYouTubeEmbed from './LiteYoutube'
import SYTCanTho from '../images/syt_can_tho.png'
import DanTri from '../images/dan-tri.png'
import HTV from '../images/htv.png'
import Next from '../images/next.svg'
import NLD from '../images/nguoi-lao-dong-logo.png'
import Prev from '../images/prev.svg'
import ThanhNien from '../images/thanh-nien-logo.png'
import THVL from '../images/thvl-logo.png'
import TuoiTre from '../images/tuoi-tre-logo.png'
import VTV1 from '../images/vtv1-logo.png'
import styles from '../styles.module.less'

const Media = (props: any) => {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth <= 991)
    }

    // Set initial value
    checkIfMobile()

    // Add event listener
    window.addEventListener('resize', checkIfMobile)

    // Clean up
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  const SampleNextArrow = (props: any) => {
    return (
      <MPButton type='default' {...props}>
        <Image
          src={Next}
          width={26}
          height={46}
          layout='responsive'
          alt='Next slide'
        />
      </MPButton>
    )
  }

  const SamplePrevArrow = (props: any) => {
    return (
      <MPButton type='default' {...props}>
        <Image
          src={Prev}
          width={26}
          height={46}
          layout='responsive'
          alt='Previous slide'
        />
      </MPButton>
    )
  }

  const videoSettings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    nextArrow: <SampleNextArrow />,
    prevArrow: <SamplePrevArrow />
  }

  const logoSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 5,
    slidesToScroll: 5,
    rows: 2,
    autoplay: true,
    autoplaySpeed: 4000,
    arrows: false,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2
        }
      },
      {
        breakpoint: 991,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3
        }
      }
    ]
  }

  const logoItems = [
    {
      id: 1,
      href: 'https://thanhnien.vn/benh-vien-mat-tphcm-chinh-thuc-ra-mat-ung-dung-dat-lich-1851509686.htm',
      src: ThanhNien,
      alt: 'Báo Thanh Niên'
    },
    {
      id: 2,
      href: 'https://tuoitre.vn/chuyen-doi-so-nganh-y-te-can-bat-dau-tu-viec-dang-ky-kham-chua-benh-20230216172403962.htm',
      src: TuoiTre,
      alt: 'Báo Tuổi Trẻ'
    },
    {
      id: 3,
      href: 'https://dantri.com.vn/suc-khoe/dat-kham-nhanh-voi-ung-dung-y-te-medpro-20240316123527449.htm',
      src: DanTri,
      alt: 'Báo Dân Trí'
    },
    {
      id: 4,
      href: 'https://nld.com.vn/suc-khoe/khong-con-canh-cho-doi-khi-di-kham-tai-benh-vien-mat-tp-hcm-20221014113328896.htm',
      src: NLD,
      alt: 'Báo Người Lao Động'
    },
    {
      id: 5,
      href: 'https://www.youtube.com/watch?v=glMog0sSvAM',
      src: HTV,
      alt: 'Truyền hình HTV'
    },
    {
      id: 6,
      href: 'https://soytecantho.vn/?tabid=979&NDID=292386&key=Thong_bao_chinh_thuc_ra_mat_ung_dung_Cham_soc_suc_khoe_Can_Tho_',
      src: SYTCanTho,
      alt: 'SYT Cần Thơ'
    },
    {
      id: 7,
      href: 'https://www.facebook.com/watch/?v=1796311490532791',
      src: VTV1,
      alt: 'Truyền hình VTV1'
    },
    {
      id: 8,
      href: 'https://youtu.be/KUuEjKxHpcU',
      src: THVL,
      alt: 'Truyền hình THVL'
    }
  ]

  const renderLogoItem = (item: any) => (
    <Link href={item.href} key={item.id}>
      <a target='_blank' rel='follow' className={styles['logoItem']}>
        <Image
          className={styles['imageLogo']}
          src={item.src}
          layout='fill'
          loading='lazy'
          alt={item.alt}
        />
      </a>
    </Link>
  )

  return (
    <>
      <div className={styles['partnerContainer']}>
        <div className={styles['header']}>
          <h2 className={styles['title']}>Truyền thông nói gì về Medpro</h2>
        </div>
        <div className={styles['listLogo']}>
          {isMobile ? (
            <Slider {...logoSettings} className={styles['logoSlider']}>
              {logoItems.map((item) => (
                <div key={item.id}>{renderLogoItem(item)}</div>
              ))}
            </Slider>
          ) : (
            <Row gutter={[53, 25]}>
              <Col
                className={styles['logoItem']}
                span={8}
                lg={6}
                md={8}
                sm={12}
                xs={24}
                style={{ paddingLeft: '16px', paddingRight: '16px' }}
              >
                <Link href='https://thanhnien.vn/benh-vien-mat-tphcm-chinh-thuc-ra-mat-ung-dung-dat-lich-1851509686.htm'>
                  <a target='_blank' rel='follow'>
                    <Image
                      className={styles['imageLogo']}
                      src={ThanhNien}
                      layout='fill'
                      loading='lazy'
                      alt='Báo Thanh Niên'
                    />
                  </a>
                </Link>
              </Col>
              <Col
                className={styles['logoItem']}
                span={8}
                lg={6}
                md={8}
                sm={12}
                xs={24}
              >
                <Link href='https://tuoitre.vn/chuyen-doi-so-nganh-y-te-can-bat-dau-tu-viec-dang-ky-kham-chua-benh-20230216172403962.htm'>
                  <a target='_blank' rel='follow'>
                    <Image
                      className={styles['imageLogo']}
                      src={TuoiTre}
                      layout='fill'
                      objectFit='contain'
                      loading='lazy'
                      alt='Báo Tuổi Trẻ'
                    />
                  </a>
                </Link>
              </Col>
              <Col
                className={styles['logoItem']}
                span={8}
                lg={6}
                md={8}
                sm={12}
                xs={24}
              >
                <Link href='https://dantri.com.vn/suc-khoe/dat-kham-nhanh-voi-ung-dung-y-te-medpro-20240316123527449.htm'>
                  <a target='_blank' rel='follow'>
                    <Image
                      className={styles['imageLogo']}
                      src={DanTri}
                      layout='fill'
                      loading='lazy'
                      alt='Báo Dân Trí'
                    />
                  </a>
                </Link>
              </Col>
              <Col
                className={styles['logoItem']}
                span={8}
                lg={6}
                md={8}
                sm={12}
                xs={24}
              >
                <Link href='https://nld.com.vn/suc-khoe/khong-con-canh-cho-doi-khi-di-kham-tai-benh-vien-mat-tp-hcm-20221014113328896.htm'>
                  <a target='_blank' rel='follow'>
                    <Image
                      className={styles['imageLogo']}
                      src={NLD}
                      layout='fill'
                      loading='lazy'
                      alt='Báo Người Lao Động'
                    />
                  </a>
                </Link>
              </Col>
              <Col
                className={styles['logoItem']}
                span={8}
                lg={6}
                md={8}
                sm={12}
                xs={24}
              >
                <Link href='https://www.youtube.com/watch?v=glMog0sSvAM'>
                  <a target='_blank' rel='follow'>
                    <Image
                      className={styles['imageLogo']}
                      src={HTV}
                      layout='fill'
                      loading='lazy'
                      alt='Truyền hình HTV'
                    />
                  </a>
                </Link>
              </Col>
              <Col
                className={styles['logoItem']}
                span={8}
                lg={6}
                md={8}
                sm={12}
                xs={24}
                style={{ padding: '0' }}
              >
                <Link href='https://soytecantho.vn/?tabid=979&NDID=292386&key=Thong_bao_chinh_thuc_ra_mat_ung_dung_Cham_soc_suc_khoe_Can_Tho_'>
                  <a target='_blank' rel='follow'>
                    <Image
                      className={styles['imageLogo']}
                      src={SYTCanTho}
                      layout='fill'
                      loading='lazy'
                      alt='SYT Cần Thơ'
                    />
                  </a>
                </Link>
              </Col>
              <Col
                className={styles['logoItem']}
                span={8}
                lg={6}
                md={8}
                sm={12}
                xs={24}
              >
                <Link href='https://www.facebook.com/watch/?v=1796311490532791'>
                  <a target='_blank' rel='follow'>
                    <Image
                      className={styles['imageLogo']}
                      src={VTV1}
                      layout='fill'
                      loading='lazy'
                      alt='Truyền hình VTV1'
                    />
                  </a>
                </Link>
              </Col>
              <Col
                className={styles['logoItem']}
                span={8}
                lg={6}
                md={8}
                sm={12}
                xs={24}
              >
                <Link href='https://youtu.be/KUuEjKxHpcU'>
                  <a target='_blank' rel='follow'>
                    <Image
                      className={styles['imageLogo']}
                      src={THVL}
                      layout='fill'
                      loading='lazy'
                      alt='Truyền hình THVL'
                    />
                  </a>
                </Link>
              </Col>
            </Row>
          )}
        </div>
        <Slider {...videoSettings} className={styles['slider']}>
          {props.data?.map((item: any, index: number) => (
            <div className={styles['item']} key={index}>
              <LiteYouTubeEmbed
                id='zfmhCJgWx8Y'
                title='Sự lợi ích của Ứng dụng đặt khám nhanh Medpro'
                thumbnail='https://cdn-pkh.longvan.net/prod-partner/54a320ac-1a85-4a86-bc83-57e99265fc9c-thumnail-video.webp'
              />
            </div>
          ))}
        </Slider>
      </div>
    </>
  )
}

export default Media
