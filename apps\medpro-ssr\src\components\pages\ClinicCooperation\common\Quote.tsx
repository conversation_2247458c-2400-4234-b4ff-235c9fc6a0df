import React from 'react'
import cx from 'classnames'
import Image from 'next/image'
import { handleN<PERSON>ber, MPButton, Valid } from '@medpro-libs/libs'
import { Form, Input, InputNumber } from 'antd'
import BgQuote from '../images/bgQuote.jpg'
import ListPartner from '../images/listPartner.png'
import IconRightArrow from '../images/iconRightArrow.png'
import styles from '../styles.module.less'

const Quote = (props: any) => {
  const valid = new Valid()
  const [form] = Form.useForm()
  const { onSubmit, loading, windowWidth } = props
  const onFinish = (values: any) => {
    onSubmit(values, form)
  }
  return (
    <div
      className={cx(styles['wrapper'])}
      style={{
        backgroundImage: `url(${BgQuote.src})`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundPosition: 'bottom center',
        minHeight: '740px'
      }}
    >
      <div className={styles['quote']}>
        <div className={styles['card']}>
          <div className={styles['left']}>
            <div className={styles['captionText']}>
              {windowWidth && windowWidth < 576 ? (
                <h2>Nhận tư vấn & báo giá khám doanh nghiệp</h2>
              ) : (
                <h2>
                  Nhận tư vấn & báo giá <br /> khám doanh nghiệp
                </h2>
              )}

              <p>
                Bạn cần tư vấn và nhận báo giá dịch vụ khám doanh nghiệp hoặc
                giải đáp thắc mắc?
                <strong> Đăng ký ngay</strong> để được đội ngũ Medpro hỗ trợ
                nhanh chóng!
              </p>
            </div>
            <div className={styles['captionImg']}>
              <Image src={ListPartner} alt={''} />
            </div>
          </div>
          <div className={styles['right']}>
            <Form
              form={form}
              size={'large'}
              layout='vertical'
              onFinish={onFinish}
              className={styles['formCard']}
            >
              <Form.Item
                label='Họ và tên'
                name='fullName'
                rules={[{ validator: valid.name, required: true }]}
              >
                <Input placeholder='Nhập họ và tên' />
              </Form.Item>
              <Form.Item
                label='Công ty / tổ chức'
                name='company'
                rules={[{ validator: valid.trimRequired, required: true }]}
              >
                <Input placeholder='Nhập công ty / tổ chức' />
              </Form.Item>
              <div className={styles['groupFormItem']}>
                <Form.Item
                  label='Số lượng khám'
                  name='quantity'
                  rules={[
                    { required: true, message: 'Vui lòng nhập thông tin!' }
                  ]}
                >
                  <InputNumber min={0} placeholder='Nhập số lượng khám' />
                </Form.Item>
                <Form.Item
                  label='Số điện thoại'
                  name='phoneNumber'
                  rules={[{ validator: valid.mobile, required: true }]}
                >
                  <Input
                    placeholder='Nhập số điện thoại'
                    maxLength={10}
                    onKeyPress={handleNumber}
                  />
                </Form.Item>
              </div>
              <Form.Item
                name='note'
                label='Ghi chú'
                rules={[{ validator: valid.trimRequired, required: true }]}
              >
                <Input.TextArea
                  showCount={false}
                  maxLength={100}
                  rows={4}
                  placeholder='Nhập ghi chú'
                />
              </Form.Item>
              <Form.Item label=''>
                <MPButton
                  className={styles['btnContactForm']}
                  htmlType='submit'
                  loading={loading}
                >
                  <span>Đăng ký ngay</span>{' '}
                  <Image src={IconRightArrow} alt='' width={24} height={24} />
                </MPButton>
              </Form.Item>
            </Form>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Quote
