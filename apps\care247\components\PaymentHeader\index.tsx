import React from 'react'
import styles from './styles.module.less'

interface PaymentHeaderProps {
  timeLeft: number
}

const PaymentHeader: React.FC<PaymentHeaderProps> = ({ timeLeft }) => {
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className={styles.header}>
      <div className={styles.logo}>
        <span className={styles.logoText}>Care247</span>
        <span className={styles.logoSubtext}>Bạn đồng hành</span>
      </div>
      <div className={styles.timer}>
        <span className={styles.timerText}>Giao dịch kết thúc sau</span>
        <span className={styles.timerValue}>{formatTime(timeLeft)}</span>
      </div>
    </div>
  )
}

export default PaymentHeader
