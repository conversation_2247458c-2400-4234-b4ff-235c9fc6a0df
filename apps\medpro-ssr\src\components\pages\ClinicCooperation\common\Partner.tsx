import React from 'react'
import cx from 'classnames'
import Slider from 'react-slick'
import 'slick-carousel/slick/slick.css'
import 'slick-carousel/slick/slick-theme.css'
import Image from 'next/image'
import BgPartner from '../images/bgPartner.png'
import styles from '../styles.module.less'

const Partner = (props: any) => {
  const { partners } = props
  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 5,
    slidesToScroll: 5,
    rows: 2,
    autoplay: true,
    autoplaySpeed: 4000,
    arrows: false,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
        }
      },
      {
        breakpoint: 991,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
        }
      },
    ]
  }
  return (
    <div
      className={cx(styles['wrapper'])}
      style={{
        backgroundImage: `url(${BgPartner.src})`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundPosition: 'bottom center',
        minHeight: '500px',
        backgroundColor: '#f1f8ff'
      }}
    >
      <div className={cx(styles['partner'])}>
        <div className={styles['titleWrapper']}>
          <h3>300+ Cơ sở y tế trên nền tảng medpro</h3>
        </div>
        <div className={styles['slidesWrapper']}>
          <Slider {...settings}>
            {partners.map((item: any, index: number) => (
              <div key={index} className={styles['slideItem']}>
                <Image src={item?.image} alt={''} width={80} height={80} />
              </div>
            ))}
          </Slider>
        </div>
      </div>
    </div>
  )
}

export default Partner
