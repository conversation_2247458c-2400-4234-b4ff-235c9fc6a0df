import { Col, Divider, Form, Modal, Row } from 'antd'
import cx from 'classnames'
import moment from 'moment'
import * as React from 'react'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { BiLeftArrowAlt } from 'react-icons/bi'
import validator from 'validator'
import Image from 'next/image'
import { matchFullAddress } from '../../common/func'
import MPButton from '../MPButton'
import MobileSelectDrawer from '../common/MobileSelectDrawer'
import SexSelectDrawer from '../common/SexSelectDrawer'
import QRScanner from './common/QRScanner'
import { handleDetails } from './common/handleDetails'
import { handleRelative } from './common/handleRelative'
import { handleAddress } from './common/handleAdress'
import IconScanQR from './common/iconScanQR.svg'
import styles from './styles.module.less'
import cancelIcon from '../../common/images/cancel_icon.jpg'
import okIcon from '../../common/images/ok_icon.jpg'

export interface Props {
  handleSubmit: (values: any) => void
  extractInformation: (values: any) => void
  data: any
  handleChangeAddress: any
  phoneLocale: any[]
  district: any[]
  ward: any[]
  partnerId: any
  submitting?: boolean
}

const patientYearOldAccepted = 16

const convertToAge = (dateString: string) => {
  let age = -1
  const isValid = moment(dateString, 'DD/MM/YYYY', true).isValid()
  if (isValid) {
    age = moment().diff(moment(dateString, 'DD/MM/YYYY'), 'years')
  }
  return age
}

const MPCompleteProfileAppCard = ({
  handleSubmit,
  extractInformation,
  data,
  handleChangeAddress,
  phoneLocale,
  district,
  ward,
  partnerId,
  submitting
}: Props) => {
  const router = useRouter()
  const scannerRef = React.useRef<any>(null)
  const isCreate = router.pathname === '/tao-moi-ho-so'
  const editProfile = router.query.editProfile as string
  const patient = data.patient
  const [dataSelect, setDataSelect] = useState<any>({})
  const [openPickSex, setOpenPickSex] = useState(false)
  const [openPickCity, setOpenPickCity] = useState(false)
  const [openScanQR, setOpenScanQR] = useState(false)
  const [scanMessage, setScanMessage] = useState<any>(undefined)
  const [isScanMessage, setIsScanMessage] = useState<boolean>(false)
  const [phonePrefix, setPhonePrefix] = useState(patient?.prefix)
  const [phonePrefixRelative, setPhonePrefixRelative] = useState(
    patient?.prefix
  )
  const [iso, setIso] = useState<any>(patient?.iso)
  const [phoneNumber, setPhoneNumber] = useState(patient?.phoneNumber)
  const [relativePhoneNumber, setRelativePhoneNumber] = useState(
    patient?.relative_mobile
  )
  const [isoRelative, setIsoRelative] = useState<any>(
    patient?.relativeMobileLocaleIso
  )
  const [showRelative, setShowRelative] = useState(false)
  const [age, setAge] = useState(-1)
  const [confirmAddress, setConfirmAddress] = useState(false)
  const [isForeign, setIsForeign] = useState(false)

  const [form] = Form.useForm()

  useEffect(() => {
    if (patient && !isCreate) {
      form.setFieldsValue(patient)
      if (patient?.birthdate) {
        toggleAge(patient.birthdate)
      }
      !patient?.relation?.relative_type_id &&
        form.setFieldValue('relative_type_id', null)
      !patient?.birthdate && form.setFieldValue('birthdate', patient.year)
      patient?.country_code &&
        form.setFieldValue('country_code', patient.country_code)
      !patient?.profession_id && form.setFieldValue('profession_id', null)
      !patient?.dantoc_id && form.setFieldValue('dantoc_id', 'medpro_1')
      !patient?.district_id && form.setFieldValue('district_id', null)
      !patient?.ward_id && form.setFieldValue('ward_id', null)
      if (patient?.birthdate) {
        const age = convertToAge(patient?.birthdate)
        setShowRelative(age < patientYearOldAccepted)
        age < patientYearOldAccepted &&
          form.setFieldValue('profession_id', 'medpro_952')
      }
      if (patient?.relation?.relative_mobile) {
        form.setFieldValue(
          'relative_mobile',
          patient?.relation?.relative_mobile
        )
      }
      if (!['Việt Nam', 'VIE'].includes(patient?.country_code)) {
        setIsForeign(true)
      } else {
        setIsForeign(false)
      }
    }
  }, [data])

  useEffect(() => {
    if (patient?.prefix) {
      setPhonePrefix(patient?.prefix)
      setPhonePrefixRelative(patient?.prefix)
    }
    if (patient?.iso) {
      setIso(patient?.iso)
    }
    if (patient?.relativeMobileLocaleIso) {
      setIsoRelative(patient?.relativeMobileLocaleIso)
    }
    if (patient?.phoneNumber) {
      setPhoneNumber(patient?.phoneNumber)
    }
    if (patient?.relative_mobile) {
      setRelativePhoneNumber(patient?.relative_mobile)
    } else {
      setRelativePhoneNumber('')
    }
  }, [
    patient?.prefix,
    patient?.iso,
    patient?.relativeMobileLocaleIso,
    patient?.phoneNumber,
    patient?.relative_mobile
  ])

  const handleFormChange = (fieldId?: string) => {
    const {
      country_code,
      city_id,
      district_id,
      ward_id,
      birthdate,
      relative_type_id,
      profession_id,
      dantoc_id
    } = form.getFieldsValue()
    if (fieldId === 'city_id' && city_id) {
      handleChangeAddress('district', city_id)
      form.setFieldValue('district_id', null)
      form.setFieldValue('ward_id', null)
    }
    if (fieldId === 'district_id' && district_id) {
      handleChangeAddress('ward', district_id)
      form.setFieldValue('ward_id', null)
      form.validateFields(['district_id'])
    }
    if (fieldId === 'ward_id' && ward_id) {
      form.validateFields(['ward_id'])
    }
    if (fieldId === 'relative_type_id' && relative_type_id) {
      form.setFieldValue('relative_type_id', relative_type_id)
      form.validateFields(['relative_type_id'])
    }
    if (fieldId === 'profession_id' && profession_id) {
      form.validateFields(['profession_id'])
    }
    if (country_code) {
      setIsForeign(country_code !== 'VIE')
      switch (country_code) {
        case 'VIE':
        case 'medpro_VIE':
          form.setFieldValue(
            'dantoc_id',
            dantoc_id === 'medpro_82' ? 'medpro_1' : dantoc_id
          )
          break
        default:
          form.setFieldValue('dantoc_id', 'medpro_82')
          break
      }
    }
    if (birthdate) {
      const age = convertToAge(birthdate)
      setShowRelative(age < patientYearOldAccepted)
      age < patientYearOldAccepted
        ? form.setFieldValue('profession_id', 'medpro_952')
        : form.setFieldValue(
            'profession_id',
            profession_id !== 'medpro_952' ? profession_id : null
          )
    }
  }

  const selectPhonePrefix = (prefix: string, iso: string) => {
    setPhonePrefix(prefix)
    setIso(iso)
    form.setFieldValue('mobileLocaleIso', iso)
    changePhoneNumber(phoneNumber, prefix, iso)
  }

  const selectPhonePrefixRelative = (prefix: string, iso: string) => {
    setPhonePrefixRelative(prefix)
    setIsoRelative(iso)
    form.setFieldValue('relativeMobileLocaleIso', iso)
    changeRelativePhoneNumber(relativePhoneNumber, prefix, iso)
  }

  const changePhoneNumber = (
    phone: string,
    prefix?: string,
    isoPrams?: string
  ) => {
    handleFormChange()
    if (phone) {
      setPhoneNumber(phone)
      const isoValidate = isoPrams ? isoPrams : iso
      if (phone?.startsWith('0') && isoValidate === 'vi-VN') {
        phone = phone.substring(1)
      }
      form.setFieldValue('mobile', `${prefix ? prefix : phonePrefix}${phone}`)
      if (
        !validator.isMobilePhone(
          `${prefix ? prefix : phonePrefix}${phone}`,
          isoValidate
        )
      ) {
        form.setFields([
          {
            name: 'mobile',
            errors: ['Số điện thoại không đúng!']
          }
        ])
      } else {
        form.setFields([
          {
            name: 'mobile',
            errors: []
          }
        ])
      }
    } else {
      setPhoneNumber('')
      form.setFieldValue('mobile', '')
      if (!showRelative) {
        form.setFields([
          {
            name: 'mobile',
            errors: ['Vui lòng nhập số điện thoại!']
          }
        ])
      } else {
        form.setFields([
          {
            name: 'mobile',
            errors: []
          }
        ])
      }
    }
  }

  const changeRelativePhoneNumber = (
    phone: string,
    prefix?: string,
    isoPrams?: string
  ) => {
    handleFormChange()
    if (phone) {
      setRelativePhoneNumber(phone)
      const isoValidate = isoPrams ? isoPrams : isoRelative
      if (phone?.startsWith('0') && isoValidate === 'vi-VN') {
        phone = phone.substring(1)
      }
      form.setFieldValue(
        'relative_mobile',
        `${prefix ? prefix : phonePrefixRelative}${phone}`
      )
      !phoneNumber &&
        form.setFieldValue(
          'mobile',
          `${prefix ? prefix : phonePrefixRelative}${phone}`
        )
      if (
        !validator.isMobilePhone(
          `${prefix ? prefix : phonePrefixRelative}${phone}`,
          isoValidate
        )
      ) {
        form.setFields([
          {
            name: 'relative_mobile',
            errors: ['Số điện thoại không đúng!']
          }
        ])
      } else {
        form.setFields([
          {
            name: 'relative_mobile',
            errors: []
          }
        ])
      }
    } else {
      setRelativePhoneNumber('')
      form.setFieldValue('relative_mobile', '')
      form.setFields([
        {
          name: 'relative_mobile',
          errors: ['Vui lòng nhập số điện thoại!']
        }
      ])
    }
  }

  const openSelect = (field: any) => {
    switch (field.id) {
      case 'sex':
        setOpenPickSex(true)
        return
      default:
        setDataSelect({ ...field })
        setOpenPickCity(true)
        return
    }
  }

  const onFinish = (values: any) => {
    const day = values.birthdate.split('/')[0]
    const month = values.birthdate.split('/')[1]
    const year = values.birthdate.split('/')[2]

    const splitName = values.name.split(' ')
    const name = splitName[splitName.length - 1]
    const surname = splitName.slice(0, -1).join(' ')

    const data = {
      ...values,
      name,
      surname,
      mobileLocaleIso: iso,
      relativeMobileLocaleIso: isoRelative,
      birthdate: moment(`${year}-${month}-${day}`, 'YYYY-MM-DD')
        .utc(true)
        .toISOString()
    }

    handleSubmit(data)
  }

  const toggleSexDrawer = () => {
    setOpenPickSex((prev) => !prev)
  }

  const toggleCityDrawer = () => {
    setOpenPickCity((prev) => !prev)
  }

  const toggleAge = (value: string) => {
    const isValid = moment(value, 'DD/MM/YYYY', true).isValid()
    if (isValid) {
      setAge(moment().diff(moment(value, 'DD/MM/YYYY'), 'years'))
    }
  }

  const handleClickScanQR = () => {
    setOpenScanQR(!openScanQR)
  }

  const handleCancelScanQR = () => {
    if (scannerRef.current?.stopScanning) {
      scannerRef.current.stopScanning()
    }
    if (scannerRef.current?.stopScanningImg) {
      scannerRef.current.stopScanningImg()
    }
    setOpenScanQR(false)
  }

  const handleCancelScanMessage = () => {
    setIsScanMessage(false)
    setScanMessage(undefined)
  }

  const handleScanSuccess = async (decodedText: any) => {
    setOpenScanQR(false)
    if (scannerRef.current?.stopScanningImg) {
      scannerRef.current.stopScanningImg()
    }
    const extractInfo = (await extractInformation(decodedText)) as any
    if (extractInfo) {
      handleChangeAddress('district', extractInfo?.city_id)
      handleChangeAddress('ward', extractInfo?.district_id)
      form.setFieldsValue({
        name: extractInfo?.fullname,
        birthdate: extractInfo?.birthdate,
        sex: Number(extractInfo?.sex),
        insuranceCode: extractInfo?.insuranceId,
        cmnd: extractInfo?.cmnd,
        country_code: extractInfo?.country_code,
        city_id: extractInfo?.city_id,
        district_id: extractInfo?.district_id,
        ward_id: extractInfo?.ward_id,
        address: extractInfo?.address
      })
      handleFormChange()
    } else {
      setScanMessage('Mã QR không hợp lệ!')
      setIsScanMessage(true)
    }
  }

  const toggleConfirmAddress = () => {
    form
      .validateFields()
      .then(() => {
        setConfirmAddress((preState) => !preState)
      })
      .catch((errorInfo) => {
        console.log('Validation failed:', errorInfo)
        const firstErrorField = errorInfo.errorFields[0].name
        form.scrollToField(firstErrorField, {
          behavior: (actions) =>
            actions.forEach(({ el, top, left }) => {
              // 100 is height of element
              const adjustedTop = top - 100
              el.scrollTop = adjustedTop
              el.scrollLeft = left
            })
        })
      })
  }

  const handleConfirmAddressInfo = () => {
    form.submit()
  }

  const DividerCustomer = (title: string) => {
    return (
      <Divider
        orientation='left'
        plain
        orientationMargin={0}
        className={styles['Divider']}
      >
        {title}
      </Divider>
    )
  }
  return (
    <div className={styles['main']}>
      <div className={styles['headerTitle']}>
        <BiLeftArrowAlt
          size={24}
          onClick={() => router.back()}
          className={styles['headerSide']}
        />
        <p>
          {isCreate
            ? 'Tạo hồ sơ'
            : editProfile
            ? 'Sửa hồ sơ'
            : 'Hoàn tất thông tin'}
        </p>
        <div className={styles['headerSide']} />
      </div>
      <Form
        form={form}
        layout='vertical'
        initialValues={
          isCreate
            ? {
                country_code: 'VIE',
                dantoc_id: 'medpro_1'
              }
            : undefined
        }
        onFinish={onFinish}
        onFieldsChange={() => handleFormChange()}
        className={styles['listInfo']}
      >
        {isCreate && (
          <Row gutter={12}>
            {isCreate && (
              <div className={cx([styles.scanButtons])}>
                <MPButton
                  className={cx([styles.scanButton])}
                  onClick={handleClickScanQR}
                >
                  <Image src={IconScanQR} alt={''} />{' '}
                  <span>Quét mã BHYT/CCCD</span>
                </MPButton>
                <div className={styles.orText}>Hoặc nhập thủ công</div>
              </div>
            )}
          </Row>
        )}
        <Row gutter={12}>
          {DividerCustomer('Thông tin chung')}
          {handleDetails({
            data,
            district,
            ward,
            openSelect,
            phoneLocale,
            selectPhonePrefix,
            form,
            iso,
            phoneNumber,
            changePhoneNumber,
            showRelative,
            partnerId,
            age,
            toggleAge,
            isForeign
          }).map((item, index) => {
            return (
              <Col key={index} span={item.width === 'fuild' ? 24 : 12}>
                <div
                  className={cx(
                    styles['inputItem'],
                    item.id === 'birthdate' && styles['inputItemHalfLeft'],
                    item.id === 'sex' && styles['inputItemHalfRight']
                  )}
                >
                  {item?.enter && item?.enter(item)}
                </div>
              </Col>
            )
          })}

          {DividerCustomer(
            isForeign ? 'Thông tin lưu trú' : 'Địa chỉ theo CCCD'
          )}
          {handleAddress({
            data,
            district,
            ward,
            openSelect,
            form
          }).map((item, index) => {
            return (
              <Col
                key={index}
                span={item.width === 'fuild' ? 24 : 12}
                hidden={item.hidden}
              >
                <div className={cx(styles['inputItem'])}>
                  {item?.enter && item?.enter(item)}
                </div>
              </Col>
            )
          })}
          {showRelative && (
            <>
              {DividerCustomer('Nhập thông tin nhân thân')}
              {handleRelative(
                data,
                openSelect,
                phoneLocale,
                selectPhonePrefixRelative,
                form,
                isoRelative,
                changeRelativePhoneNumber,
                relativePhoneNumber
              ).map((item, index) => {
                return (
                  <Col key={index} span={item.width === 'fuild' ? 24 : 12}>
                    <div
                      className={cx(
                        styles['inputItem'],
                        item.id === 'birthdate' && styles['inputItemHalfLeft'],
                        item.id === 'sex' && styles['inputItemHalfRight']
                      )}
                    >
                      {item?.enter && item?.enter(item)}
                    </div>
                  </Col>
                )
              })}
            </>
          )}
        </Row>
        <div className={styles['footerBtn']}>
          <MPButton
            className={styles['btnSubmit']}
            type='primary'
            full='true'
            onClick={toggleConfirmAddress}
          >
            {isCreate ? 'Tạo mới' : 'Hoàn tất'}
          </MPButton>
        </div>
      </Form>

      {openPickSex && (
        <SexSelectDrawer
          isOpen={openPickSex}
          toggleDrawer={toggleSexDrawer}
          form={form}
          handleFormChange={handleFormChange}
        />
      )}
      {openPickCity && (
        <MobileSelectDrawer
          isOpen={openPickCity}
          toggleDrawer={toggleCityDrawer}
          form={form}
          handleFormChange={handleFormChange}
          dataSelect={dataSelect}
        />
      )}
      {openScanQR && (
        <Modal
          centered
          title=''
          open={openScanQR}
          closable={true}
          footer={false}
          onCancel={handleCancelScanQR}
          className={styles['modalScanQR']}
        >
          <QRScanner ref={scannerRef} onScanSuccess={handleScanSuccess} />
        </Modal>
      )}
      {confirmAddress && (
        <Modal
          open={confirmAddress}
          footer={null}
          closable={false}
          centered
          className={styles['modalAddress']}
        >
          <>
            <div className={styles['title']}>Thông báo</div>
            <p className={styles['desc']}>
              Vui lòng kiểm tra địa chỉ bạn đã cung cấp. Nếu khác với{' '}
              {isForeign ? 'thông tin lưu trú' : 'địa chỉ theo CCCD'}, nhấn
              'Chỉnh sửa lại' để cập nhật
            </p>
          </>
          <div className={styles['input']}>
            <p className={styles['add']}>Địa chỉ:</p>
            <p className={styles['address']}>
              {matchFullAddress(form.getFieldsValue(), {
                ...data,
                district,
                ward
              })}
            </p>
          </div>
          <div className={styles['guide']}>
            <label>Hướng dẫn:</label>
            <div className={styles['guideContent']}>
              <Image src={cancelIcon} width={20} height={20} layout='fixed' />
              <p>
                123 Bùi Đình Túy, quận Bình Thạnh, TPHCM Bùi Đình Túy, quận Bình
                Thạnh, TPHCM
              </p>
            </div>
            <div className={styles['guideContent']}>
              <Image src={okIcon} width={20} height={20} layout='fixed' />
              <p>123 Bùi Đình Túy, quận Bình Thạnh, TPHCM</p>
            </div>
          </div>
          <div className={styles['btnWrapper']}>
            <MPButton
              className={styles['btnCancel']}
              onClick={toggleConfirmAddress}
            >
              Chỉnh sửa lại
            </MPButton>
            <MPButton
              className={styles['btnConfirm']}
              type='primary'
              onClick={handleConfirmAddressInfo}
              loading={submitting}
            >
              Đồng ý
            </MPButton>
          </div>
        </Modal>
      )}
      {isScanMessage && scanMessage ? (
        <Modal
          title={<div style={{ textAlign: 'center' }}>Thông báo</div>}
          centered
          open={isScanMessage}
          closable={true}
          footer={false}
          onCancel={handleCancelScanMessage}
        >
          {scanMessage}
        </Modal>
      ) : null}
    </div>
  )
}

export default MPCompleteProfileAppCard
