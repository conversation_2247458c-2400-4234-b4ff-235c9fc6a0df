import React, { useEffect, useState } from 'react'
import { newDefaultLayout } from '../../layout'
import { useDispatch } from 'react-redux'
import { useRouter } from 'next/router'
import {
  AlertModal,
  MPButton,
  MPContainer,
  MPDetailHospital,
  MPDetailHospitalListing,
  PageRoutesV2,
  RedirectHandler,
  useWindowResize
} from '@medpro-libs/libs'
import {
  selectExtraConfig,
  selectPartnerSlug
} from '../../store/hospital/selector'
import { useAppSelector } from '../../store/hooks'
import { IHospitalDescription, IHospitalExInfo } from '@medpro-libs/types'
import { GetServerSidePropsContext, GetStaticPathsContext } from 'next'
import {
  fetchFeatureByPartner,
  fetchHospitalDescriptionServer,
  fetchPartnerInfoBySlugServer,
  fetchPartnerInfoBySlugV2Server
} from '../../utils/utils.query-server'
import { bookingActions, selectFeature } from '../../store/booking/slice'
import { size } from 'lodash'
import { handleUpdateLinkFromPartner } from '../../utils/utils.function'
import { downloadApp } from '../../src/components/pages/Home/staticData'
import { getError } from '../../utils/utils.error'
import { SSG_REVALIDATE_SECOND } from '../../utils/utils.contants'
import { setBreadcrumb } from '../../store/total/slice'
import styles from './styles.module.less'
import client from '../../config/medproSdk'
import { hospitalActions } from '../../store/hospital/hospitalSlice'
import useWindowDimensions from '../../hooks/useWindowDimesion'
import { anchorLink } from 'libs/medpro-component-libs/src/lib/ui/MPDetailHospitalListing/utils/constant'

interface BenhVienPageProps {
  hospitalDescription: IHospitalDescription
  features: any[]
  partnerInfo: any
}

export const BenhVienPage = (props: BenhVienPageProps) => {
  const router = useRouter()
  const { hospitalDescription, partnerInfo } = props
  const typeLayoutHospital = hospitalDescription?.layout === 'LISTING'
  const openType = router.query?.openType
  const partnerSlug = useAppSelector(selectPartnerSlug)
  const extraConfig = useAppSelector(selectExtraConfig)
  const isMobile = useWindowResize(576)
  const [featuresDrawerVisible, setFeaturesDrawerVisible] = useState(false)
  const [warningMessage, setWarningMessage] = useState('')
  const [openFeature, setOpenFeature] = useState<any>({
    item: {},
    load: false,
    open: false
  })

  const { windowWidth } = useWindowDimensions()
  const [selectedData, setSelectedData] = useState<any>()
  const dispatch = useDispatch()
  useEffect(() => {
    dispatch(setBreadcrumb([{ title: partnerInfo?.name }]))
  }, [])
  useEffect(() => {
    if (openType) setOpenFeature({ item: props.partnerInfo, open: true })
  }, [openType])

  useEffect(() => {
    if (size(openFeature?.item) > 0) {
      if (props.features.length === 1) {
        const featureOnly = props.features[0]
        if (!featureOnly.message && !featureOnly.warningMessage) {
          onFinish({
            message: 'Thành công',
            feature: featureOnly,
            index: featureOnly.index
          })
        } else {
          setFeaturesDrawerVisible(true)
        }
      } else {
        setFeaturesDrawerVisible(true)
      }
    }
  }, [openFeature])
  const onBooking = (params: any) => {
    if (isMobile) {
      setOpenFeature({ item: { ...params }, open: false })
      return
    } else {
      router.push(
        PageRoutesV2.bookingType({
          appPartnerSlug: partnerSlug,
          query: {
            bookingPage: router.asPath,
            behavior: router.query?.behavior
          }
        })
      )
    }
  }

  const onBookingTreeId = (params: any) => {
    const { id, slug: featureSlug = 'goi-kham', treeId = 'DATE' } = params
    router.push(
      PageRoutesV2.booking({
        featureSlug,
        query: {
          feature: `booking.${treeId.toLowerCase()}`,
          subjectId: id,
          partnerId: partnerInfo?.partnerId
        }
      })
    )
  }

  const onWarning = (item: any) => {
    setWarningMessage(item?.feature?.message)
  }

  const onFinish = async (item: any) => {
    if (item?.feature?.type.includes('booking.') && isMobile) {
      client.setPartner(partnerInfo?.partnerId)
      await dispatch(hospitalActions.setPartnerId(partnerInfo?.partnerId))
      await dispatch(bookingActions.resetBookingFlow({}))
      await dispatch(
        bookingActions.setUrlParams({
          treeId: item?.feature?.type.split('.')[1].toUpperCase(),
          partnerId: partnerInfo?.partnerId
        })
      )
      router.push({
        pathname: '/chon-lich-kham',
        query: {
          partnerId: partnerInfo?.partnerId,
          feature: item?.feature?.type,
          treeId: item?.feature?.type.split('.')[1].toUpperCase(),
          behavior: router.query?.behavior
        }
      })
    } else {
      dispatch(selectFeature(item?.feature))
      dispatch(bookingActions.resetBookingFlow({}))
      const feature: any = {
        partnerId: partnerInfo?.partnerId,
        type: item?.feature?.type,
        slug: item?.feature?.slug || 'dat-kham-co-so'
      }

      const { pathname, query } = RedirectHandler.redirectFeature({
        feature,
        extraConfig,
        slug: partnerSlug
      })

      router.push({
        pathname,
        query: {
          ...query,
          isDetail: true,
          feature: item?.feature?.type,
          behavior: router.query?.behavior
        }
      })
    }
  }

  const dataInfoHospital = {
    partnerInfo,
    hospitalDescription,
    downloadApp: handleUpdateLinkFromPartner(downloadApp, partnerInfo)
  }

  const checkSelect = (item: any) => {
    if (item.feature?.disabled) {
      onWarning({
        message: item.feature?.message,
        feature: item.feature,
        index: item.index
      })
    } else if (item.feature?.warningMessage) {
      setSelectedData(item)
    } else {
      onFinish({
        message: 'Thành công',
        feature: item.feature,
        index: item.index
      })
    }
  }
  const filterFeature = (features: any[]) => {
    return features.filter((item) => item?.status)
  }

  const renderHospitalLayout = (layout: string) => {
    switch (layout) {
      case 'LISTING':
        return (
          <MPDetailHospitalListing
            data={hospitalDescription}
            features={filterFeature(hospitalDescription?.features)}
            onBooking={onBooking}
            onWarning={onWarning}
            onFinish={onFinish}
            handleClickFeature={checkSelect}
            downloadApp={handleUpdateLinkFromPartner(downloadApp, partnerInfo)}
            windowWidth={windowWidth}
            partnerSlug={partnerSlug}
            openFeature={openFeature}
            featuresDrawerVisible={featuresDrawerVisible}
            setFeaturesDrawerVisible={setFeaturesDrawerVisible}
          />
        )
      default:
        return (
          <MPDetailHospital
            data={dataInfoHospital}
            onBooking={onBooking}
            onWarning={onWarning}
            onFinish={onFinish}
            features={filterFeature(props.features)}
            partnerSlug={partnerSlug}
            openFeature={openFeature}
            featuresDrawerVisible={featuresDrawerVisible}
            setFeaturesDrawerVisible={setFeaturesDrawerVisible}
          />
        )
    }
  }

  return (
    <React.Fragment>
      <MPContainer
        {...(typeLayoutHospital
          ? { layout: true }
          : {
              partner: true
            })}
        partner
      >
        {renderHospitalLayout(hospitalDescription?.layout)}
        <AlertModal
          style={{ top: '30%' }}
          open={size(warningMessage) > 0}
          onCancel={() => setWarningMessage('')}
        >
          {warningMessage}
        </AlertModal>
      </MPContainer>
      <div className={styles['booking']}>
        <MPButton onClick={() => onBooking(partnerInfo)}>
          Đặt khám ngay
        </MPButton>
      </div>
    </React.Fragment>
  )
}

BenhVienPage.getLayout = newDefaultLayout
BenhVienPage.ssr = true
BenhVienPage.showBreadcrumb = false
export default BenhVienPage

export async function getStaticPaths(context: GetStaticPathsContext) {
  return {
    paths: [],
    fallback: 'blocking'
  }
}

export async function getStaticProps(context: GetServerSidePropsContext) {
  try {
    const slug = context.params.partner_slug as string
    if (!slug) {
      return {
        redirect: {
          permanent: true,
          destination: '/404'
        },
        revalidate: SSG_REVALIDATE_SECOND
      }
    }

    let partnerInfo: any
    let partnerId: string

    try {
      partnerInfo = await fetchPartnerInfoBySlugServer(slug)
      partnerId = partnerInfo.partnerId
    } catch (err) {
      return {
        redirect: {
          permanent: true,
          destination: '/404'
        },
        revalidate: SSG_REVALIDATE_SECOND
      }
    }
    const [hospitalDescription, features]: any = await Promise.all([
      fetchPartnerInfoBySlugV2Server(slug, { partnerid: partnerId }),
      fetchFeatureByPartner({
        appid: partnerId,
        partnerid: partnerId,
        version: '1'
      })
    ])

    const detailSEO = {
      ...hospitalDescription?.seo,
      ogImage: hospitalDescription?.seo?.ogImage
        ? hospitalDescription?.seo?.ogImage
        : partnerInfo?.circleLogo,
      detailType: 'hospital',
      hospitalName: partnerInfo.name
    }

    return {
      props: {
        hospitalDescription,
        features: features
          ?.filter((f) => f.status)
          ?.sort((a, b) => a?.priority - b?.priority),
        partnerInfo,
        detailSEO,
        backgroundColor:
          hospitalDescription?.layout === 'LISTING' ? 'white' : '',
        CSYT_DYNAMIC: hospitalDescription?.layout === 'LISTING'
      },
      revalidate: SSG_REVALIDATE_SECOND
    }
  } catch (err) {
    return {
      props: {
        error: getError(err)
      },
      revalidate: SSG_REVALIDATE_SECOND
    }
  }
}
