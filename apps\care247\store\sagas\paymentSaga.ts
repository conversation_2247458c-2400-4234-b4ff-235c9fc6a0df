import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { AxiosResponse } from 'axios'
import client from '../../config/medproSdk'
import { paymentActions } from '../slices/paymentSlice'

// API function wrapper
function* callGetPaymentInfo(transactionId: string, partnerId: string) {
  return yield call(() =>
    client.booking.getPayment(
      { transactionId },
      {
        partnerid: partnerId
      }
    )
  )
}

// API function for translate
function* callGetTranslate(language: string) {
  return yield call(() => {
    const apiUrl = process.env.NEXT_PUBLIC_RESTFULL_API_URL || 'https://api-hotfix.medpro.com.vn'
    return fetch(`${apiUrl}/resource/get-translate?language=${language}`)
      .then(response => response.json())
  })
}

// Saga Workers
function* getPaymentInfoSaga(action: any) {
  try {
    const { transactionId, partnerId } = action.payload

    // Call real API
    const response: AxiosResponse = yield call(callGetPaymentInfo, transactionId, partnerId)

    const { data } = response
    yield put(paymentActions.getPaymentInfoSuccess(data))

  } catch (error: any) {
    const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi lấy thông tin thanh toán'
    yield put(paymentActions.getPaymentInfoFailure(errorMessage))
  }
}

function* getTranslateSaga(action: any) {
  try {
    console.log('🌐 Translate saga called with:', action.payload)
    const { language } = action.payload

    // Call translate API
    const data = yield call(callGetTranslate, language)
    console.log('✅ Translate API response:', data)

    // Dispatch success action
    yield put(paymentActions.getTranslateSuccess(data))

  } catch (error: any) {
    console.error('❌ Translate saga error:', error)
    const errorMessage = error?.message || 'Có lỗi xảy ra khi tải ngôn ngữ'
    yield put(paymentActions.getTranslateFailure(errorMessage))
  }
}

function* watcherGetPaymentInfo() {
  yield takeLatest(paymentActions.getPaymentInfo, getPaymentInfoSaga)
}

function* watcherGetTranslate() {
  yield takeLatest(paymentActions.getTranslate, getTranslateSaga)
}

// Root Saga
const paymentSaga = function* root() {
  yield all([
    fork(watcherGetPaymentInfo),
    fork(watcherGetTranslate)
  ])
}

export default paymentSaga
