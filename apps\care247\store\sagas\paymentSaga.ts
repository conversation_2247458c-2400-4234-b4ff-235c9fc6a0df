import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { AxiosResponse } from 'axios'
import client from '../../config/medproSdk'
import { paymentActions } from '../slices/paymentSlice'

// API function wrapper
function* callGetPaymentInfo(transactionId: string, partnerId: string) {
  return yield call(() =>
    client.booking.getPayment(
      { transactionId },
      {
        partnerid: partnerId,
        appid: "care247"
      }
    )
  )
}

// Saga Workers
function* getPaymentInfoSaga(action: any) {
  try {
    console.log('🚀 Saga called with:', action.payload)
    const { transactionId, partnerId } = action.payload

    console.log('📞 Calling API with:', { transactionId, partnerId })

    // Call real API
    const response: AxiosResponse = yield call(callGetPaymentInfo, transactionId, partnerId)

    const { data } = response
    console.log('✅ API response:', data)

    // Dispatch success action
    yield put(paymentActions.getPaymentInfoSuccess(data))
    console.log('✅ Success action dispatched')

  } catch (error: any) {
    console.error('❌ Saga error:', error)
    const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi lấy thông tin thanh toán'

    // Dispatch failure action
    yield put(paymentActions.getPaymentInfoFailure(errorMessage))
  }
}

function* watcherGetPaymentInfo() {
  yield takeLatest(paymentActions.getPaymentInfo, getPaymentInfoSaga)
}

// Root Saga
const paymentSaga = function* root() {
  yield all([
    fork(watcherGetPaymentInfo)
  ])
}

export default paymentSaga
