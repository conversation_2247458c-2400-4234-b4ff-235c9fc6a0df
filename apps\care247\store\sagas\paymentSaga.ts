import { all, call, fork, put, takeLatest } from 'redux-saga/effects'

// Mock client for now - replace with actual client later
const mockClient = {
  getPayment: async (query: any, headers: any) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    return {
      data: {
        payment: {
          transactionId: query.transactionId,
          amount: 122000,
          customerName: 'Vũ Thị Kim <PERSON>ân',
          service: 'Trẻ sơ sinh, ghế nôi',
          bankAccount: '9646474000000891799',
          bankName: 'MASTERBANK - Ngân hàng TMCP Việt Nam',
          transferCode: '*****************',
          status: 'pending',
          createdAt: new Date().toISOString()
        }
      }
    }
  }
}

import { paymentActions } from '../slices/paymentSlice'

// Saga Workers
function* getPaymentInfoSaga(action: any) {
  try {
    const { transactionId, partnerId } = action.payload

    // Call API
    const response = yield call(mockClient.getPayment,
      { transactionId },
      {
        partnerid: partnerId,
        appId: "medpro"
      }
    )

    const { data: { payment } } = response

    // Dispatch success action
    yield put(paymentActions.getPaymentInfoSuccess(payment))

  } catch (error: any) {
    const errorMessage = error?.message || 'Có lỗi xảy ra khi lấy thông tin thanh toán'

    // Dispatch failure action
    yield put(paymentActions.getPaymentInfoFailure(errorMessage))
  }
}

function* watcherGetPaymentInfo() {
  yield takeLatest(paymentActions.getPaymentInfo, getPaymentInfoSaga)
}

// Root Saga
const paymentSaga = function* root() {
  yield all([
    fork(watcherGetPaymentInfo)
  ])
}

export default paymentSaga
