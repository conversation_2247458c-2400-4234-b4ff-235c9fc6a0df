import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { AxiosResponse } from 'axios'
import client from '../../config/medproSdk'
import { currentEnv } from '../../config/envs'
import { paymentActions } from '../slices/paymentSlice'

// API function wrapper
function* callGetPaymentInfo(transactionId: string, partnerId: string) {
  return yield call(() =>
    client.booking.getPayment(
      { transactionId },
      {
        partnerid: partnerId
      }
    )
  )
}

// API function for translate
function* callGetTranslate(language: string) {
  return yield call(() => {
    const apiUrl = currentEnv.RESTFULL_API_URL
    console.log('🌐 Calling translate API:', `${apiUrl}/resource/get-translate?language=${language}`)
    return fetch(`${apiUrl}/resource/get-translate?language=${language}`)
      .then(response => response.json())
  })
}

// Saga Workers
function* getPaymentInfoSaga(action: any) {
  try {
    console.log('🚀 Payment saga called with:', action.payload)
    console.log('🔧 Using API environment:', currentEnv.API_BE)
    const { transactionId, partnerId } = action.payload

    // Call real API
    const response: AxiosResponse = yield call(callGetPaymentInfo, transactionId, partnerId)

    const { data } = response
    console.log('✅ Payment API response:', data)
    yield put(paymentActions.getPaymentInfoSuccess(data))

  } catch (error: any) {
    console.error('❌ Payment saga error:', error)
    const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi lấy thông tin thanh toán'
    yield put(paymentActions.getPaymentInfoFailure(errorMessage))
  }
}

function* getTranslateSaga(action: any) {
  try {
    console.log('🌐 Translate saga called with:', action.payload)
    console.log('🔧 Using translate API environment:', currentEnv.RESTFULL_API_URL)
    const { language } = action.payload

    // Call translate API
    const data = yield call(callGetTranslate, language)
    console.log('✅ Translate API response:', data)

    // Dispatch success action
    yield put(paymentActions.getTranslateSuccess(data))

  } catch (error: any) {
    console.error('❌ Translate saga error:', error)
    const errorMessage = error?.message || 'Có lỗi xảy ra khi tải ngôn ngữ'
    yield put(paymentActions.getTranslateFailure(errorMessage))
  }
}

function* watcherGetPaymentInfo() {
  yield takeLatest(paymentActions.getPaymentInfo, getPaymentInfoSaga)
}

function* watcherGetTranslate() {
  yield takeLatest(paymentActions.getTranslate, getTranslateSaga)
}

// Root Saga
const paymentSaga = function* root() {
  yield all([
    fork(watcherGetPaymentInfo),
    fork(watcherGetTranslate)
  ])
}

export default paymentSaga
