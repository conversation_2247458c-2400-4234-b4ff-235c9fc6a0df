import { all, call, fork, put, takeLatest } from 'redux-saga/effects'

import { paymentActions } from '../slices/paymentSlice'

// Saga Workers
function* getPaymentInfoSaga(action: any) {
  try {
    const { transactionId, partnerId } = action.payload

    // Dispatch success action
    yield put(paymentActions.getPaymentInfoSuccess(action.payload))
    console.log('✅ Success action dispatched')

  } catch (error: any) {
    console.error('❌ Saga error:', error)
    const errorMessage = error?.message || 'Có lỗi xảy ra khi lấy thông tin thanh toán'

    // Dispatch failure action
    yield put(paymentActions.getPaymentInfoFailure(errorMessage))
  }
}

function* watcherGetPaymentInfo() {
  yield takeLatest(paymentActions.getPaymentInfo, getPaymentInfoSaga)
}

// Root Saga
const paymentSaga = function* root() {
  yield all([
    fork(watcherGetPaymentInfo)
  ])
}

export default paymentSaga
