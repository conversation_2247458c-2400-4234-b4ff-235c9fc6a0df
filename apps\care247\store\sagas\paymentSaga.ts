import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { AxiosResponse } from 'axios'
import client from '../../config/medproSdk'
import { paymentActions } from '../slices/paymentSlice'
import { getError, showErrorNotification } from '../../utils/utils.error'

// API Call Function
export const getPaymentInfo = async (params: {
  transactionId: string
  partnerId: string
}) => {
  const { transactionId, partnerId } = params

  try {
    const res: AxiosResponse = await client.getPayment(
      { transactionId },
      {
        partnerid: partnerId,
        appId: "medpro"
      }
    )

    const {
      data: { payment }
    } = res

    return payment
  } catch (error) {
    throw error
  }
}

// Saga Workers
function* getPaymentInfoSaga(action: any) {
  try {
    const { transactionId, partnerId } = action.payload

    // Call API
    const payment = yield call(getPaymentInfo, {
      transactionId,
      partnerId
    })

    // Dispatch success action
    yield put(paymentActions.getPaymentInfoSuccess(payment))

  } catch (error: any) {
    const errorMessage = getError(error)

    // Dispatch failure action
    yield put(paymentActions.getPaymentInfoFailure(errorMessage))

    // Show error notification
    showErrorNotification(error)
  }
}

function* watcherGetPaymentInfo() {
  yield takeLatest(paymentActions.getPaymentInfoRequest, getPaymentInfoSaga)
}

// Root Saga
const paymentSaga = function* root() {
  yield all([
    fork(watcherGetPaymentInfo)
  ])
}

export default paymentSaga
