import { call, put, takeEvery } from 'redux-saga/effects'
import { PayloadAction } from '@reduxjs/toolkit'
import { client } from '../../services/apiClient'

// Action Types
export const GET_PAYMENT_INFO_REQUEST = 'payment/getPaymentInfoRequest'
export const GET_PAYMENT_INFO_SUCCESS = 'payment/getPaymentInfoSuccess'
export const GET_PAYMENT_INFO_FAILURE = 'payment/getPaymentInfoFailure'

// Action Interfaces
interface GetPaymentInfoRequestPayload {
  transactionId: string
  partnerId: string
  onSuccess?: (payment: any) => void
  onError?: (error: string) => void
}

interface GetPaymentInfoRequestAction extends PayloadAction<GetPaymentInfoRequestPayload> {
  type: typeof GET_PAYMENT_INFO_REQUEST
}

// Action Creators
export const getPaymentInfoRequest = (payload: GetPaymentInfoRequestPayload) => ({
  type: GET_PAYMENT_INFO_REQUEST,
  payload
})

export const getPaymentInfoSuccess = (payment: any) => ({
  type: GET_PAYMENT_INFO_SUCCESS,
  payload: payment
})

export const getPaymentInfoFailure = (error: string) => ({
  type: GET_PAYMENT_INFO_FAILURE,
  payload: error
})

// API Call Function
export const getPaymentInfo = async (params: {
  transactionId: string
  partnerId: string
}) => {
  const { transactionId, partnerId } = params
  
  try {
    const res = await client.getPayment(
      { transactionId },
      {
        partnerid: partnerId,
        appId: "medpro"
      }
    )
    
    const {
      data: { payment }
    } = res
    
    return payment
  } catch (error) {
    throw error
  }
}

// Saga Workers
function* getPaymentInfoSaga(action: GetPaymentInfoRequestAction) {
  try {
    const { transactionId, partnerId, onSuccess, onError } = action.payload
    
    // Call API
    const payment = yield call(getPaymentInfo, {
      transactionId,
      partnerId
    })
    
    // Dispatch success action
    yield put(getPaymentInfoSuccess(payment))
    
    // Call success callback if provided
    if (onSuccess) {
      onSuccess(payment)
    }
    
  } catch (error: any) {
    const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi lấy thông tin thanh toán'
    
    // Dispatch failure action
    yield put(getPaymentInfoFailure(errorMessage))
    
    // Call error callback if provided
    if (action.payload.onError) {
      action.payload.onError(errorMessage)
    }
  }
}

// Root Saga
export function* paymentSaga() {
  yield takeEvery(GET_PAYMENT_INFO_REQUEST, getPaymentInfoSaga)
}

export default paymentSaga
