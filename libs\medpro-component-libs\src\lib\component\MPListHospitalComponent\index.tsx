import { Pagination } from 'antd'
import { useScroll } from 'framer-motion'
import { debounce, first, size } from 'lodash'
import { useRouter } from 'next/router'
import React, { ReactNode, useCallback, useEffect, useState } from 'react'
import { PageRoutesV2 } from '../../common/PageRoutesV2'
import { LIST_TYPE_PARTNER } from '../../common/constant'
import { getReplaceUTF8, useIsMount, useWindowResize } from '../../common/func'

interface Props {
  hospitals: any
  onSelectHospital: (hospital: any, setHospitalDescription: any) => void
  onBooking: (item: any, setMessageHospital: any) => void
  provinces: any[]
  locationData: any
  renderItem: ({
    dataProvinces,
    data,
    type,
    dataSelected,
    searching,
    title,
    messageHospital,
    setMessageHospital,
    countSearch,
    isMobile,
    loading,
    setLoading,
    stickyScroll,
    handleSelectHospital,
    handleViewMore,
    renderPagination,
    handleFilterCity,
    handleTypeSelect
  }: ItemRender) => React.ReactNode | JSX.Element
}

export interface ItemRender {
  dataProvinces: any[]
  data: any[]
  type: any
  dataSelected: any
  searching: any
  title: any
  messageHospital: any
  setMessageHospital: any
  countSearch: any
  setCount: any
  count: any
  loading: boolean
  setLoading: any
  filteredList: any[]
  isMobile: boolean
  stickyScroll: boolean
  handleSelectHospital: (hospital: any) => void
  handleBooking: (item: any) => void
  handleViewMore: (item: any) => any
  renderPagination: () => ReactNode
  handleFilterCity: (v2: any) => void
  handleSearchDebounce: (event: any) => Promise<void>
  handleTypeSelect: (item: any) => void
}

const LIST_TYPE_PARTNER_KEY = LIST_TYPE_PARTNER.map((e) => e.key)
const TYPE_PARTNER: any = LIST_TYPE_PARTNER.reduce((r: any, e) => {
  r[e.key] = e.newHospitalType
  return r
}, {})

let timeout: any
const PAGE_SIZE = 10
const MPListHospitalSearchComponent = ({
  hospitals,
  provinces,
  onSelectHospital,
  onBooking,
  locationData,
  renderItem
}: Props) => {
  const router = useRouter()
  const isMount = useIsMount()
  const type = router.query.type as string
  const behavior = router.query.behavior as string
  const [filteredList, setFilterList] = useState(
    type
      ? hospitals.filter((item: any) =>
          item.newHospitalTypes?.includes(TYPE_PARTNER[type])
        )
      : hospitals
  )
  const [dataProvinces, setDataProvinces] = useState(provinces)
  const [keySearch, setKeySearch] = useState<string>('')
  const [cityId, setCityId] = useState<string>('')
  const [pageIndex, setPageIndex] = useState(1)
  const [data, setData] = useState(
    type
      ? hospitals
          .filter((item: any) =>
            item.newHospitalTypes?.includes(TYPE_PARTNER[type])
          )
          ?.slice(0, PAGE_SIZE)
      : hospitals?.slice(0, PAGE_SIZE)
  )
  const [searching, setSearching] = useState(false)
  const [loading, setLoading] = useState<boolean>(false)
  const [selectedHospital, setSelectedHospital] = useState<any>(
    first(
      LIST_TYPE_PARTNER_KEY.includes(type)
        ? hospitals.filter((h: any) =>
            h.newHospitalTypes?.includes(TYPE_PARTNER[type])
          )
        : hospitals
    )
  )
  const [count, setCount] = useState(PAGE_SIZE)
  const [hospitalDescription, setHospitalDescription] = useState<any>()
  const [messageHospital, setMessageHospital] = useState('')
  const [countSearch, setCountSearch] = useState({})
  const isMobile = useWindowResize(992)
  const [title, setTitle] = useState<any>(
    LIST_TYPE_PARTNER.filter((e) => e.key === type)[0]
  )
  const { scrollY } = useScroll()
  const [stickyScroll, setStickyScroll] = useState(false)
  const update = () => {
    if (scrollY.get() > scrollY.getPrevious()) {
      setStickyScroll(true)
    } else if (scrollY.get() < scrollY.getPrevious()) {
      setStickyScroll(false)
    }
  }

  useEffect(() => {
    return scrollY.onChange(() => update())
  })
  useEffect(() => {
    const filteredProvinces = provinces?.filter((province: any) =>
      hospitals.some((item: any) => item.city_id === province.id)
    )
    setDataProvinces(filteredProvinces)
  }, [provinces, hospitals])
  useEffect(() => {
    // setLoading(true)
    if (isMount) {
      renderPageData()
    } else {
      size(filteredList) > PAGE_SIZE && setLoading(true)
      setTimeout(() => {
        void renderPageData()
      }, 500)
    }
  }, [pageIndex, count, filteredList])

  useEffect(() => {
    setTitle(LIST_TYPE_PARTNER.filter((e) => e.key === type)[0])
    if (LIST_TYPE_PARTNER_KEY.includes(type)) {
      setSelectedHospital(
        first(
          hospitals.filter((h: any) =>
            h.newHospitalTypes?.includes(TYPE_PARTNER[type])
          )
        )
      )
    }
  }, [type, hospitals])

  useEffect(() => {
    const mappedListNewHospitalType = TYPE_PARTNER[type]
    const newList = hospitals.filter((item: any) => {
      if (keySearch) {
        const regex = new RegExp(getReplaceUTF8(keySearch), 'i')
        if (!getReplaceUTF8(item.name).match(regex)) {
          return false
        }
      }
      if (size(cityId) > 0 && !cityId.includes(item.city_id)) {
        return false
      }

      return true
    })
    keySearch || cityId ? countKeySearch(newList) : countKeySearch(hospitals)
    const dataType = type
      ? newList.filter((item: any) =>
          item.newHospitalTypes?.includes(mappedListNewHospitalType)
        )
      : newList
    setFilterList(dataType)
    window.innerWidth <= 991
      ? setSelectedHospital({})
      : setSelectedHospital(dataType[0])
    setPageIndex(1)
  }, [hospitals, cityId, type])

  useEffect(() => {
    onSelectHospital(selectedHospital, setHospitalDescription)
  }, [selectedHospital])

  const handleSelectHospital = (hospital: any) => {
    setSelectedHospital(hospital)
    onSelectHospital(hospital, setHospitalDescription)
  }

  const handlePageChange = (page: number) => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
    setPageIndex(page)
    setSelectedHospital(filteredList[(page - 1) * PAGE_SIZE])
  }

  const handleViewMore = (item: any) => {
    const { pathname } = PageRoutesV2.homePagePartner({
      slug: item.slug
    })
    router.push({
      pathname: pathname,
      query: {
        behavior: router.query?.behavior
      }
    })
  }

  const renderPagination = () => {
    return (
      <Pagination
        pageSize={PAGE_SIZE}
        current={pageIndex}
        onChange={handlePageChange}
        total={size(filteredList)}
        showSizeChanger={false}
      />
    )
  }
  const onFilterCity = (v2: any) => {
    const { province } = v2
    setCityId(province)
    setPageIndex(1)
  }
  const onSearchDebounce = async (event: any) => {
    clearTimeout(timeout)
    const kw = event.target.value.normalize('NFC')
    console.log(kw)
    setKeySearch(kw)
    setSearching(true)
    timeout = setTimeout(() => {
      setPageIndex(1)
      setCount(PAGE_SIZE)
      void onSearch(kw, cityId, type)
    }, 500)
  }

  const onSearch = useCallback(
    debounce(async (kw, cityId, type) => {
      try {
        const mappedListNewHospitalType = TYPE_PARTNER[type]
        let newList = hospitals.filter((item: any) => {
          if (kw) {
            const regex = new RegExp(getReplaceUTF8(kw), 'i')
            if (!getReplaceUTF8(item.name).match(regex)) {
              return false
            }
          }

          return !(size(cityId) > 0 && !cityId.includes(item.city_id))
        })
        if (locationData) {
          newList = newList.sort(
            (a: any, b: any) => parseFloat(a.distance) - parseFloat(b?.distance)
          )
        }
        kw || cityId ? countKeySearch(newList) : countKeySearch(hospitals)

        const dataType = newList.filter((item: any) =>
          mappedListNewHospitalType
            ? item.newHospitalTypes?.includes(mappedListNewHospitalType)
            : item
        )
        setFilterList(dataType)
        setSelectedHospital(window.innerWidth <= 991 ? [] : dataType[0])
        setPageIndex(1)
      } catch (err) {
        setFilterList([])
        // showError(err)
      } finally {
        clearTimeout(timeout)
        setSearching(false)
      }
    }),
    [setFilterList, setSearching, hospitals]
  )

  const onTypeSelect = (itemKey: any) => {
    setSearching(true)
    if (type === itemKey) {
      router.replace(
        {
          pathname: `/co-so-y-te`,
          query: {
            ...(behavior && { behavior })
          }
        },
        undefined,
        {
          shallow: true
        }
      )
    } else {
      router.replace(
        {
          pathname: `/co-so-y-te/${itemKey}`,
          query: {
            ...(behavior && { behavior })
          }
        },
        undefined,
        {
          shallow: false
        }
      )
    }
  }
  const handleBooking = (item: any) => {
    onBooking(item, setHospitalDescription)
  }

  const countKeySearch = (newList: any[]) => {
    const typeCounts: any = {}
    newList.forEach((hospital) => {
      hospital.newHospitalTypes.forEach((typeNumber: number) => {
        const typeHospital = LIST_TYPE_PARTNER_KEY[typeNumber - 1]
        if (typeHospital in typeCounts) {
          typeCounts[typeHospital]++
        } else {
          typeCounts[typeHospital] = 1
        }
      })
    })
    setCountSearch(typeCounts)
  }
  const renderPageData = useCallback(
    debounce(async () => {
      try {
        if (typeof window !== 'undefined' && window.innerWidth > 576) {
          setData(
            filteredList?.slice(
              (pageIndex - 1) * PAGE_SIZE,
              pageIndex * PAGE_SIZE
            )
          )
        } else {
          setData(filteredList?.slice(0, count))
        }
      } catch (err) {
        setData([])
        // showError(err)
      } finally {
        setLoading(false)
        setSearching(false)
      }
    }),
    [setData, setLoading, count, pageIndex, filteredList]
  )
  return (
    <>
      {renderItem({
        dataProvinces,
        data,
        type: type,
        dataSelected: { ...(selectedHospital as any), ...hospitalDescription },
        searching,
        title,
        messageHospital,
        setMessageHospital,
        countSearch,
        isMobile,
        stickyScroll,
        setCount,
        count,
        loading,
        setLoading,
        filteredList,
        handleViewMore,
        handleBooking,
        handleSelectHospital,
        renderPagination,
        handleFilterCity: onFilterCity,
        handleSearchDebounce: onSearchDebounce,
        handleTypeSelect: onTypeSelect
      })}
    </>
  )
}

export default MPListHospitalSearchComponent
