.leftPanel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .title {
    font-family: 'Roboto', sans-serif;
    font-weight: 700;
    font-size: 36px;
    line-height: 100%;
    letter-spacing: 0%;
    color: #333;
    margin-bottom: 24px;
  }

  .infoCard {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    flex: 1;
    display: flex;
    flex-direction: column;

    .infoText {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 24px;

      .borderLine {
        width: 1.5px;
        height: 38px;
        background: #52c41a;
        flex-shrink: 0;
        margin-top: 2px;
      }

      span {
        font-family: 'Roboto', sans-serif;
        font-weight: 400;
        font-size: 16px;
        line-height: 100%;
        letter-spacing: 0%;
        color: #666;
        padding-left: 12px;
        padding-right: 12px;
      }
    }

    .patientImage {
      margin-bottom: 24px;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .imageElement {
        width: 100% !important;
        max-width: 400px;
        height: auto !important;
        object-fit: contain;
        border-radius: 8px;
      }
    }

    .bottomInfo {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-top: auto;

      .qualityInfo,
      .professionalInfo {
        display: flex;
        align-items: center;
        gap: 12px;
        background: #f8f9fa;
        padding: 12px 16px;
        border-radius: 8px;

        .qualityIcon,
        .professionalIcon {
          font-size: 24px;
          flex-shrink: 0;
        }

        .qualityText,
        .professionalText {
          display: flex;
          flex-direction: column;
          font-size: 14px;
          color: #333;
          line-height: 1.3;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .leftPanel {
    .title {
      font-size: 28px;
    }

    .infoCard {
      padding: 16px;

      .infoText {
        .borderLine {
          height: 32px;
        }

        span {
          font-size: 14px;
          padding-left: 8px;
          padding-right: 8px;
        }
      }

      .patientImage .imageElement {
        max-width: 300px;
      }

      .bottomInfo {
        gap: 12px;

        .qualityInfo,
        .professionalInfo {
          padding: 10px 12px;

          .qualityIcon,
          .professionalIcon {
            font-size: 20px;
          }

          .qualityText,
          .professionalText {
            font-size: 13px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .leftPanel {
    .title {
      font-size: 24px;
      line-height: 110%;
    }

    .infoCard {
      padding: 12px;

      .infoText span {
        font-size: 13px;
      }

      .patientImage img {
        max-width: 250px;
      }
    }
  }
}
