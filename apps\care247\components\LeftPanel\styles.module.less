.leftPanel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .title {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin-bottom: 24px;
    line-height: 1.4;
  }

  .infoCard {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    flex: 1;
    display: flex;
    flex-direction: column;

    .infoHeader {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 24px;
      font-size: 14px;
      color: #666;
      line-height: 1.4;

      .infoIcon {
        font-size: 20px;
        margin-top: -2px;
        flex-shrink: 0;
      }
    }

    .patientImage {
      margin-bottom: 24px;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        max-width: 300px;
        height: auto;
        object-fit: contain;
        border-radius: 8px;
      }
    }

    .doctorInfo {
      display: flex;
      align-items: center;
      gap: 12px;
      background: #f0f8ff;
      padding: 16px;
      border-radius: 8px;
      margin-top: auto;

      .doctorAvatar {
        font-size: 32px;
        flex-shrink: 0;
      }

      .doctorText {
        display: flex;
        flex-direction: column;
        font-size: 14px;
        color: #333;
        line-height: 1.3;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .leftPanel {
    .title {
      font-size: 24px;
    }

    .infoCard {
      padding: 16px;

      .patientImage img {
        max-width: 250px;
      }

      .doctorInfo {
        padding: 12px;

        .doctorAvatar {
          font-size: 28px;
        }

        .doctorText {
          font-size: 13px;
        }
      }
    }
  }
}
