.leftPanel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .title {
    font-family: 'Roboto', sans-serif;
    font-weight: 700;
    font-size: 36px;
    line-height: 100%;
    letter-spacing: 0%;
    color: #333;
    margin-bottom: 24px;
  }
  .infoText {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 24px;

    .borderLine {
      width: 1.5px;
      height: 38px;
      background: #52c41a;
      flex-shrink: 0;
      margin-top: 2px;
    }

    span {
      font-family: 'Roboto', sans-serif;
      font-weight: 400;
      font-size: 16px;
      line-height: 100%;
      letter-spacing: 0%;
      color: #666;
      padding-left: 12px;
      padding-right: 12px;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .leftPanel {
    .title {
      font-size: 28px;
    }

    .infoCard {
      padding: 16px;

      .infoText {
        .borderLine {
          height: 32px;
        }

        span {
          font-size: 14px;
          padding-left: 8px;
          padding-right: 8px;
        }
      }

      .patientImage .imageElement {
        max-width: 300px;
      }

      .bottomInfo {
        gap: 12px;

        .qualityInfo,
        .professionalInfo {
          padding: 10px 12px;

          .qualityIcon,
          .professionalIcon {
            font-size: 20px;
          }

          .qualityText,
          .professionalText {
            font-size: 13px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .leftPanel {
    .title {
      font-size: 24px;
      line-height: 110%;
    }

    .infoCard {
      padding: 12px;

      .infoText span {
        font-size: 13px;
      }

      .patientImage img {
        max-width: 250px;
      }
    }
  }
}
