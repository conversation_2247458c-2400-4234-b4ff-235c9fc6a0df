import { useWindowResize } from '@medpro-libs/libs'
import AboutUs from './common/AboutUs'
import Banner from './common/Banner'
import Quote from './common/Quote'
import Highlight from './common/Highlight'
import Partner from './common/Partner'
import Media from './common/Media'
import { Statistical } from './common/Statistical'

const ClinicCooperation = ({
  isLandingPage,
  partners,
  windowWidth,
  onSubmit,
  loading
}: {
  isLandingPage?: boolean
  loading?: boolean
  partners?: any
  windowWidth?: any
  onSubmit: (data: any, form: any) => void
}) => {
  const isMobile = useWindowResize(576)
  const scrollToSection = (id: any) => {
    const section = document.getElementById(id)
    if (section) {
      const offset = isMobile ? 45 : 80
      const topPosition =
        section.getBoundingClientRect().top + window.scrollY - offset
      window.scrollTo({
        top: topPosition,
        behavior: 'smooth'
      })
    } else {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }
  return (
    <>
      <Banner scrollToSection={scrollToSection} />
      <div id={'loi-ich-khi-hop-tac-voi-medpro'}>
        <AboutUs windowWidth={windowWidth} />
      </div>
      {/*<div id={'giai-phap-tu-medpro'}>*/}
      {/*  <Solution scrollToSection={scrollToSection} windowWidth={windowWidth} />*/}
      {/*</div>*/}

      <div id={'quy-trinh-tu-medpro'}>
        <Highlight windowWidth={windowWidth} />
      </div>
      <Partner partners={partners} />
      <div id={'nhan-tu-van-bao-gia'}>
        <Quote
          onSubmit={onSubmit}
          loading={loading}
          windowWidth={windowWidth}
        />
      </div>
      <div>
        <Statistical />
      </div>
      <Media />
    </>
  )
}

export default ClinicCooperation
