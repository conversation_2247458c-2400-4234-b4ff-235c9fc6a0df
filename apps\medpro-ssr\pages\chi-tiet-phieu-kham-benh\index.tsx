/* eslint-disable @typescript-eslint/no-var-requires */
import {
  BOOKING_STATUS,
  getRoutePartnerIdQueryParams,
  ModalSharePayment,
  MPLoading,
  useWindowResize
} from '@medpro-libs/libs'
import { Tabs, Form } from 'antd'
import cx from 'classnames'
import {
  renderDurationNumber,
  useCountDownSeconds
} from 'libs/medpro-component-libs/src/lib/ui/MPBillCodeCard/common/Hooks/useCountDown'
import { first, get, isArray, size } from 'lodash'
import { GetServerSidePropsContext } from 'next'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useQuery } from 'react-query'
import { useDispatch } from 'react-redux'
import client from '../../config/medproSdk'
import EmptyList from '../../public/images/EmptyList.png'
import { handleRedirectPayment } from '../../src/components/pages/Booking/func'
import {
  bookingActions,
  resetPayment,
  setIsRepayment,
  setTreeId
} from '../../store/booking/slice'
import { useAppSelector } from '../../store/hooks'
import { hospitalActions } from '../../store/hospital/hospitalSlice'
import { selectExtraConfig } from '../../store/hospital/selector'
import { patientActions } from '../../store/patient/patientSlice'
import { userActions } from '../../store/user/userSlice'
import { setKeyCookie } from '../../utils/cookies'
import { PageRoutes } from '../../utils/PageRoutes'
import {
  getPatientBooking,
  isEmptyObject,
  onCopytoShare
} from '../../utils/utils.function'
import {
  openNotification,
  showError,
  showMessage
} from '../../utils/utils.notification'
import { itemsBooking } from './../../src/components/DetailExam/ExamPayment'
import {
  ModalCancelBooking,
  ModalNoticeImportant,
  ModalNoticePaymentCare247,
  ModalRequestLogin,
  ModalRequestUpdateInfoPatient,
  ModalShareBooking,
  ModalSuccessPaymentCare247
} from './../../src/components/DetailExam/Modals'
import styles from './styles.module.less'
import {
  CancelBookingModal,
  NotificationModal,
  ComplaintSuccessModal
} from '../../src/components/BookingModal/BookingModal'
import { IoIosCheckmarkCircleOutline } from 'react-icons/io'
declare const window: Window &
  typeof globalThis & {
    dataLayer: any
  }

const PaymentInfo = (props: BookingDetailProps) => {
  const { transactionId } = props
  const dispatch = useDispatch()
  const router = useRouter()
  const tab = router.query?.tab
  const userId = useAppSelector((s) => s.user.userInfo?.id)
  const userIsCs = useAppSelector((s) => s.user.userInfo?.isCS)
  const loading = useAppSelector((s) => s.booking?.paymentInfo?.loading)
  const token = useAppSelector((s) => s.user.accessToken)

  const [formShareBooking] = Form.useForm()
  const [formCancelBooking] = Form.useForm()
  const [showSharePaymentModal, setShowSharePaymentModal] = useState(false)
  const [currenTab, setCurrenTab] = useState<any>(tab)
  const [showModalComplaintSuccess, setShowModalComplaintSuccess] =
    useState(false)
  const [isCancelBooking, setIsCancelBooking] = useState(false)
  const [currentStepCancel, setCurrentStepCancel] = useState(0)
  const [isPaymentApotapay, setIsPaymentApotapay] = useState(true)
  const [cancelReasons, setCancelReasons] = useState([])
  const [selectedCancelReasons, setSelectedCancelReasons] = useState([])
  const [bankList, setBankList] = useState([])
  const [otherReason, setOtherReason] = useState('')
  const [isBtnNotification, setIsBtnNotification] = useState(true)
  const [openModalPaymentCare247, setOpenModalPaymentCare247] = useState(false)
  const [isCanceling, setIsCanceling] = useState(false)
  const isMobile = useWindowResize(576)
  const [openModal, setOpenModal] = useState(false)
  const [isImportantNotice, setImportantNotice] = useState(false)
  const [care247Available, setCare247Available] = useState<Care247>({})
  const [isShareBooking, setIsShareBooking] = useState(false)
  const [shareInfo, setShareInfo] = useState({})

  useEffect(() => {
    setCurrenTab(tab)
  }, [tab])

  useEffect(() => {
    dispatch(resetPayment())
    if (!token) {
      setOpenModal(true)
      return
    }
    dispatch(hospitalActions.getExtraConfig())
    refetchBookingInfo()
  }, [])

  const fetchMedproCareInfo = async () => {
    try {
      const { data } = await client.bookingTree.getMedproCareInfo({
        transactionId: transactionId
      })
      setCare247Available(data)
      setOpenModalPaymentCare247(
        data?.popupPayPending?.status && !!router.query?.mpTransaction
      )
      if (data?.popupPayPending?.status && !!router.query?.mpTransaction) {
        setImportantNotice(false)
      }
    } catch (error) {
      setCare247Available({})
    }
  }

  const getPaymentInfo = async () => {
    const { data } = await client.paymentMethod.getPaymentInfo({
      transactionId: transactionId
    })
    const paymentInfo = isArray(data) ? data : [data]

    return paymentInfo
  }

  const checkSyncBooking = async (bookingId) => {
    const { data } = await client.paymentMethod.checkSyncBooking({
      bookingId: bookingId
    })
    if (data.isOk) {
      refetchBookingInfo()
      setIsBtnNotification(false)
    }
    return data?.isOk
  }

  const {
    data,
    isFetching,
    isFetched,
    isError,
    refetch: refetchBookingInfo
  } = useQuery([transactionId], getPaymentInfo, {
    enabled: !!transactionId && !!token,
    onSuccess: (data) => {
      const bookingInfo = first(data)?.bookingInfo

      /* Message thanh toán không thành công */
      if (bookingInfo?.status === BOOKING_STATUS.CHUA_THANH_TOAN) {
        const paymentType = first(data).payment.chargeFeeInfo.name?.replace(
          'Thanh toán bằng',
          ''
        )
        showMessage(`Thanh toán bằng ${paymentType} không thành công`, 'error')
      }

      /* Thông báo 15p trước giờ khám */
      if (
        !!bookingInfo?.importantNotice &&
        !getPatientBooking(bookingInfo?.patient).isMissInfoPatient
      ) {
        setImportantNotice(true)
      }

      /* Chọn PTTT Thanh toán hộ */
      if (
        bookingInfo?.sharePaymentConfigUrl &&
        bookingInfo?.sharePayment &&
        bookingInfo?.status !== BOOKING_STATUS.DA_HUY
      ) {
        setShowSharePaymentModal(true)
      }

      fetchMedproCareInfo()

      if (bookingInfo?.cancelStep) {
        setIsPaymentApotapay(true)
      } else {
        setIsPaymentApotapay(false)
      }
      fetchBankList()
    }
  })

  const firstBooking = useMemo(() => {
    return first(data)
  }, [isFetched])

  const date = firstBooking?.bookingInfo?.waitaminute
  const waitingMessage = firstBooking?.bookingInfo?.waitaminute_message
  const { duration } = useCountDownSeconds(date, 1000)

  useEffect(() => {
    if (!!duration && duration > 0) {
      setIsBtnNotification(true)
    } else {
      firstBooking?.bookingInfo?.waitaminute && refetchBookingInfo()
      if (!waitingMessage) {
        setIsBtnNotification(false)
      }
    }
  }, [duration])

  const intervalRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    if (waitingMessage) {
      setIsBtnNotification(true)
    } else {
      setIsBtnNotification(false)
    }
    intervalRef.current = setInterval(() => {
      checkSyncBooking(firstBooking?.bookingInfo?.id)
    }, 10000)

    if (!firstBooking?.bookingInfo?.waitaminute) {
      clearInterval(intervalRef.current)
    }

    return () => clearInterval(intervalRef.current)
  }, [firstBooking])

  const refundNote = get(firstBooking, 'bookingInfo.refundNote', '')

  const onReserveMedproCare = async (values: ReserveMedproCare) => {
    try {
      const { data } = await client.booking.reserveMedproCare(values)
      handleRedirectPayment({ data, dispatch })
    } catch (err) {
      showError(err)
    }
  }

  const onReserveMedproCareAddon = async (values: ReserveMedproCareAddon) => {
    try {
      const { data } = await client.booking.reserveMedproCareAddon(values)
      handleRedirectPayment({ data, dispatch })
    } catch (err) {
      showError(err)
    }
  }

  const repayment = (e: any) => {
    dispatch(bookingActions.resetSchedule())
    dispatch(bookingActions.resetMultiSchedules())
    dispatch(bookingActions.resetBookingTreeState())
    dispatch(setIsRepayment(true))
    dispatch(setTreeId(first(data)?.bookingInfo?.treeId))
    dispatch(patientActions.setSelectedPatient(firstBooking.patientInfo))
    dispatch(bookingActions.getPaymentInfo({ transactionId }))

    setKeyCookie('partnerId', firstBooking?.bookingInfo?.partnerId)
    window.dataLayer.push({
      event: 'Thanh toán lại phiếu khám',
      Action: 'Click',
      Category: 'Button-Action',
      Event: 'Thanh toán lại phiếu khám',
      Label: e?.currentTarget?.textContent || 'Thanh toán lại',
      PartnerId: firstBooking.bookingInfo?.partnerId,
      UserId: userId
    })
    isMobile
      ? router.push({
          pathname: '/thanh-toan-lai-app',
          query: {
            partnerId: firstBooking?.bookingInfo?.partnerId
          }
        })
      : router.push(
          getRoutePartnerIdQueryParams(
            PageRoutes.bookingConfirm.path,
            firstBooking?.bookingInfo?.partnerId
          )
        )
  }

  const getShareBookingInfo = async (_id: string) => {
    try {
      const { data } = await client.booking.shareBooking({ bookingId: _id })
      setShareInfo(data?.link)
    } catch (error) {
      showError(error)
    }
  }

  const shareToPay = () => {
    setShowSharePaymentModal((preState) => !preState)
  }

  const toggleShareBooking = () => {
    setIsShareBooking((preState) => !preState)
  }

  const fetchCancelReasons = async () => {
    try {
      const { data } = await client.booking.getCancelReasons()
      setCancelReasons(data)
    } catch (error) {
      showError(error)
      return []
    }
  }

  const fetchBankList = async () => {
    try {
      const { data } = await client.booking.getBankList()
      setBankList(data)
    } catch (error) {
      showError(error)
    }
  }

  const toggleCancelModel = (e: any) => {
    setIsCancelBooking(!isCancelBooking)
    fetchCancelReasons()

    window.dataLayer.push({
      event: 'Hủy phiếu khám',
      Action: 'Click',
      Category: 'Button-Action',
      Event: 'Hủy phiếu khám',
      Label: e?.currentTarget?.textContent,
      PartnerId: firstBooking?.bookingInfo?.partnerId,
      UserId: userId
    })
  }

  const toggleLogin = () => {
    dispatch(userActions.login())
  }

  const handleNavigation = (route: string) => {
    router.push(route)
  }

  const handleUpdateInfoPatient = () => {
    router.push(
      isMobile
        ? {
            pathname: '/cap-nhat-thong-tin',
            query: {
              ...router.query,
              id: getPatientBooking(data?.[0].bookingInfo?.patient)?.patientId,
              bookingId: data?.[0].bookingInfo?.id,
              transactionId: transactionId,
              prevPage: router.asPath,
              partnerId: data?.[0].bookingInfo?.partnerId
            }
          }
        : {
            pathname: '/cap-nhat-thong-tin',
            query: {
              id: getPatientBooking(data?.[0].bookingInfo?.patient)?.patientId,
              partnerId: data?.[0].bookingInfo?.partnerId
            }
          }
    )
  }

  if (openModal) {
    /* Modal - Login để xem phiếu khám */
    return (
      openModal && (
        <ModalRequestLogin
          isOpen={openModal}
          setOpen={setOpenModal}
          handleNavigation={handleNavigation}
          toggleLogin={toggleLogin}
        />
      )
    )
  } else if (isError) {
    return (
      <div className={styles['emptyList']}>
        <p>Không tìm thấy thông tin phiếu khám</p>
        <span>
          <Image src={EmptyList} layout='fill' alt='Empty List' />
        </span>
      </div>
    )
  }
  const handleSkipPaymentCare247 = () => {
    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, tab: 1 }
      },
      undefined,
      { shallow: true }
    )
    setOpenModalPaymentCare247(false)
  }

  const onOpenModalPaymentCare247 = () => {
    setOpenModalPaymentCare247(true)
  }

  const handleChangeCancelReasons = (checkedValues) => {
    setSelectedCancelReasons(checkedValues)
  }

  const toggleCancelBooking = () => {
    setIsCancelBooking(!isCancelBooking)
    setCurrentStepCancel(0)
    setSelectedCancelReasons([])
    setOtherReason('')
  }

  const onSubmitCancelBooking = async (values: any) => {
    if (currentStepCancel === 0 && isPaymentApotapay) {
      setCurrentStepCancel(1)
    } else {
      formCancelBooking
        .validateFields()
        .then(async () => {
          setIsCanceling(true)
          const body: {
            _id: string
            reasonIds: string[]
            otherContent?: string
            accountName?: string
            accountNumber?: string
            bankName?: string
            bankCode?: string
          } = {
            _id: firstBooking?.bookingInfo?._id,
            reasonIds: selectedCancelReasons
          }

          if (selectedCancelReasons.includes('other')) {
            body.otherContent = otherReason
          }

          if (isPaymentApotapay) {
            body.accountName = values.accountName
            body.accountNumber = values.accountNumber
            body.bankName = values.bankName
            body.bankCode = values.bankCode
          }

          try {
            await client.booking.cancelBookingV2(body)
            setIsCancelBooking(false)
            openNotification('open', {
              placement: 'top',
              message: (
                <>
                  Hủy phiếu khám thành công{' '}
                  <IoIosCheckmarkCircleOutline size={20} fill='#FFFFFF' />
                </>
              ),
              duration: 5,
              className: 'custom-notification-success'
            })
            onCloseCancelBooking()
          } catch (error) {
            showError(error)
          } finally {
            setIsCanceling(false)
          }
        })
        .catch((errorInfo) => {
          console.log('errorInfo', errorInfo)
        })
    }
  }

  const onCloseCancelBooking = () => {
    setIsCancelBooking(false)
    formCancelBooking.resetFields()
    setCurrentStepCancel(0)
    setSelectedCancelReasons([])
    setOtherReason('')
  }

  return (
    <>
      {isFetching ? (
        <MPLoading loading={{ status: loading }} />
      ) : (
        <>
          <Tabs
            activeKey={currenTab ? 'Care247' : 'NormalBooking'}
            centered
            className={cx(
              styles['TabsBooking'],
              !isEmptyObject(care247Available) && styles['care247Available'],
              !isMobile && styles['tabMedproCareDesktop']
            )}
            onChange={(e) => setCurrenTab(e === 'NormalBooking' ? 0 : 1)}
            items={itemsBooking({
              data,
              isMobile,
              refundNote,
              router,
              toggleCancelModel,
              onReserveMedproCare,
              onReserveMedproCareAddon,
              repayment,
              shareToPay,
              transactionId,
              care247Available,
              isCs: userIsCs,
              onOpenModalPaymentCare247,
              getShareBookingInfo,
              toggleShareBooking
            })}
          />
          {/* Modal - Thanh toán hộ */}
          {showSharePaymentModal && (
            <ModalSharePayment
              id={firstBooking?.bookingInfo?.id}
              onOke={shareToPay}
              onToggle={shareToPay}
              showModal={showSharePaymentModal}
              partnerId={firstBooking?.bookingInfo?.partnerId}
              userId={userId}
            />
          )}
          {/* Modal & Drawer - Hủy phiếu */}
          {isCancelBooking && (
            <CancelBookingModal
              bookingInfo={firstBooking?.bookingInfo}
              paymentInfo={firstBooking?.payment}
              bankList={bankList}
              isCancelBooking={isCancelBooking}
              isCanceling={isCanceling}
              isPaymentApotapay={isPaymentApotapay}
              currentStepCancel={currentStepCancel}
              cancelReasons={cancelReasons}
              selectedCancelReasons={selectedCancelReasons}
              otherReason={otherReason}
              formCancelBooking={formCancelBooking}
              isMobile={isMobile}
              onClose={onCloseCancelBooking}
              onCancel={toggleCancelBooking}
              onSubmit={onSubmitCancelBooking}
              onStepChange={(step) => setCurrentStepCancel(step)}
              onReasonChange={handleChangeCancelReasons}
              onOtherReasonChange={(value) => setOtherReason(value)}
            />
          )}

          {/* Modal - Thông báo */}
          <NotificationModal
            isOpen={isBtnNotification}
            onClose={() => setIsBtnNotification(false)}
            waitingMessage={waitingMessage}
            duration={duration}
            renderDurationNumber={renderDurationNumber}
            onHomeClick={() => router.push('/')}
          />

          {/* Modal - Khiếu nại thành công */}
          <ComplaintSuccessModal
            isOpen={showModalComplaintSuccess}
            onClose={() => setShowModalComplaintSuccess(false)}
          />
          {/* Modal - Thanh toán DV care247 */}
          {openModalPaymentCare247 && (
            <ModalNoticePaymentCare247
              isOpen={openModalPaymentCare247}
              setOpen={setOpenModalPaymentCare247}
              handleSkipPaymentCare247={handleSkipPaymentCare247}
              onReserveMedproCare={onReserveMedproCare}
              data={care247Available?.popupPayPending}
              bookingInfo={data?.[0]?.bookingInfo}
            />
          )}
          {/* Modal - Thông báo tin quan trọng liên quan phiếu khám */}
          {isImportantNotice && (
            <ModalNoticeImportant
              isOpen={isImportantNotice}
              setOpen={setImportantNotice}
              data={firstBooking}
            />
          )}
          {/* Modal - Yêu cầu bổ sung thông tin hồ sơ để xem phiếu khám */}
          {getPatientBooking(firstBooking?.bookingInfo?.patient) && (
            <>
              {getPatientBooking(firstBooking?.bookingInfo?.patient)
                .isMissInfoPatient && (
                <div
                  className={styles['mobileMask']}
                  onClick={(e) => {
                    e.stopPropagation()
                  }}
                ></div>
              )}
              <ModalRequestUpdateInfoPatient
                isOpen={
                  !!getPatientBooking(firstBooking?.bookingInfo?.patient)
                    .isMissInfoPatient
                }
                data={getPatientBooking(data?.[0]?.bookingInfo?.patient)}
                handleUpdateInfoPatient={handleUpdateInfoPatient}
              />
            </>
          )}
          {/* Modal - Chia sẽ phiếu khám */}
          {isShareBooking && (
            <ModalShareBooking
              isOpen={isShareBooking}
              setOpen={toggleShareBooking}
              form={formShareBooking}
              onCopy={onCopytoShare}
              link={shareInfo}
            />
          )}
        </>
      )}
    </>
  )
}
PaymentInfo.breadcrumb = [{ title: 'Thông tin phiếu khám bệnh' }]
export default PaymentInfo
export async function getServerSideProps(context: GetServerSidePropsContext) {
  const transactionId =
    (context.query.mpTransaction as string) ||
    (context.query.transactionId as string)
  if (!transactionId) {
    return {
      redirect: {
        permanent: true,
        destination: '/404'
      }
    }
  }
  return {
    props: {
      transactionId
    }
  }
}

interface BookingDetailProps {
  transactionId: string
  tab?: number
}

interface Care247 {
  content?: string
  footer?: string
  header?: string
  status?: number
  popupPayPending?: any
  popupPaySuccess?: any
}

interface ReserveMedproCare {
  transactionId: string
  redirectUrl: string
  groupId?: string
  medproCareServiceIds?: string[]
}

interface ReserveMedproCareAddon {
  transactionId: string
  redirectUrl: string
  groupId?: number
  medproCareServiceId?: string
  addonId: string
}
