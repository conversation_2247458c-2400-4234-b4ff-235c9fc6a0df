import React, { useEffect } from 'react'
import { useRouter } from 'next/router'
import { Spin, Alert, Modal } from 'antd'
import usePayment from '../hooks/usePayment'

const PaymentExample: React.FC = () => {
  const router = useRouter()
  const { transactionId, partnerId } = router.query
  
  const {
    paymentInfo,
    loading,
    error,
    successModalOpen,
    getPaymentInfo,
    setSuccessModal,
    clearError,
    resetState
  } = usePayment()

  // Fetch payment info when component mounts
  useEffect(() => {
    if (transactionId && partnerId) {
      getPaymentInfo({
        transactionId: transactionId as string,
        partnerId: partnerId as string,
        onSuccess: (payment) => {
          console.log('Payment info loaded successfully:', payment)
          // You can show success modal or do other actions here
        },
        onError: (errorMessage) => {
          console.error('Failed to load payment info:', errorMessage)
          // Handle error (show notification, redirect, etc.)
        }
      })
    }
  }, [transactionId, partnerId, getPaymentInfo])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      resetState()
    }
  }, [resetState])

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <Alert
        message="Lỗi"
        description={error}
        type="error"
        showIcon
        closable
        onClose={clearError}
        style={{ margin: '20px' }}
      />
    )
  }

  return (
    <div>
      {paymentInfo && (
        <div>
          <h2>Thông tin thanh toán</h2>
          <p><strong>Mã giao dịch:</strong> {paymentInfo.transactionId}</p>
          <p><strong>Số tiền:</strong> {paymentInfo.amount?.toLocaleString()}₫</p>
          <p><strong>Khách hàng:</strong> {paymentInfo.customerName}</p>
          <p><strong>Dịch vụ:</strong> {paymentInfo.service}</p>
          <p><strong>Số tài khoản:</strong> {paymentInfo.bankAccount}</p>
          <p><strong>Ngân hàng:</strong> {paymentInfo.bankName}</p>
          <p><strong>Nội dung chuyển khoản:</strong> {paymentInfo.transferCode}</p>
          <p><strong>Trạng thái:</strong> {paymentInfo.status}</p>
        </div>
      )}

      <Modal
        title="Thanh toán thành công"
        open={successModalOpen}
        onOk={() => setSuccessModal(false)}
        onCancel={() => setSuccessModal(false)}
      >
        <p>Giao dịch đã được xử lý thành công!</p>
      </Modal>
    </div>
  )
}

export default PaymentExample
