import { all, call, fork, put, takeLatest } from 'redux-saga/effects'
import { AxiosResponse } from 'axios'
import client from './../../config/medproSdk'
import actionStore from '../actionStore'
import { patientActions } from '../patient/patientSlice'
import { fetchCountries, setCountries, totalDataActions } from './slice'
import { openNotification } from '../../utils/utils.notification'
import { getError, showErrorNotification } from '../../utils/utils.error'
import { getNewsHomePageServer } from '../../utils/utils.query-server'
import { delay } from '../../utils/utils.function'

function* unLinkPatientSaga(
  action: ReturnType<typeof actionStore.unlinkPatient>
) {
  try {
    const response: AxiosResponse = yield client.patient.unlinkPatient({
      id: action.payload.id
    })
    openNotification('success', {
      message: '<PERSON>ạn đã x<PERSON><PERSON> hồ sơ bệnh nhân thành công!'
    })
    yield put(patientActions.getPatientByUserId())
  } catch (err) {
    showErrorNotification(err)
  }
}

function* watcher_unLinkPatientSaga() {
  yield takeLatest(actionStore.unlinkPatient, unLinkPatientSaga)
}

function* getAllBookingOfUserSaga() {
  try {
    yield put(actionStore.setLoadingTrue())
    const response: AxiosResponse = yield client.booking.getAllBookingByUserId()
    const { data } = response
    yield put(actionStore.setAllBookingOfUser(data))

    yield call(delay, 150)
    yield put(actionStore.setLoadingFalse())
  } catch (e) {
    console.info('error getAllBookingOfUserSaga: ', e)
    yield put(actionStore.setLoadingFalse())
  }
}

function* watcher_getAllBookingOfUserSaga() {
  yield takeLatest(actionStore.getAllBookingOfUser, getAllBookingOfUserSaga)
}
// get All booking
function* getAllBookingSaga() {
  try {
    yield put(actionStore.setLoadingTrue())
    const response: AxiosResponse = yield client.booking.getAllBooking()
    const { data } = response
    yield put(actionStore.setAllBooking(data))

    yield call(delay, 150)
    yield put(actionStore.setLoadingFalse())
  } catch (e) {
    console.info('error getAllBookingSaga: ', e)
    yield put(actionStore.setLoadingFalse())
  }
}

function* watcher_getAllBookingSaga() {
  yield takeLatest(actionStore.getAllBooking, getAllBookingSaga)
}
function* getUnReadNotifSaga() {
  try {
    const response: AxiosResponse =
      yield client.notify.getAllUnReadNotifByUser()
    const { data } = response
    yield put(totalDataActions.setUnReadNoti(data.unReadCount))
  } catch (err) {
    console.info('error getUnReadNotif', err)
  }
}

function* watcher_getUnReadNotif() {
  yield takeLatest(totalDataActions.getUnReadNotif, getUnReadNotifSaga)
}

function* getNotiOfUserSaga() {
  try {
    yield put(actionStore.setLoadingTrue())
    const response: AxiosResponse = yield client.notify.getAllNotifByUser()
    const { data } = response
    yield put(actionStore.setNotiOfUser(data))
    yield put(actionStore.setLoadingFalse())
  } catch (e) {
    console.info('lỗi')
    yield put(actionStore.setLoadingFalse())
  }
}

function* watcher_getNotiOfUserSaga() {
  yield takeLatest(actionStore.getNotiOfUser, getNotiOfUserSaga)
}

function* getGroupNotiOfUserSaga() {
  try {
    yield put(actionStore.setLoadingTrue())
    const response: AxiosResponse = yield client.notify.getGroupNotifByUser()
    const { data } = response
    yield put(actionStore.setGroupNotiOfUser(data))
    yield put(actionStore.setLoadingFalse())
  } catch (e) {
    console.info('lỗi')
    yield put(actionStore.setLoadingFalse())
  }
}

function* watcher_getGroupNotiOfUserSaga() {
  yield takeLatest(actionStore.getGroupNotiOfUser, getGroupNotiOfUserSaga)
}

function* setMarkViewedNotiSaga(
  action: ReturnType<typeof actionStore.setMarkViewedNoti>
) {
  try {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    yield put(actionStore.setLoadingTrue())
    yield client.notify.markViewedNotif({
      id: action.payload.id
    })
    const res: AxiosResponse = yield client.notify.getGroupNotifByUser()
    const { data } = res
    yield put(actionStore.setGroupNotiOfUser(data))
    yield put(totalDataActions.getUnReadNotif())
    yield put(actionStore.setLoadingFalse())
  } catch (e) {
    console.info('lỗi')
    yield put(actionStore.setLoadingFalse())
  }
}

function* watcher_setMarkViewedNotiSaga() {
  yield takeLatest(actionStore.setMarkViewedNoti, setMarkViewedNotiSaga)
}

//read all noti
function* setMarkViewedAllNotiSaga(
  action: ReturnType<typeof actionStore.setMarkViewedAllNoti>
) {
  try {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    yield put(actionStore.setLoadingTrue())
    const response: AxiosResponse = yield client.notify.markViewedAllNotif()
    const res: AxiosResponse = yield client.notify.getGroupNotifByUser()
    const { data } = res
    yield put(actionStore.setGroupNotiOfUser(data))
    yield put(totalDataActions.getUnReadNotif())
    if (response) {
      openNotification('success', {
        message: 'Đọc tất cả thông báo thành công!'
      })
    }
  } catch (e) {
    console.info('lỗi')
  } finally {
    yield put(actionStore.setLoadingFalse())
  }
}

function* watcher_setMarkViewedAllNotiSaga() {
  yield takeLatest(actionStore.setMarkViewedAllNoti, setMarkViewedAllNotiSaga)
}

//delete all noti
function* deleteAllNotiSaga(
  action: ReturnType<typeof actionStore.setMarkViewedNoti>
) {
  try {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    yield put(actionStore.setLoadingTrue())
    const response: AxiosResponse =
      yield client.notify.deleteAllUnReadNotifByUser()
    const res: AxiosResponse = yield client.notify.getGroupNotifByUser()
    const { data } = res
    yield put(actionStore.setGroupNotiOfUser(data))
    yield put(totalDataActions.getNotiOfUser())
    yield put(actionStore.setLoadingFalse())
    yield put(totalDataActions.setUnReadNoti(0))
    if (response) {
      openNotification('success', {
        message: 'Bạn đã xóa tất cả thông báo thành công!'
      })
    }
  } catch (e) {
    console.info('lỗi')
    yield put(actionStore.setLoadingFalse())
  }
}

function* watcher_deleteAllNotiSaga() {
  yield takeLatest(actionStore.deleteAllNoti, deleteAllNotiSaga)
}

function* getBookingBillByTransactionSaga(
  action: ReturnType<typeof actionStore.getBookingByTransaction>
) {
  try {
    const response: AxiosResponse = yield client.cskh.getBookingInfoCSKH(
      {
        smsCode: action.payload.smsCode
      },
      {
        appid: 'medpro'
      }
    )
    const { data } = response
    yield put(actionStore.setBookingByTransaction(data))
  } catch (e) {
    console.info('lỗi')
  }
}

function* watcher_getBookingBillByTransactionSaga() {
  yield takeLatest(
    actionStore.getBookingByTransaction,
    getBookingBillByTransactionSaga
  )
}

function* fetchCountriesSaga(action: any) {
  try {
    const { data } = yield call(() => client.country.getList({}))
    yield put(setCountries(data))
  } catch (e) {
    console.info('lỗi get countries')
  }
}

function* watcherGetCountries() {
  yield takeLatest(fetchCountries, fetchCountriesSaga)
}

function* getProvinces(action: any) {
  try {
    const { data } = yield call(() =>
      client.city.getList({ country_code: '203' })
    )
    yield put(actionStore.setProvinces(data))
  } catch (e) {
    console.info('lỗi lấy danh sách Tỉnh thành phố')
  }
}

function* watcherGetProvince() {
  yield takeLatest(actionStore.fetchProvinces, getProvinces)
}

function* getDistricts(action: any) {
  try {
    const { data } = yield call(() =>
      client.district.getList({ city_id: action.payload.city_id })
    )
    yield put(actionStore.setDistricts(data))
    yield put(actionStore.setWards([]))
  } catch (e) {
    console.info('lỗi lấy danh sách Quận huyện')
  }
}

function* watcherGetDistricts() {
  yield takeLatest(actionStore.fetchDistricts, getDistricts)
}

function* getWards(action: any) {
  try {
    const { data } = yield call(() => client.ward.getList(action.payload))
    yield put(actionStore.setWards(data))
  } catch (e) {
    console.info('lỗi')
  }
}

function* watcherGetWards() {
  yield takeLatest(actionStore.fetchWards, getWards)
}

function* getRelative(action: any) {
  try {
    const { data } = yield call(() =>
      client.relation.getRelationType({ partnerid: 'medpro' })
    )
    yield put(totalDataActions.setRelative(data))
  } catch (e) {
    console.info('lỗi')
  }
}

function* watcherGetRelative() {
  yield takeLatest(actionStore.fetchRelative, getRelative)
}

function* getTransactionInfoSaga(
  action: ReturnType<typeof actionStore.getTransactionInfo>
) {
  try {
    const response: AxiosResponse =
      yield client.booking.getBookingWithTransactionCode(
        {
          transactionId: action.payload.transactionId
        },
        {
          appid: 'medpro',
          token:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6IiIsInN1YiI6MCwidXNlck1vbmdvSWQiOiI2MTZjZGY3OGM5NWE2ZTAwMWE1M2I1M2IiLCJpYXQiOjE2NjY4NTg1MzksImV4cCI6NDgyMjYxODUzOX0.Tx_qUq7QsfWonvXOuEb5dWukADdGKop0j7YWLOlCx5c'
        }
      )
    const { data } = response
    yield put(actionStore.setTransactionInfo(data))
  } catch (e) {
    console.info('lỗi')
  }
}

function* watcher_getTransactionInfoSaga() {
  yield takeLatest(actionStore.getTransactionInfo, getTransactionInfoSaga)
}

function* getNewsSaga(action: any) {
  try {
    const data = yield call((p) => getNewsHomePageServer(p), action.payload)
    const news = data.reduce((c, i) => {
      c[i.cate] = i.news
      return c
    }, {})

    yield put(totalDataActions.getNewsSuccess(news))
  } catch (e) {
    console.log('err get news', e)
    yield put(totalDataActions.getNewsFailed(getError(e)))
  }
}

function* watchGetNews() {
  yield takeLatest(totalDataActions.getNews, getNewsSaga)
}

function* getBannerNewSaga() {
  try {
    const { data } = yield client.total.getBannerNews()
    yield put(totalDataActions.setBannerNews(data))
  } catch (e) {
    console.log('Lỗi không lấy được thông tin banner')
  }
}

function* watcher_getBannerNewSaga() {
  yield takeLatest(totalDataActions.getBannerNews, getBannerNewSaga)
}

const totalSagas = function* root() {
  yield all([
    fork(watcher_unLinkPatientSaga),
    fork(watcher_getAllBookingOfUserSaga),
    fork(watcher_getAllBookingSaga),
    fork(watcher_getNotiOfUserSaga),
    fork(watcher_getGroupNotiOfUserSaga),
    fork(watcher_setMarkViewedNotiSaga),
    fork(watcher_setMarkViewedAllNotiSaga),
    fork(watcher_deleteAllNotiSaga),
    fork(watcher_getBookingBillByTransactionSaga),
    fork(watcher_getTransactionInfoSaga),
    fork(watcherGetCountries),
    fork(watcherGetProvince),
    fork(watcherGetDistricts),
    fork(watcherGetWards),
    fork(watcherGetRelative),
    fork(watchGetNews),
    fork(watcher_getUnReadNotif),
    fork(watcher_getBannerNewSaga)
  ])
}

export default totalSagas
