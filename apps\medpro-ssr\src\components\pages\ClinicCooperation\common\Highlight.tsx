import React from 'react'
import cx from 'classnames'
import BgHighlight from '../images/bgHighlight.png'
import styles from '../styles.module.less'

const Highlight = ({ windowWidth }: any) => {
  return (
    <div
      className={cx(styles['wrapper'])}
      style={{
        backgroundImage: `url(${BgHighlight.src})`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'contain',
        backgroundPosition: 'bottom center',
        height: '100%'
      }}
    >
      <div className={styles['highlightTitle']}>
        {windowWidth && windowWidth <= 576 ? (
          <h3>
            Những lợi ích
            <br />
            khi hợp tác với Medpro
          </h3>
        ) : (
          <h3>Những lợi ích khi hợp tác với Medpro</h3>
        )}
      </div>
      <div className={styles['highlight']}>
        <div className={cx(styles['tableWrapper'])}>
          <div className={styles['comparisonContainer']}>
            <div className={cx(styles['headerRow'])}>
              <div className={cx(styles['headerCell'], styles['empty'])}></div>
              <div className={cx(styles['headerCell'], styles['selfContact'])}>
                Tự liên hệ bệnh viện
              </div>
              <div className={cx(styles['headerCell'], styles['medpro'])}>
                Đăng ký qua Medpro
              </div>
              <div
                className={cx(styles['headerCell'], styles['dataEmpty'])}
              ></div>
            </div>
            <div className={cx(styles['contentRow'])}>
              <div className={cx(styles['contentCell'], styles['category'])}>
                Lựa chọn gói khám
              </div>
              <div className={cx(styles['contentCell'], styles['dataOne'])}>
                Doanh nghiệp tự tìm hiểu, dễ chọn sai gói
              </div>
              <div className={cx(styles['contentCell'], styles['dataTwo'])}>
                Tư vấn trung lập, tối ưu theo ngành nghề
              </div>
              <div
                className={cx(styles['contentCell'], styles['dataEmpty'])}
              ></div>
            </div>
            <div className={cx(styles['contentRow'])}>
              <div className={cx(styles['contentCell'], styles['category'])}>
                Địa điểm khám
              </div>
              <div className={cx(styles['contentCell'], styles['dataOne'])}>
                Chỉ làm việc với một bệnh viện
              </div>
              <div className={cx(styles['contentCell'], styles['dataTwo'])}>
                Chọn từ 300+ cơ sở y tế trên toàn quốc
              </div>
              <div
                className={cx(styles['contentCell'], styles['dataEmpty'])}
              ></div>
            </div>
            <div className={cx(styles['contentRow'])}>
              <div className={cx(styles['contentCell'], styles['category'])}>
                Đặt lịch khám
              </div>
              <div className={cx(styles['contentCell'], styles['dataOne'])}>
                Khó điều phối, dễ bị trễ lịch
              </div>
              <div className={cx(styles['contentCell'], styles['dataTwo'])}>
                Sắp xếp lịch nhanh chóng, có ưu tiên
              </div>
              <div
                className={cx(styles['contentCell'], styles['dataEmpty'])}
              ></div>
            </div>
            <div className={cx(styles['contentRow'])}>
              <div className={cx(styles['contentCell'], styles['category'])}>
                Quy trình thủ tục
              </div>
              <div className={cx(styles['contentCell'], styles['dataOne'])}>
                Mất nhiều thời gian tự xử lý
              </div>
              <div className={cx(styles['contentCell'], styles['dataTwo'])}>
                Medpro hỗ trợ toàn bộ thủ tục
              </div>
              <div
                className={cx(styles['contentCell'], styles['dataEmpty'])}
              ></div>
            </div>
            <div className={cx(styles['contentRow'])}>
              <div className={cx(styles['contentCell'], styles['category'])}>
                Hỗ trợ trong quá trình khám
              </div>
              <div className={cx(styles['contentCell'], styles['dataOne'])}>
                Không có hướng dẫn, dễ ra chậm trễ
              </div>
              <div className={cx(styles['contentCell'], styles['dataTwo'])}>
                Có nhân viên hỗ trợ, xử lý phát sinh
              </div>
              <div
                className={cx(styles['contentCell'], styles['dataEmpty'])}
              ></div>
            </div>
            <div className={cx(styles['contentRow'])}>
              <div className={cx(styles['contentCell'], styles['category'])}>
                Nhận kết quả
              </div>
              <div className={cx(styles['contentCell'], styles['dataOne'])}>
                Rời rạc, khó theo dõi
              </div>
              <div className={cx(styles['contentCell'], styles['dataTwo'])}>
                Báo cáo tổng hợp, tư vấn chuyên sâu
              </div>
              <div
                className={cx(styles['contentCell'], styles['dataEmpty'])}
              ></div>
            </div>
            <div className={cx(styles['contentRow'])}>
              <div className={cx(styles['contentCell'], styles['category'])}>
                Chi phí
              </div>
              <div className={cx(styles['contentCell'], styles['dataOne'])}>
                Không có chiết khấu, giá cao hơn
              </div>
              <div className={cx(styles['contentCell'], styles['dataTwo'])}>
                Hưởng chiết khấu từ bệnh viện đối tác
              </div>
              <div
                className={cx(styles['contentCell'], styles['dataEmpty'])}
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Highlight
