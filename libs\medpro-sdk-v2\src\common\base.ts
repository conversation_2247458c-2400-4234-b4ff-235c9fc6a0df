import type { ClientOptions, Options } from '../interfaces'
import { API_ROOT } from './constants'
import { jsonToQueryString, stripTrailingSlash } from './utils'

export class Base {
  private defaultClientOptions = {
    apiRoot: API_ROOT,
    partnerid: '',
    appid: '',
    osid: '',
    ostoken: '',
    locale: '',
    token: '',
    cskhtoken: '',
    platform: '',
    version: ''
  }

  protected options: Options

  constructor(options?: ClientOptions) {
    this.options = { ...this.defaultClientOptions, ...(options || {}) }
    this.options.apiRoot = stripTrailingSlash(this.options.apiRoot)
  }

  api(path: string, param?: any): string {
    const query = param ? `?${jsonToQueryString(param)}` : ''

    return `${this.options.apiRoot}/${path}${query}`
  }
}
