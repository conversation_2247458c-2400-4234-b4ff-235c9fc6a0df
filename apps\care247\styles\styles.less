@import '~antd/es/style/themes/default.less';
@import '~antd/dist/antd.less';
@import './custom-variable.less';

@font-face {
  font-family: 'Roboto';
  src: url('../public/fonts/Roboto/Roboto-Light.ttf') format('woff2 supports variations'),
  url('../public/fonts/Roboto/Roboto-Light.ttf') format('woff2-variations'),
  url('../public/fonts/Roboto/Roboto-Light.ttf') format('truetype');
  font-weight: 300;
}

@font-face {
  font-family: 'Roboto';
  src: url('../public/fonts/Roboto/Roboto-Regular.ttf') format('woff2 supports variations'),
  url('../public/fonts/Roboto/Roboto-Regular.ttf') format('woff2-variations'),
  url('../public/fonts/Roboto/Roboto-Regular.ttf') format('truetype');
  font-weight: 400;
}

@font-face {
  font-family: 'Roboto';
  src: url('../public/fonts/Roboto/Roboto-Medium.ttf') format('woff2 supports variations'),
  url('../public/fonts/Roboto/Roboto-Medium.ttf') format('woff2-variations'),
  url('../public/fonts/Roboto/Roboto-Medium.ttf') format('truetype');
  font-weight: 500;
}

@font-face {
  font-family: 'Roboto';
  src: url('../public/fonts/Roboto/Roboto-Bold.ttf') format('woff2 supports variations'),
  url('../public/fonts/Roboto/Roboto-Bold.ttf') format('woff2-variations'),
  url('../public/fonts/Roboto/Roboto-Bold.ttf') format('truetype');
  font-weight: 700;
}

ul {
  list-style-position: inside;
  padding-inline-start: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  max-width: 100vw;
  scroll-behavior: smooth;
  font-family: 'Roboto', serif !important;
  font-variant: normal !important;
  font-feature-settings: normal !important;
  font-size: @16;
  text-rendering: optimizeLegibility;
  color: #12263f !important;
  -webkit-font-smoothing: antialiased;
  overflow-x: hidden;
}

* {
  font-family: 'Roboto', serif !important;
  font-variant: normal !important;
  font-feature-settings: normal !important;
}

.grecaptcha-badge {
  visibility: hidden !important;
}

.ql-video {
  width: 100%;
  aspect-ratio: 16/9;
}

.ql-image {
  display: block;
  margin: auto;
  width: auto;
  max-width: 100%;
}

.ql-indent-1 {
  margin-left: 32px !important;
  list-style: circle !important;
  text-align: justify;
}

.ql-indent-2 {
  margin-left: 48px;
  list-style: square !important;
  text-align: justify;
}

.ql-indent-3 {
  margin-left: 64px;
  list-style: circle !important;
  text-align: justify;
  @media (max-width: 576px) {
    margin-left: 0;
  }
}

.ql-indent-4 {
  margin-left: 80px;
  list-style: square !important;
  text-align: justify;
}

.ql-indent-5 {
  margin-left: 96px;
  list-style: circle !important;
  text-align: justify;
}

.ql-indent-7 {
  margin-left: 112px;
  list-style: square !important;
  text-align: justify;
}

.ql-indent-8 {
  margin-left: 128px;
  list-style: circle !important;
  text-align: justify;
}

.ql-indent-9 {
  margin-left: 144px;
  list-style: square !important;
  text-align: justify;
}

.grecaptcha-badge {
  visibility: hidden !important;
}
