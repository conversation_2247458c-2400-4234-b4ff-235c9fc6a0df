import { Drawer } from 'antd'
import React, { CSSProperties, ReactNode } from 'react'
import styles from './styles.module.less'
import cx from 'classnames'
interface drawerProps {
  onClose: (e?: any) => void
  open: boolean
  title?: any
  width?: number | string
  height?: number | string
  extra?: ReactNode
  placement?: any
  children: ReactNode
  className?: any
  style?: CSSProperties
}
const DefaultDrawer = ({
  onClose,
  open,
  title,
  extra,
  height,
  placement,
  children,
  className,
  style
}: drawerProps) => {
  return (
    <Drawer
      title={
        <span className={styles['titleDrawer']}>
          {title ? title : 'Vui lòng chọn'}
        </span>
      }
      placement={placement ? placement : 'bottom'}
      onClose={onClose}
      open={open}
      height={height ? height : '100%'}
      className={cx(styles['drawer'], className)}
      extra={extra}
      style={style}
      closable={!!onClose}
    >
      {children}
    </Drawer>
  )
}
export default DefaultDrawer
