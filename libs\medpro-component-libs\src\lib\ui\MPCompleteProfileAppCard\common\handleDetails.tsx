import { Form, Input, Select } from 'antd'
import styles from '../styles.module.less'
import { Valid, sexData } from '@medpro-libs/libs'
import { get, size } from 'lodash'
import cx from 'classnames'
// export interface ListFormIF {}

const valid = new Valid()
const { Option } = Select

interface ListFormIF {
  data: any
  district?: any
  ward?: any
  openSelect: (field: any) => void
  phoneLocale: any[]
  selectPhonePrefix: (prefix: string, iso: string) => void
  form: any
  iso: string
  phoneNumber: string
  changePhoneNumber: (phone: string) => void
  showRelative: boolean
  partnerId?: any
  age?: number
  toggleAge: any
  isForeign: boolean
}

export const handleDetails = ({
  data,
  openSelect,
  phoneLocale,
  selectPhonePrefix,
  form,
  iso,
  phoneNumber,
  changePhoneNumber,
  showRelative,
  toggleAge,
  isForeign
}: ListFormIF) => {
  const label_code_ID = 'Mã định danh/CCCD/Passport'
  const handleRequireInput = (label: string, require: boolean) => {
    if (require) {
      return (
        <>
          {label} <sup className={styles['requireInput']}>*</sup>
        </>
      )
    }
    return <>{label}</>
  }

  const ignoreProperties = get(data, 'patient.propertyIgnoreUpdate', [])
  const list = [
    {
      id: 'name',
      type: 'text',
      label: 'Họ và tên (có dấu)',
      placeholder: 'Nhập họ và tên',
      require: true,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.name }]}
            className={styles['formInputItem']}
          >
            <Input
              type={type}
              disabled={disabled}
              placeholder={placeholder}
              className={cx({
                [styles['validInput']]: form.getFieldValue('name') && !disabled
              })}
              style={{ textTransform: 'uppercase' }}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'birthdate',
      type: 'text',
      label: 'Ngày sinh',
      placeholder: 'Nhập ngày sinh',
      require: true,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.birthdate }]}
            className={styles['formInputItem']}
          >
            <Input
              type={type}
              disabled={disabled}
              placeholder={placeholder}
              className={cx({
                [styles['validInput']]:
                  form.getFieldValue('birthdate') && !disabled
              })}
              onChange={() => {
                toggleAge(form.getFieldValue('birthdate'))
              }}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: '50%'
    },
    {
      id: 'sex',
      type: 'select',
      label: 'Giới tính',
      placeholder: 'Chọn giới tính',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.sex }]}
            className={styles['formInputItem']}
          >
            <Select
              placeholder={placeholder}
              disabled={disabled}
              open={false}
              onClick={() => !disabled && openSelect({ id })}
              className={cx({
                [styles['validInput']]:
                  !disabled &&
                  (form.getFieldValue('sex') === 0 ||
                    form.getFieldValue('sex') === 1),
                [styles['disabledValidInput']]: disabled
              })}
            >
              {sexData?.map((item, index) => (
                <Option key={index} value={item.value}>
                  {item.title}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: '50%'
    },
    {
      id: 'cmnd',
      type: 'text',
      label: label_code_ID,
      placeholder: `Vui lòng nhập ${label_code_ID}`,
      require: true,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[
              {
                validator: valid.cccd
              }
            ]}
            className={styles['formInputItem']}
          >
            <Input
              type={type}
              disabled={disabled}
              placeholder={placeholder}
              className={cx({
                [styles['validInput']]: form.getFieldValue('cmnd') && !disabled
              })}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'insuranceCode',
      type: 'text',
      label: 'Mã bảo hiểm y tế',
      placeholder: 'Nhập Mã bảo hiểm y tế',
      require: false,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            // rules={[{ validator: valid.mobile }]}
            className={styles['formInputItem']}
          >
            <Input
              type={type}
              disabled={disabled}
              placeholder={placeholder}
              className={cx({
                [styles['validInput']]:
                  form.getFieldValue('insuranceId') && !disabled
              })}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'email',
      type: 'text',
      label: 'Địa chỉ Email',
      placeholder: 'Nhập địa chỉ Email',
      require: false,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.emailNoRequired }]}
            className={styles['formInputItem']}
          >
            <Input
              type={type}
              disabled={disabled}
              placeholder={placeholder}
              className={cx({
                [styles['validInput']]: form.getFieldValue('email') && !disabled
              })}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'profession_id',
      type: 'select',
      label: 'Nghề nghiệp',
      placeholder: 'Nhập Nghề nghiệp',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.profession }]}
            className={styles['formInputItem']}
          >
            <Select
              placeholder={placeholder}
              disabled={showRelative ? true : disabled}
              open={false}
              onClick={() =>
                !(showRelative ? true : disabled) &&
                openSelect({
                  id,
                  title: 'Chọn nghề nghiệp',
                  data: data?.profession
                })
              }
              className={cx({
                [styles['validInput']]:
                  form.getFieldValue('profession_id') &&
                  !(showRelative ? true : disabled),
                [styles['disabledValidInput']]: showRelative ? true : disabled
              })}
            >
              {size(data?.profession) > 0 &&
                data?.profession?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'mobile',
      type: 'text',
      label: 'Số điện thoại',
      placeholder: 'Nhập số điện thoại',
      require: showRelative ? false : true,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[
              { required: require, message: 'Vui lòng nhập số điện thoại' }
            ]}
            className={cx(styles['formInputItem'], styles['phoneNumber'])}
          >
            <Input
              type={type}
              disabled={disabled}
              placeholder={placeholder}
              value={phoneNumber}
              onChange={(v) => changePhoneNumber(v.target.value)}
              className={cx({
                [styles['validInput']]:
                  form.getFieldValue('mobile') && !disabled
              })}
            />
            <Select
              value={iso}
              disabled={disabled}
              onChange={(v, e: any) => {
                console.log('v', v)
                console.log('e', e)
                selectPhonePrefix(e.key, v)
              }}
              className={cx({
                [styles['selectPhoneLocale']]: true,
                [styles['disabledValidInput']]: disabled
              })}
            >
              {size(phoneLocale) > 0 &&
                phoneLocale?.map((item: any) => (
                  <Option key={item.label} value={item.iso}>
                    <img
                      src={item.image}
                      alt=''
                      className={styles['flag']}
                      style={{
                        width: '32px',
                        height: '20px',
                        borderRadius: '4px',
                        marginRight: '5px'
                      }}
                    />
                    {item.label}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'country_code',
      type: 'text',
      label: 'Quốc gia',
      placeholder: 'Chọn quốc gia',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.country }]}
            className={cx(styles['formInputItem'], {
              [styles['disabledValidInput']]: disabled
            })}
          >
            <Select
              disabled={disabled}
              open={false}
              placeholder={placeholder}
              onClick={() =>
                !disabled &&
                openSelect({
                  id,
                  title: 'Chọn quốc gia',
                  data: data?.countries
                })
              }
            >
              {size(data?.countries) > 0 &&
                data?.countries?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    },
    {
      id: 'dantoc_id',
      type: 'text',
      label: 'Dân tộc',
      placeholder: 'Chọn dân tộc',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ required: require }]}
            className={styles['formInputItem']}
          >
            <Select
              placeholder={placeholder}
              disabled={
                form.getFieldValue('dantoc_id') !== 'medpro_1' ? true : disabled
              }
              open={false}
              onClick={() =>
                !(form.getFieldValue('dantoc_id') !== 'medpro_1'
                  ? true
                  : disabled) &&
                openSelect({
                  id,
                  title: 'Chọn dân tộc',
                  data: data?.nation
                })
              }
              className={cx({
                [styles['validInput']]:
                  form.getFieldValue('dantoc_id') &&
                  !(form.getFieldValue('dantoc_id') !== 'medpro_1'
                    ? true
                    : disabled),
                [styles['disabledValidInput']]:
                  form.getFieldValue('dantoc_id') !== 'medpro_1'
                    ? true
                    : disabled
              })}
            >
              {size(data?.nation) > 0 &&
                data?.nation?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: 'fuild'
    }
  ]

  return list.map((l) => ({
    ...l,
    disabled: ignoreProperties.includes(l.id)
  }))
}
