.patientInfoContainer {
  background: white;
  border-radius: 16px;
  padding: 24px;
  height: fit-content;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  .logo {
    text-align: center;
    margin-bottom: 24px;

    .logoPlaceholder {
      width: 120px;
      height: 60px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 12px;
      color: white;
      font-weight: bold;
      font-size: 16px;
    }

    h2 {
      font-size: 14px;
      color: #666;
      font-weight: 400;
      line-height: 1.4;
      margin: 0;
    }
  }

  .patientInfo {
    margin-bottom: 24px;

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      border-bottom: 2px solid #f0f0f0;
      padding-bottom: 8px;
    }

    .infoItem {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      padding: 8px 0;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .label {
        font-weight: 500;
        color: #666;
        flex: 1;
      }

      .value {
        font-weight: 600;
        color: #333;
        text-align: right;
        flex: 1;
      }
    }
  }

  .imageSection {
    text-align: center;

    .imagePlaceholder {
      width: 100%;
      max-width: 200px;
      height: 120px;
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      border-radius: 12px;
      margin: 0 auto 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 20px;
      position: relative;

      .doctorIcon,
      .patientIcon {
        font-size: 32px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
      }

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 60%;
        height: 2px;
        background: linear-gradient(90deg, transparent 0%, #2196f3 50%, transparent 100%);
      }
    }

    .imageText {
      p {
        font-size: 12px;
        color: #666;
        line-height: 1.4;
        margin: 8px 0;

        &:first-child {
          font-weight: 600;
          color: #333;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .logo {
      .logoPlaceholder {
        width: 100px;
        height: 50px;
        font-size: 14px;
      }

      h2 {
        font-size: 12px;
      }
    }

    .imageSection {
      .imagePlaceholder {
        height: 100px;

        .doctorIcon,
        .patientIcon {
          font-size: 24px;
        }
      }
    }
  }
}
