.paymentCard {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;

  .paymentTabs {
    display: flex;
    background: #f8f9fa;

    .tab {
      flex: 1;
      padding: 16px;
      text-align: center;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 500;
      color: #666;
      border-bottom: 3px solid transparent;

      .tabIcon {
        font-size: 16px;
      }

      &.activeTab {
        background: white;
        color: #1890ff;
        border-bottom-color: #1890ff;
      }
    }
  }

  .bankInfo {
    padding: 24px;

    h3 {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #f0f0f0;
    }

    .orderInfo,
    .bankDetails {
      margin-bottom: 24px;
    }

    .infoRow {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      font-size: 14px;

      span:first-child {
        color: #666;
      }

      span:last-child {
        font-weight: 500;
        color: #333;
      }
    }

    .note {
      background: #fff7e6;
      border: 1px solid #ffd591;
      border-radius: 6px;
      padding: 12px;
      font-size: 12px;
      color: #d46b08;

      span:first-child {
        font-weight: bold;
        margin-right: 8px;
      }
    }
  }

  .qrSection {
    padding: 0 24px 24px;

    h3 {
      font-size: 14px;
      color: #333;
      margin-bottom: 16px;
      text-align: center;
    }

    .qrCode {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;

      .qrPlaceholder {
        width: 200px;
        height: 200px;
        border: 2px dashed #d9d9d9;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fafafa;

        .qrCodeImage {
          width: 160px;
          height: 160px;
          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K') center/contain no-repeat;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
        }
      }

      .vietorLogo {
        font-size: 12px;
        font-weight: bold;
        color: #1890ff;
        padding: 4px 8px;
        border: 1px solid #1890ff;
        border-radius: 4px;
      }
    }
  }

  .actionButtons {
    display: flex;
    gap: 12px;
    padding: 0 24px 24px;

    button {
      flex: 1;
      padding: 12px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      background: white;
      cursor: pointer;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      &.downloadBtn {
        color: #52c41a;
        border-color: #52c41a;

        &:hover {
          background: #f6ffed;
        }
      }

      &.helpBtn {
        color: #fa8c16;
        border-color: #fa8c16;

        &:hover {
          background: #fff7e6;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .paymentCard {
    .bankInfo {
      padding: 16px;
    }

    .qrSection {
      padding: 0 16px 16px;

      .qrCode .qrPlaceholder {
        width: 160px;
        height: 160px;

        .qrCodeImage {
          width: 120px;
          height: 120px;
        }
      }
    }

    .actionButtons {
      flex-direction: column;
      padding: 0 16px 16px;

      button {
        width: 100%;
      }
    }
  }
}
