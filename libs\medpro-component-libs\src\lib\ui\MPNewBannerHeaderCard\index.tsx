import { Col, Row, Space } from 'antd'
import styles from './styles.module.less'
import MPSearch from '../../shapes/MPSearchTyping'
import { HiCheckBadge } from 'react-icons/hi2'
import { LocationType } from '../MPNewHeaderCard'

export interface NewBannerHeaderProps {
  data: any
  onSearchDebounce: (item: any) => Promise<void>
  searchData: any
  locationData: any
  locationType?: LocationType
  handleBookingSearch: ({ type, item }: any) => Promise<void>
  handleDetectLocation: () => void
  searching: boolean
}

const MPNewBannerHeaderCard = (props: NewBannerHeaderProps) => {
  const title = 'Kết nối Người Dân với \n Cơ sở & Dịch vụ Y tế hàng đầu'
  return (
    <div className={styles['bannerHeader']} aria-labelledby='Ảnh nền Banner'>
      <Row className={styles['container']}>
        <Col span={24} lg={24} className={styles['contentLeft']}>
          <div className={styles['wrapper']}>
            <h1 className={styles['title']}>{title}</h1>
            <MPSearch
              onSearchDebounce={props.onSearchDebounce}
              searchData={props.searchData}
              searching={props.searching}
              handleBookingSearch={props.handleBookingSearch}
              handleDetectLocation={props.handleDetectLocation}
              locationData={props.locationData}
              locationType={props.locationType}
            />
            <Space direction='vertical' className={styles['benefit']}>
              {props.data.desc.map((item: string) => {
                return (
                  <h3 className={styles['desc']}>
                    <HiCheckBadge color='#52c41a' size={18} />
                    {item}
                  </h3>
                )
              })}
            </Space>
          </div>
        </Col>
      </Row>
    </div>
  )
}

export default MPNewBannerHeaderCard
