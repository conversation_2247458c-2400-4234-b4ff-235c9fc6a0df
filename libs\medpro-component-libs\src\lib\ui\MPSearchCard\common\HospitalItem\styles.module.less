.pickCard {
  border-color: #00b5f1 !important;
}

.boxHospital {
  border: 2px solid #00b5f1 !important;
  box-shadow: 0px 4px 15px rgba(116, 157, 206, 0.5) !important;
}

.card {
  position: relative;
  overflow: hidden;
  gap: 14px;
  padding: 20px 20px;
  width: 100%;
  min-height: 130px;
  border-bottom: 2px solid #eaeaea;
  transition: all 0.23s ease;
  background-color: white;

  &:hover {
    box-shadow: 0px 4px 15px rgba(116, 157, 206, 0.5) !important;
    border-bottom: 2px solid #00b5f1;
    cursor: pointer;
    @media only screen and (max-width: 992px) {
      border-bottom: none;
    }
  }

  @media only screen and (max-width: 768px) {
    padding: 14px 16px;
  }
  @media only screen and (max-width: 577px) {
    flex-direction: column;
    align-items: center;
    margin-bottom: 12px;
    border-radius: 12px;
    gap: 12px;
  }
  @media (max-width: 472px) {
    padding: 0;
    border: 1px solid transparent;
    box-shadow: 0px 4px 15px rgba(116, 157, 206, 0.5) !important;
    &:hover {
      border: 1px solid #00b5f1;
    }
  }
  // @media only screen and (max-width: 992px) {
  //   flex-direction: column;
  //   align-items: center;
  //   margin-bottom: 12px;
  //   border-radius: 12px;
  //   gap: 12px;
  //   border: 1px solid #eaeaea;
  // }
  .DetailInfo {
    width: 100%;
    display: flex;
    gap: 12px;
    @media (max-width: 472px) {
      padding: 14px 16px 0 16px;
    }
  }

  .tagCashBack {
    position: absolute;
    top: 13px;
    right: -62px;
    width: 190.81px;
    height: 25.86px;
    border: none;
    transform: rotate(35deg);
    background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
    text-transform: uppercase;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 12px;
    font-weight: 700;
    line-height: 17.58px;
    @media (max-width: 576px) {
      width: 107.53px;
      height: 20.77px !important;
      top: 10px;
      right: -25px;
      font-size: 10px;
      line-height: 11.72px;
    }

    &:hover {
      cursor: pointer;
      background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
      color: white;
    }

    &:focus {
      background: linear-gradient(180deg, #ff8500 -1.88%, #fb3c00 103.01%);
      color: white;
    }
  }

  .popoverCashBack {
    :global {
      .ant-popover-content {
        border-radius: 12px !important;
        max-width: 510px;
      }

      .ant-popover-inner {
        backdrop-filter: blur(30px);
        background-color: rgba(255, 255, 255, 0.9);
      }

      .ant-popover-inner-content {
        p {
          font-size: 16px;
          font-weight: 400;
          line-height: 20px;
        }
      }
    }
  }

  .cardImage {
    min-width: 90px !important;
    min-height: 90px !important;
    align-items: center;
    display: flex;
    justify-content: center;
    flex-direction: column;

    @media (max-width: 420px) {
      width: fit-content !important;
      min-width: fit-content !important;
      min-height: fit-content !important;
      display: block;
      img {
        width: 43px !important;
        height: 43px !important;
      }
    }

    .contentDistance {
      display: flex;

      .contentDropdownDistance {
        font-size: 14px;
        color: #11a2f3;
        font-weight: 400;
      }

      .distanceNumber {
        font-size: 16px;
      }

      .distanceUnit {
        font-size: 16px;
      }
    }
  }

  .cardBody {
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .cardContent {
      width: 100%;
      margin-bottom: 12px;
      @media only screen and (max-width: 630px) {
        max-width: 100%;
        margin-bottom: 8px;
      }

      .title {
        font-weight: 500;
        font-size: 25px;
        line-height: 29px;
        margin-bottom: 12px;
        width: 100%;
        padding-right: 16px;

        svg {
          margin-top: 2px;
          vertical-align: top;
          @media (max-width: 576px) {
            width: 21px;
            height: 21px;
          }
        }

        @media only screen and (max-width: 768px) {
          font-size: 20px;
          font-weight: 500;
          line-height: normal;

          margin-bottom: 8px;
        }
        @media only screen and (max-width: 576px) {
          margin-bottom: 16px;
        }
      }

      .titleSponsor {
        width: 90%;
      }

      .contentPrice {
        color: var(--accent, #ffb54a);
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin: 0;

        strong {
          font-weight: 700;
        }
      }

      .contentItem {
        display: flex;
        gap: 5px;
        font-weight: 400;
        font-size: 17px;
        line-height: 21px;
        color: #858585;
        margin-bottom: 12px;

        &:last-of-type {
          margin-bottom: 0;
        }

        @media only screen and (max-width: 768px) {
          font-size: 14px;
          line-height: 16px;
        }
        @media only screen and (max-width: 577px) {
          font-size: 15px;
          font-weight: 400;
          line-height: normal;
          svg {
            width: 12px !important;
            height: 12px !important;
          }
        }
      }

      .status {
        background: #ffb54a;
        border-radius: 16px;
        color: #ffffff;
        padding: 6px 12px;
        display: inline-block;
        font-weight: 500;
        font-size: 12px;
        line-height: 15px;
        margin-top: 10px;
        font-family: 'Montserrat', sans-serif !important;
        @media only screen and (max-width: 420px) {
          font-size: 12px;
          padding: 3px 7px;
        }
      }

      .rating {
        color: #ffb54a;
        margin-top: 10px;
        font-size: 16px;
        font-weight: 500;
        line-height: 18px;
        @media (max-width: 576px) {
          margin-bottom: 5px;
          margin-top: 0;
        }
      }

      .ratingOff {
        color: #8f8e8d;
        margin-top: 10px;
        font-size: 16px;
        font-weight: 500;
        line-height: 18px;
        @media (max-width: 576px) {
          margin-bottom: 5px;
          margin-top: 0;
        }
      }
    }
  }

  .desktop {
    @media (max-width: 472px) {
      display: none !important;
    }
  }

  .mobile {
    display: none !important;
    @media (max-width: 472px) {
      display: flex !important;
      background: #eff7ff;
      padding: 10px;
      justify-content: center;
      align-items: center;
      border-bottom-left-radius: 12px;
      border-bottom-right-radius: 12px;
    }
  }

  .onlyBtn {
    @media (max-width: 472px) {
      width: 100% !important;
    }
  }

  .btnControl {
    display: flex;
    width: 100%;
    gap: 12px;
    @media only screen and (max-width: 992px) {
      display: flex;
      flex-direction: row;
      width: 100%;
    }
    @media (max-width: 568px) {
      margin-left: 0px;
    }

    button {
      border: none;
      padding: 10px 16px;
      width: 167px;
      height: 42px !important;
      transition: all 0.3s ease;
      border-radius: 30px;
      font-weight: 500;
      font-size: 16px;
      line-height: normal;
      @media (max-width: 992px) {
        width: 167px;
        height: 42px;
      }
    }

    .btnView {
      color: #00b5f1;
      border: 1px solid #00b5f1;
      background-color: #fff;
      @media (max-width: 576px) {
        width: 50%;
      }

      &:active,
      &:focus {
        @media only screen and (max-width: 992px) {
          border: none;
        }
      }

      &:hover {
        background: linear-gradient(83.63deg, #d5f3fd, #f0f2f2);
      }
    }

    .btnBooking {
      background: linear-gradient(83.63deg, #00b5f1 33.34%, #00e0ff 113.91%);
      color: white;
      @media (max-width: 576px) {
        width: 50%;
      }

      &:active,
      &:focus {
        @media only screen and (max-width: 992px) {
          border: none;
          background: none !important;
          -webkit-text-fill-color: #00b5f1;
        }
      }

      &:hover {
        @media (min-width: 578px) {
          box-shadow: 0px 4px 30px 0px rgba(116, 157, 206, 0.2);
          -webkit-text-fill-color: white;
          background: linear-gradient(83.63deg, #07aae0, #00c6e1) !important;
        }
      }
    }
  }

  .sponsor {
    position: absolute;
    top: 0px;
    right: 6px;
    margin-top: -5px;
    @media (max-width: 576px) {
      right: 0;
    }
  }

  .sponsor_cashback {
    right: 60px;
    @media (max-width: 576px) {
      right: 48px;
    }
  }
}
