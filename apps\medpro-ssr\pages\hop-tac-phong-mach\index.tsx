import React, { useCallback, useEffect, useState } from 'react'
import client from '../../config/medproSdk'
import ClinicCooperation from '../../src/components/pages/ClinicCooperation'
import { showError } from '../../utils/utils.notification'
import useWindowDimensions from '../../hooks/useWindowDimesion'
import {
  showErrorNotification,
  showSuccessNotification
} from '../../utils/utils.error'
import { executeRecaptcha } from '../../utils/reCapcha/reCapcha'
import { newDefaultLayout } from '../../layout'
import { GetServerSidePropsContext } from 'next'

const PackageBusinessPages = (props: any) => {
  const [partners, setPartners] = useState([])
  const [loading, setLoading] = useState(false)
  const { windowWidth } = useWindowDimensions()
  const fetchPartners = useCallback(async () => {
    try {
      const { data } = await client.partner.getHospitalListByAppIdV6()
      const filteredData = data
        .filter((partner: any) => partner.image && partner.image.trim() !== '')
        .slice(0, 30)
      setPartners(filteredData)
    } catch (error: any) {
      showError(error)
    }
  }, [])

  const onSubmit = async (data: any, form: any) => {
    try {
      setLoading(true)
      await executeRecaptcha((token: any) =>
        handleRegisterEnterprise(token, data, form)
      )
    } catch (err) {
      showError(err)
    }
  }

  const handleRegisterEnterprise = async (
    token: string,
    data: any,
    form: any
  ) => {
    try {
      setLoading(true)
      const response: any =
        await client.packageDoctor.registerCooperationEnterprise({
          ...data,
          captchaResponse: token
        })
      form.resetFields()
      if (!response.data?.message) {
        showSuccessNotification('Đăng ký hợp tác thành công')
      } else {
        showErrorNotification(response.data?.message?.message)
      }
    } catch (error) {
      showErrorNotification(error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    void fetchPartners()
  }, [fetchPartners])

  return (
    <ClinicCooperation
      isLandingPage={props.isLandingPage}
      partners={partners}
      windowWidth={windowWidth}
      onSubmit={onSubmit}
      loading={loading}
    />
  )
}

PackageBusinessPages.getLayout = newDefaultLayout
PackageBusinessPages.showBreadcrumb = false
export default PackageBusinessPages

export async function getServerSideProps(context: GetServerSidePropsContext) {
  return {
    props: { isLandingPage: false }
  }
}
