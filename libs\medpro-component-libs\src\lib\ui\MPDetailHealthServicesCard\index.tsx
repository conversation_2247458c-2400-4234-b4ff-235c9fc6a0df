import { <PERSON><PERSON><PERSON>, MPButton, MPNewListDoctorCard } from '@medpro-libs/libs'
import { Col, Modal, Pagination, Row, Tabs, TabsProps } from 'antd'
import cx from 'classnames'
import { identity, isEmpty, pickBy, size } from 'lodash'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import derectionUp from '../../common/images/derectionUp.svg'
import FillterCard from '../MPFillterDoctorCard/common/FillterCard'
import DrawerProvince from '../MPHeaderHospitalsCard/common/DrawerProvince'
import MPHopitalsPackageCard from '../MPHopitalsPackageCard'
import MPHopitalsTelemedCard from '../MPHopitalsTelemedCard'
import DoctorSkeleton from '../common/selekon/DoctorSkeleton'
import SeleketonHealthService from '../common/selekon/SeleketonHealthService'
import TelemedSekeleton from '../common/selekon/TelemedSekeleton'
import { PackageSkeleton } from '../common/selekon/packageSkeleton'
import { BannerHealthService } from './BannerHealthService'
import CompomentSearch from './CompomentSearch'
import { ContentHealthService } from './ContenHealthService'
import DrawerDoctorSearch from './DrawerDoctorSearch'
import DrawerService from './DrawerService'
import {
  BOOKING_TYPE,
  DOCTOR_TYPE,
  handleDataSearch,
  LIST_TYPE,
  PACKAGE_TYPE
} from './func'
import EmptyList from './img/EmptyList.png'
import { useWindowDimensions } from '../../hooks/useWindowDimesion'
import { propHealthService } from './common/interface'
import PopupArea from '../MPHeaderHospitalsCard/common/PopupArea'
import styles from './styles.module.less'

const pageDefault = 20

export const MPDetailHealthServicesCard = ({
  feature,
  hospitals = [],
  provinces,
  packageData,
  onSelectHospital,
  handleViewMore,
  handleBooking,
  setPagePackageIndex,
  pageIndexPackage,
  onSearch,
  setSearching,
  setCityId,
  cityId,
  totalRows,
  searching,
  listFilter,
  handleFillter,
  appInfo,
  dataTelemed,
  stickyScroll,
  areaData,
  areaFilterData,
  dispatchSaveAreaFilter
}: propHealthService) => {
  const router = useRouter()
  const telemedType = router.query?.telemedType || 'TELEMEDNOW'
  const tabType = router.query?.tabType || ''
  const appFeatureSlug = router.query.app_feature_slug as string
  const behavior = router.query?.behavior as string
  const [typeTab, setTypeTab] = useState(tabType)
  const [filteredList, setFilterList] = useState<any>(hospitals)
  const [keySearch, setKeySearch] = useState<string>('')
  const [pageIndex, setPageIndex] = useState(1)
  const [typeSearch, setTypeSearch] = useState<any>('')
  const [pageSize, setPageSize] = useState(pageDefault)
  const [active, setActive] = useState(false)
  const [loading, setLoading] = useState(false)
  const [dataFilter, setDataFilter] = useState<any>([])
  const [selectedPackage, setSelectedPackage] = useState(packageData || [])
  const [isModalOpen, setIsModalOpen] = useState({
    filterProvince: false,
    filterDoctor: false
  })
  const [isOpenSearch, setIsOpenSearch] = useState({
    typeSeach: 'co-so-y-te',
    open: false
  })
  const [keyTab, setKeyTab] = useState('subjects')
  const [subjects, setSubjects] = useState<any[] | undefined>([])
  const [roles, setRoles] = useState<any[] | undefined>([])
  const [gender, setGender] = useState<any[] | undefined>([])
  const { windowWidth } = useWindowDimensions()

  const isMobile = windowWidth < 577
  const isDesktop = windowWidth > 992

  const [count, setCount] = useState<number>(
    feature?.type === 'booking.package' ? 10 : pageDefault
  )
  const [loadMore, setLoadMore] = useState(false)
  const [dataFilterMobile, setDataFilterMobile] = useState<any>([])
  const [isReset, setIsReset] = useState(false)

  useEffect(() => {
    setTypeTab(tabType)
  }, [tabType])

  useEffect(() => {
    setSelectedPackage(packageData?.[0] || [])
  }, [packageData])
  useEffect(() => {
    const resetInput: any = document.getElementById('myForm')
    setKeySearch('')
    resetInput?.reset()
    setDataFilter([])
    setCityId('')
    setCount(feature?.type === 'booking.package' ? 10 : pageDefault)
  }, [appFeatureSlug, tabType])
  useEffect(() => {
    if (feature?.type !== 'booking.telemed') {
      const newList = handleDataSearch({
        hospitals,
        cityId,
        keySearch,
        typeSearch
      })
      setFilterList(newList)
      window.innerWidth < 577 && setDataFilterMobile(newList.slice(0, count))

      setActive(false)
      setPageSize(pageDefault)
      setPageIndex(1)
    }

    setTimeout(() => {
      setLoadMore(false)
    }, 1000)
  }, [count])

  useEffect(() => {
    if (
      [
        'booking.package',
        'booking.vaccine',
        'booking.covid',
        'booking.doctor'
      ].includes(feature?.type) &&
      !tabType
    ) {
      onSearch(keySearch, [])
      return
    }
    if (feature?.type === 'booking.telemed') {
      onSearch(keySearch, [])
      return
    }

    !isMobile && setLoading(true)

    const newList = handleDataSearch({
      hospitals,
      cityId,
      keySearch,
      typeSearch
    })

    if (
      isEmpty(dataFilter) &&
      isDesktop &&
      ![
        'booking.package',
        'booking.vaccine',
        'booking.covid',
        'booking.doctor',
        'booking.date'
      ].includes(feature?.type)
    ) {
      const dataHopital: any[] = []
      const dataClinic: any[] = []

      newList.forEach((item: any) => {
        if (
          LIST_TYPE[1].newHospitalType.some((i: any) =>
            item?.newHospitalTypes?.includes(i)
          )
        ) {
          dataHopital.push(item)
        } else {
          dataClinic.push(item)
        }
      })

      const listData = Array.from(
        { length: Math.max(dataHopital.length, dataClinic.length) },
        (_, i) => [dataHopital[i], dataClinic[i]].filter(Boolean)
      ).flat()

      setFilterList(listData)
    } else {
      setFilterList(newList)
      window.innerWidth < 577 && setDataFilterMobile(newList.slice(0, count))
    }

    setActive(false)
    setPageSize(pageDefault)
    setPageIndex(1)
    setTimeout(() => {
      setLoading(false)
      setLoadMore(false)
    }, 1000)
  }, [hospitals, cityId, keySearch, typeSearch, tabType])

  const handleSearch = (v: any) => {
    const { hospital, province } = v
    if (!isMobile) {
      if (Array.isArray(province)) {
        setCityId(province)
      } else {
        setCityId(province ? [province] : [])
      }
    }
    setKeySearch(hospital?.normalize('NFC'))
    setLoading(true)
    setSearching(true)
    setCount(feature?.type === 'booking.package' ? 10 : pageDefault)
  }
  const handleSearchCityId = (v: any) => {
    const { province } = v
    if (Array.isArray(province)) {
      setCityId(province)
    } else {
      setCityId(province ? [province] : [])
    }
    setDataFilter(province)
    setLoading(true)
    setSearching(true)
    setCount(feature?.type === 'booking.package' ? 10 : pageDefault)
  }

  useEffect(() => {
    const totalPage = Math.ceil(size(filteredList) / pageSize)
    if (pageIndex >= totalPage) {
      setPageIndex(totalPage > 0 ? totalPage : 1)
    }

    setTimeout(() => {
      setLoading(false)
    }, 500)
  }, [pageSize, pageIndex, filteredList])

  const renderNullList = (text: string) => (
    <div className={styles['emptyList']}>
      <p>{text}</p>
      <Image src={EmptyList} layout='fixed' alt='Empty List' />
    </div>
  )
  const handlePageChange = (page: number) => {
    setPageIndex(page)
    setLoading(true)
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }
  const handleFinishFillter = () => {
    if (isReset) {
      setDataFilter('')
      setIsReset(false)
      setSubjects([])
      setRoles([])
      setGender([])
      handleFillter({
        subjects: [],
        roles: [],
        genders: []
      })
    } else {
      handleFillter({
        subjects: subjects,
        roles: roles,
        genders: gender
      })
      setDataFilter({
        subjects: subjects,
        roles: roles,
        gender: gender
      })
    }

    cancelModal()
  }
  const handleDoctorFillter = (item: any) => {
    if (isReset) {
      setDataFilter('')
      setIsReset(false)
      setSubjects([])
      setRoles([])
      setGender([])
      handleFillter({
        subjects: [],
        roles: [],
        genders: []
      })
    } else {
      setSubjects(item?.subjects)
      setRoles(item?.roles)
      setGender(item?.gender)
      handleFillter({
        subjects: item?.subjects,
        roles: item?.roles,
        genders: item?.gender,
        hospitals: item?.hospitals
      })
      setDataFilter({
        subjects: item?.subjects,
        roles: item?.roles,
        gender: item?.gender,
        hospitals: item?.hospitals
      })
    }

    cancelModal()
  }
  const handleRemoveFilter = (item: any) => {
    const filterFunc = (key: string, value: any) => {
      const updatedValue = value?.filter((i: any) => {
        return i !== (key === 'hospitals' ? item.partnerId : item.id)
      })
      handleFillter({ ...dataFilter, [key]: updatedValue })
      setDataFilter({ ...dataFilter, [key]: updatedValue })
    }
    if (item.type === 'subject') {
      setSubjects((prev) => prev?.filter((i: any) => i !== item.id))
      filterFunc('subjects', subjects)
    } else if (item.type === 'role') {
      setRoles((prev) => prev?.filter((i: any) => i !== item.id))
      filterFunc('roles', roles)
    } else if (item.type === 'gender') {
      setGender((prev) => prev?.filter((i: any) => i !== item.id))
      filterFunc('gender', gender)
    } else if (item.type === 'hospital') {
      filterFunc('hospitals', dataFilter?.hospitals)
    }
  }

  const renderPagination = (
    <div className={styles['pagination']}>
      <Pagination
        hideOnSinglePage={true}
        current={pageIndex}
        onChange={(page) => {
          handlePageChange(page)
        }}
        pageSize={pageDefault}
        total={size(filteredList) <= pageSize ? 1 : size(filteredList)}
        showSizeChanger={false}
      />
      {active ? (
        <MPButton
          onClick={() => {
            setPageSize(pageDefault)
            setActive(!active)
            handlePageChange(1)
          }}
          className={styles['button']}
        >
          Thu gọn
        </MPButton>
      ) : (
        size(filteredList) > pageSize && (
          <MPButton
            onClick={() => {
              setPageSize(size(filteredList))
              setActive(!active)
              handlePageChange(1)
            }}
            className={styles['button']}
          >
            Xem tất cả
          </MPButton>
        )
      )}
    </div>
  )
  const renderBookingType = (type: any) => {
    switch (type) {
      case 'booking.package':
      case 'booking.vaccine':
      case 'booking.covid':
        return !tabType ? (
          searching ? (
            <PackageSkeleton />
          ) : size(packageData) > 0 ? (
            <MPHopitalsPackageCard
              appInfo={appInfo}
              handleBooking={handleBooking}
              handleViewMore={handleViewMore}
              packageData={packageData}
              pageIndexPackage={pageIndexPackage}
              selectedPackage={selectedPackage}
              setSearching={setSearching}
              setSelectedPackage={setSelectedPackage}
              totalRows={totalRows}
              isMobile={isMobile}
              count={count}
              setCount={setCount}
              loadMore={loadMore}
              setLoadMore={setLoadMore}
              derectionUp={derectionUp}
              setPagePackageIndex={setPagePackageIndex}
            />
          ) : size(keySearch) || size(cityId) || size(typeSearch) ? (
            renderNullList('Không tìm thấy dữ liệu cần tìm')
          ) : (
            renderNullList('Danh sách sẽ cập nhật trong thời gian tới')
          )
        ) : loading ? (
          !isMobile ? (
            <PackageSkeleton />
          ) : (
            <SeleketonHealthService />
          )
        ) : size(filteredList) > 0 ? (
          <>
            <Row gutter={[26, 0]}>
              <Col span={24} lg={{ span: 15, order: 1 }}>
                {(isMobile
                  ? dataFilterMobile
                  : filteredList
                  ? filteredList.slice(
                      (pageIndex - 1) * pageSize,
                      (pageIndex - 1) * pageSize + pageSize
                    )
                  : []
                )?.map((item: any) => {
                  return (
                    <HospitalItem
                      key={`detail${item.partnerId}`}
                      onSelect={() => {
                        // props.handleSelectHospital(item)
                      }}
                      data={item}
                      handleBooking={() => onSelectHospital(item)}
                      handleViewMore={handleViewMore}
                    />
                  )
                })}
              </Col>
              <Col
                span={24}
                lg={{ span: 9, order: 2 }}
                className={cx(
                  styles['detailHospital'],
                  stickyScroll ? styles[`detailBottom`] : styles[`detailTop`]
                )}
              >
                <Image
                  src={
                    'https://bo-api.medpro.com.vn/static/images/medpro/web/salebooking.png'
                  }
                  width={368}
                  height={588}
                  alt='Banner tải App Medpro'
                  objectFit='contain'
                />
              </Col>
            </Row>
            <Row className={cx(styles['paginationRow'], styles['pagiPackage'])}>
              {renderPagination}
            </Row>
          </>
        ) : size(keySearch) || size(cityId) || size(typeSearch) ? (
          renderNullList('Không tìm thấy dữ liệu cần tìm')
        ) : (
          renderNullList('Danh sách sẽ cập nhật trong thời gian tới')
        )
      case 'booking.doctor':
        return !tabType ? (
          searching ? (
            <DoctorSkeleton />
          ) : size(packageData) > 0 ? (
            <>
              <Row gutter={[26, 0]}>
                <Col span={24} lg={{ span: 15, order: 1 }}>
                  <MPNewListDoctorCard
                    data={packageData}
                    handleBooking={handleBooking}
                  />
                </Col>
                <Col
                  span={24}
                  lg={{ span: 9, order: 2 }}
                  className={cx(
                    styles['detailHospital'],
                    stickyScroll ? styles[`detailBottom`] : styles[`detailTop`]
                  )}
                >
                  <Image
                    src={
                      'https://bo-api.medpro.com.vn/static/images/medpro/web/salebooking.png'
                    }
                    width={368}
                    height={588}
                    alt='Banner tải App Medpro'
                    objectFit='contain'
                  />
                </Col>
              </Row>
              <Row>
                <Col
                  span={24}
                  lg={{ span: 24, order: 3 }}
                  flex={1}
                  className={styles['paginationRow']}
                >
                  <div className={styles['pagination']}>
                    <Pagination
                      pageSize={10}
                      current={pageIndexPackage}
                      onChange={(p) => {
                        setSearching(true)
                        window.scrollTo({
                          top: 0,
                          behavior: 'smooth'
                        })
                        setPagePackageIndex(p)
                        router.replace(
                          {
                            pathname: router.pathname,
                            query: { ...router.query, page: p }
                          },
                          undefined,
                          { shallow: true }
                        )
                      }}
                      total={totalRows}
                      showSizeChanger={false}
                    />
                  </div>
                  {loadMore ? (
                    <div className={styles['loadingRing']}>
                      <div></div>
                      <div></div>
                      <div></div>
                      <div></div>
                    </div>
                  ) : (
                    totalRows > count &&
                    isMobile && (
                      <div
                        onClick={() => {
                          setLoadMore(true)
                          setCount((prev: any) => prev + 10)
                          setPagePackageIndex((count + 10) / 10)
                        }}
                        className={styles['viewMove']}
                      >
                        <p>Xem tiếp</p>
                        <span>
                          <Image
                            src={derectionUp}
                            width={13}
                            height={13}
                            objectFit='contain'
                            alt='Icon derectionUp'
                            layout='fixed'
                          />
                        </span>
                      </div>
                    )
                  )}
                </Col>
              </Row>
            </>
          ) : size(keySearch) || size(cityId) || size(typeSearch) ? (
            renderNullList('Không tìm thấy dữ liệu cần tìm')
          ) : (
            renderNullList('Danh sách sẽ cập nhật trong thời gian tới')
          )
        ) : loading ? (
          !isMobile ? (
            <PackageSkeleton />
          ) : (
            <SeleketonHealthService />
          )
        ) : size(filteredList) > 0 ? (
          <>
            <Row gutter={[26, 0]}>
              <Col span={24} lg={{ span: 15, order: 1 }}>
                {(isMobile
                  ? dataFilterMobile
                  : filteredList
                  ? filteredList.slice(
                      (pageIndex - 1) * pageSize,
                      (pageIndex - 1) * pageSize + pageSize
                    )
                  : []
                )?.map((item: any) => {
                  return (
                    <HospitalItem
                      key={`detail${item.partnerId}`}
                      onSelect={() => {
                        // props.handleSelectHospital(item)
                      }}
                      data={item}
                      handleBooking={() => onSelectHospital(item)}
                      handleViewMore={handleViewMore}
                    />
                  )
                })}
              </Col>
              <Col
                span={24}
                lg={{ span: 9, order: 2 }}
                className={cx(
                  styles['detailHospital'],
                  stickyScroll ? styles[`detailBottom`] : styles[`detailTop`]
                )}
              >
                <Image
                  src={
                    'https://bo-api.medpro.com.vn/static/images/medpro/web/salebooking.png'
                  }
                  width={368}
                  height={588}
                  alt='Banner tải App Medpro'
                  objectFit='contain'
                />
              </Col>
            </Row>
            <Row className={cx(styles['paginationRow'], styles['pagiPackage'])}>
              {renderPagination}
            </Row>
          </>
        ) : size(keySearch) || size(cityId) || size(typeSearch) ? (
          renderNullList('Không tìm thấy dữ liệu cần tìm')
        ) : (
          renderNullList('Danh sách sẽ cập nhật trong thời gian tới')
        )

      case 'booking.telemed':
        return searching ? (
          <TelemedSekeleton />
        ) : size(dataTelemed) > 0 ? (
          <MPHopitalsTelemedCard
            dataTelemed={dataTelemed}
            totalRows={totalRows}
            pageIndexPackage={pageIndexPackage}
            setPagePackageIndex={setPagePackageIndex}
            pageDefaut={pageDefault}
            isMobile={isMobile}
            count={count}
            setCount={setCount}
            loadMore={loadMore}
            handleViewMore={handleViewMore}
            setLoadMore={setLoadMore}
            derectionUp={derectionUp}
            handleBooking={handleBooking}
          />
        ) : size(keySearch) ? (
          renderNullList('Không tìm thấy bác sĩ cần tìm')
        ) : (
          renderNullList('Danh sách sẽ cập nhật trong thời gian tới')
        )

      default:
        return (
          <>
            {loading ? (
              <SeleketonHealthService />
            ) : (
              <Row gutter={[20, 20]}>
                {size(filteredList) < 1
                  ? renderNullList('Danh sách sẽ cập nhật trong thời gian tới')
                  : (isMobile
                      ? dataFilterMobile
                      : filteredList
                      ? filteredList.slice(
                          (pageIndex - 1) * pageSize,
                          (pageIndex - 1) * pageSize + pageSize
                        )
                      : []
                    ).map((item: any, index: number) => (
                      <Col
                        span={12}
                        xs={24}
                        xl={12}
                        lg={12}
                        md={24}
                        key={index}
                      >
                        <ContentHealthService
                          onSelectHospital={() => onSelectHospital(item)}
                          data={item}
                          handleViewMore={handleViewMore}
                          bookingButtonText={
                            feature?.screenOptions?.bookingButtonText
                          }
                          bookingDoctor={type === 'booking.doctor'}
                        />
                      </Col>
                    ))}
                <Col
                  span={24}
                  lg={{ span: 24, order: 3 }}
                  className={styles['paginationRow']}
                >
                  {renderPagination}
                  {loadMore ? (
                    <div className={styles['loadingRing']}>
                      <div></div>
                      <div></div>
                      <div></div>
                      <div></div>
                    </div>
                  ) : (
                    size(filteredList) > count &&
                    isMobile && (
                      <div
                        onClick={() => {
                          setLoadMore(true)
                          setCount((prev: any) => prev + pageDefault)
                        }}
                        className={styles['viewMove']}
                      >
                        <p>Xem tiếp</p>
                        <span>
                          <Image
                            src={derectionUp}
                            width={13}
                            height={13}
                            objectFit='contain'
                            alt='Icon Derection Up'
                            layout='fixed'
                          />
                        </span>
                      </div>
                    )
                  )}
                </Col>
              </Row>
            )}
          </>
        )
    }
  }
  const cancelModal = () => {
    setIsModalOpen({
      filterProvince: false,
      filterDoctor: false
    })
    setKeyTab('subjects')
  }

  const items: TabsProps['items'] = [
    {
      key: 'subjects',
      label: `Chuyên khoa`,
      children: (
        <FillterCard
          selects={dataFilter?.subjects}
          data={listFilter?.subjects}
          maxSelect={3}
          name={'subjects'}
          setSubjects={setSubjects}
          setRoles={setRoles}
          isReset={isReset}
          setIsReset={setIsReset}
          setGender={setGender}
          setDataFilter={setDataFilter}
          handleFillter={handleFinishFillter}
          listFilter={listFilter}
          keyTab={keyTab}
        />
      )
    },
    {
      key: 'roles',
      label: `Học hàm/ học vị`,
      children: (
        <FillterCard
          selects={dataFilter?.roles}
          data={listFilter?.roles}
          maxSelect={2}
          name={'roles'}
          setIsReset={setIsReset}
          setSubjects={setSubjects}
          setRoles={setRoles}
          isReset={isReset}
          setGender={setGender}
          setDataFilter={setDataFilter}
          handleFillter={handleFinishFillter}
          listFilter={listFilter}
          keyTab={keyTab}
        />
      )
    },
    {
      key: 'genders',
      label: `Giới tính`,
      children: (
        <FillterCard
          selects={dataFilter?.gender}
          data={listFilter?.gender}
          maxSelect={1}
          name={'genders'}
          setIsReset={setIsReset}
          setSubjects={setSubjects}
          setRoles={setRoles}
          isReset={isReset}
          setGender={setGender}
          setDataFilter={setDataFilter}
          handleFillter={handleFinishFillter}
          listFilter={listFilter}
          keyTab={keyTab}
        />
      )
    }
  ]
  const renderProvince = () => {
    if (!['booking.package', 'booking.telemed'].includes(feature?.type)) {
      return provinces?.filter((province: any) =>
        hospitals.some((item: any) => item.city_id === province.id)
      )
    }
    return provinces
  }
  return (
    <div className={styles['main']}>
      <div className={styles['header']}>
        <BannerHealthService feature={feature} behavior={behavior} />
      </div>
      <div
        className={cx(
          styles['body'],
          feature?.type === 'booking.package' && styles['bodyPackage']
        )}
      >
        <CompomentSearch
          handleSearch={handleSearch}
          provinces={renderProvince()}
          setIsModalOpen={setIsModalOpen}
          setIsOpenSearch={setIsOpenSearch}
          feature={['booking.telemed'].includes(feature?.type)}
          featureType={feature?.type}
          filterData={dataFilter}
          listFilter={listFilter}
          tabType={tabType}
          isMobile={isMobile}
          handleRemoveFilter={handleRemoveFilter}
          areaData={areaData}
          areaFilterData={areaFilterData}
          dispatchSaveAreaFilter={dispatchSaveAreaFilter}
        />
        {![
          'booking.package',
          'booking.telemed',
          'booking.vaccine',
          'booking.covid',
          'booking.doctor'
        ].includes(feature?.type) && (
          <ul className={cx(styles['tag'])}>
            {LIST_TYPE.map((t: any) => (
              <li key={t.key}>
                <MPButton
                  type='primary'
                  className={cx({
                    [styles['tagItem']]: true,
                    [styles['active']]: typeSearch === t.key
                  })}
                  onClick={() => {
                    setTypeSearch((p: any) => (p === t.key ? '' : t.key))
                    setCount(pageDefault)
                    setPagePackageIndex(1)
                  }}
                >
                  <h2>{t.title}</h2>
                </MPButton>
              </li>
            ))}
          </ul>
        )}
        {[
          'booking.package',
          'booking.vaccine',
          'booking.covid',
          'booking.doctor'
        ].includes(feature?.type) && (
          <div className={cx(styles['tag'])}>
            {(feature?.type === 'booking.doctor'
              ? DOCTOR_TYPE
              : PACKAGE_TYPE
            ).map((t: any) => (
              <MPButton
                key={t.key}
                type='primary'
                className={cx({
                  [styles['typeItem']]: true,
                  [styles['active']]: tabType === t.key
                })}
                onClick={() => {
                  router.replace(
                    {
                      pathname: router.pathname,
                      query: pickBy(
                        { ...router.query, tabType: t.key, page: null },
                        identity
                      )
                    },
                    undefined,
                    { shallow: true }
                  )
                  if (!tabType) {
                    setSearching(false)
                    setLoading(true)
                  } else {
                    setSearching(true)
                    setLoading(false)
                  }
                  setPagePackageIndex(1)
                  setCount(pageDefault)
                  if (!tabType) {
                    setSearching(false)
                    setLoading(true)
                  } else {
                    setSearching(true)
                    setLoading(false)
                  }
                  setDataFilter({})
                }}
              >
                <h2>{t.title}</h2>
              </MPButton>
            ))}
          </div>
        )}
        {feature?.type === 'booking.telemed' && (
          <div className={cx(styles['tag'])}>
            {BOOKING_TYPE.map((t: any) => (
              <MPButton
                key={t.key}
                type='primary'
                className={cx({
                  [styles['typeItem']]: true,
                  [styles['active']]: telemedType === t.key
                })}
                onClick={() => {
                  router.replace(
                    {
                      pathname: router.pathname,
                      query: { ...router.query, telemedType: t.key }
                    },
                    undefined,
                    { shallow: true }
                  )
                  setPagePackageIndex(1)
                  setCount(pageDefault)
                  setDataFilter({})
                  setSubjects([])
                  setRoles([])
                  setGender([])
                }}
              >
                <h2>{t.title}</h2>
              </MPButton>
            ))}
          </div>
        )}

        {renderBookingType(feature?.type)}
      </div>
      {isModalOpen?.filterDoctor && window.innerWidth > 576 && (
        <Modal
          closeIcon={null}
          closable={false}
          title={null}
          open={isModalOpen?.filterDoctor && window.innerWidth > 576}
          onCancel={() => {
            setIsReset(false)
            cancelModal()
          }}
          className={styles['modalForm']}
          footer={null}
          width={684}
        >
          <Tabs
            className={styles['tab']}
            renderTabBar={(props, DefaultTabBar) => {
              return (
                <div>
                  <DefaultTabBar {...props}>
                    {(node) => (
                      <div
                        className={styles['tabTitle']}
                        onClick={() => {
                          setKeyTab(
                            node.key ? node.key?.toString() : 'subjects'
                          )
                        }}
                      >
                        {node}
                      </div>
                    )}
                  </DefaultTabBar>
                </div>
              )
            }}
            centered
            activeKey={keyTab}
            items={items}
          />
        </Modal>
      )}
      {isModalOpen?.filterDoctor && window.innerWidth < 576 && (
        <DrawerService
          open={isModalOpen?.filterDoctor}
          setOpen={setIsModalOpen}
          subjects={subjects}
          setSubjects={setSubjects}
          roles={roles}
          setRoles={setRoles}
          gender={gender}
          setIsReset={setIsReset}
          setGender={setGender}
          dataFilter={dataFilter}
          listFilter={listFilter}
          handleFillterTabs={handleFinishFillter}
        />
      )}
      {isOpenSearch?.open && window.innerWidth < 576 && (
        <DrawerDoctorSearch
          isOpenSearch={isOpenSearch}
          setOpen={setIsOpenSearch}
          setIsReset={setIsReset}
          dataFilter={dataFilter}
          listFilter={listFilter}
          handleFillterTabs={handleDoctorFillter}
        />
      )}
      {isModalOpen?.filterProvince &&
        ['booking.date'].includes(feature?.type) && (
          <PopupArea
            areaData={areaData}
            areaFilterData={areaFilterData}
            setIsModalOpen={setIsModalOpen}
            isModalOpen={isModalOpen}
            handleSearchCityId={handleSearchCityId}
            dispatchSaveAreaFilter={dispatchSaveAreaFilter}
          />
        )}
      {isModalOpen?.filterProvince &&
        !['booking.date'].includes(feature?.type) && (
          <DrawerProvince
            open={isModalOpen}
            setOpen={setIsModalOpen}
            dataFilter={dataFilter}
            setDataFilter={setDataFilter}
            listFilter={renderProvince()}
            handleFillterTabs={handleSearchCityId}
          />
        )}
    </div>
  )
}
