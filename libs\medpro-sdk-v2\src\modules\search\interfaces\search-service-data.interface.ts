export interface ServiceSearchQuery {
  search_key?: string
  tags?: string
  category?: string
  limit?: number
  offset?: number
  city_id?: string
  treeIds?: string
  partnerId?: string
  excludes?: string
  subject_ids?: string
  role_ids?: string
  latitude?: string
  longitude?: string
}

export interface SearchDetailQuery {
  slug?: string
  treeId?: string
  serviceId?: string
  doctorId?: string
}
