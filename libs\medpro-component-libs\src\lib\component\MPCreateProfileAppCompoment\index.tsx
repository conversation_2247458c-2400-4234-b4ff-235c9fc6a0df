import React from 'react'

export interface ItemRender_patient {
  handleSubmit: (values: any) => void
  handleChangeAddress: (type: string, id: string) => void
}
export interface Props {
  onSubmit: (values: any) => void
  onChangeAddress: (type: string, id: string) => void
  render: ({
    handleSubmit,
    handleChangeAddress
  }: ItemRender_patient) => React.ReactNode | JSX.Element
}

export const MPCreateProfileAppCompoment = ({
  onSubmit,
  onChangeAddress,
  render
}: Props) => {
  const handleSubmit = (values: any) => {
    onSubmit(values)
  }
  const handleChangeAddress = (type: string, id: string) => {
    onChangeAddress(type, id)
  }
  return <div>{render({ handleSubmit, handleChangeAddress })}</div>
}
