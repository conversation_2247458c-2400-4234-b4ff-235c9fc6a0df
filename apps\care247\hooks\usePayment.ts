import { useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  paymentActions,
  selectPaymentInfo,
  selectPaymentLoading,
  selectPaymentError
} from '../store/slices/paymentSlice'

export const usePayment = () => {
  const dispatch = useDispatch()

  // Selectors
  const paymentInfo = useSelector(selectPaymentInfo)
  const loading = useSelector(selectPaymentLoading)
  const error = useSelector(selectPaymentError)

  // Actions
  const getPaymentInfo = useCallback((params: {
    transactionId: string
    partnerId: string
  }) => {
    dispatch(paymentActions.getPaymentInfo(params))
  }, [dispatch])

  const clearError = useCallback(() => {
    dispatch(paymentActions.clearPaymentError())
  }, [dispatch])

  const resetState = useCallback(() => {
    dispatch(paymentActions.resetPaymentState())
  }, [dispatch])

  return {
    // State
    paymentInfo: paymentInfo?.payment,
    loading,
    error,

    // Actions
    getPaymentInfo,
    clearError,
    resetState
  }
}

export default usePayment
