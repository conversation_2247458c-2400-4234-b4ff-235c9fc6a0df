import { useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { 
  getPaymentInfoRequest,
  setSuccessModalOpen,
  clearPaymentError,
  resetPaymentState,
  selectPaymentInfo,
  selectPaymentLoading,
  selectPaymentError,
  selectSuccessModalOpen,
  PaymentInfo
} from '../store/slices/paymentSlice'

export const usePayment = () => {
  const dispatch = useDispatch()
  
  // Selectors
  const paymentInfo = useSelector(selectPaymentInfo)
  const loading = useSelector(selectPaymentLoading)
  const error = useSelector(selectPaymentError)
  const successModalOpen = useSelector(selectSuccessModalOpen)
  
  // Actions
  const getPaymentInfo = useCallback((params: {
    transactionId: string
    partnerId: string
    onSuccess?: (payment: PaymentInfo) => void
    onError?: (error: string) => void
  }) => {
    dispatch(getPaymentInfoRequest(params))
  }, [dispatch])
  
  const setSuccessModal = useCallback((open: boolean) => {
    dispatch(setSuccessModalOpen(open))
  }, [dispatch])
  
  const clearError = useCallback(() => {
    dispatch(clearPaymentError())
  }, [dispatch])
  
  const resetState = useCallback(() => {
    dispatch(resetPaymentState())
  }, [dispatch])
  
  return {
    // State
    paymentInfo,
    loading,
    error,
    successModalOpen,
    
    // Actions
    getPaymentInfo,
    setSuccessModal,
    clearError,
    resetState
  }
}

export default usePayment
