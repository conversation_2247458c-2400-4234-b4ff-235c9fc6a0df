import { useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  paymentActions,
  selectPaymentInfo,
  selectPaymentLoading,
  selectPaymentError,
  selectTranslateData,
  selectTranslateLoading,
  selectTranslateError
} from '../store/slices/paymentSlice'

export const usePayment = () => {
  const dispatch = useDispatch()

  // Selectors
  const paymentInfo = useSelector(selectPaymentInfo)
  const loading = useSelector(selectPaymentLoading)
  const error = useSelector(selectPaymentError)
  const translateData = useSelector(selectTranslateData)
  const translateLoading = useSelector(selectTranslateLoading)
  const translateError = useSelector(selectTranslateError)

  // Actions
  const getPaymentInfo = useCallback((params: {
    transactionId: string
    partnerId: string
  }) => {
    dispatch(paymentActions.getPaymentInfo(params))
  }, [dispatch])

  const getTranslate = useCallback((params: {
    language: string
  }) => {
    dispatch(paymentActions.getTranslate(params))
  }, [dispatch])

  const clearError = useCallback(() => {
    dispatch(paymentActions.clearPaymentError())
  }, [dispatch])

  const resetState = useCallback(() => {
    dispatch(paymentActions.resetPaymentState())
  }, [dispatch])

  return {
    // State
    paymentInfo: paymentInfo?.payment,
    loading,
    error,
    translateData,
    translateLoading,
    translateError,

    // Actions
    getPaymentInfo,
    getTranslate,
    clearError,
    resetState
  }
}

export default usePayment
