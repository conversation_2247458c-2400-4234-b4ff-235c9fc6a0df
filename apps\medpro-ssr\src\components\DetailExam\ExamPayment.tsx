import { BOOKING_STATUS, <PERSON><PERSON>utton, MPContainer } from '@medpro-libs/libs'
import { Button, Carousel, CarouselProps, Col, Row, TabsProps } from 'antd'
import BookingBillPage from '../../../src/components/pages/BookingBillPage'
import { first, size } from 'lodash'
import { AiOutlineClose, AiOutlineInfoCircle } from 'react-icons/ai'
import { MdOutlineStickyNote2 } from 'react-icons/md'
import BookingBillCarePage from '../../../src/components/pages/BookingBillCarePage'
import BookingNoPaymentCare from '../../../src/components/pages/BookingNoPaymentCare'
import CardDownload from '../../../src/components/shapes/CardDownload'
import styles from './styles.module.less'
import cx from 'classnames'
import { RiArrowLeftSLine, RiArrowRightSLine } from 'react-icons/ri'
import { Gi<PERSON>lainCircle } from 'react-icons/gi'
import { IoClose } from 'react-icons/io5'
import Image from 'next/image'
import Link from 'next/link'
export const itemsBooking = ({
  isMobile,
  router,
  data,
  refundNote,
  care247Available,
  isCs,
  toggleCancelModel,
  repayment,
  shareToPay,
  transactionId,
  onReserveMedproCare,
  onReserveMedproCareAddon,
  onOpenModalPaymentCare247,
  getShareBookingInfo,
  toggleShareBooking
}: ITEM_BOOKING) => {
  const SampleNextArrow = (props_next: any) => {
    const { className, style, onClick, currentSlide } = props_next
    if (currentSlide === size(data) - 1) {
      return ''
    }
    return (
      <div
        className={cx(className, styles['btnNext'])}
        style={{ ...style }}
        onClick={onClick}
      >
        <RiArrowRightSLine
          size={20}
          color='#003553'
          aria-labelledby='Next Icon'
        />
      </div>
    ) as any
  }

  const SamplePrevArrow = (props_prev: any) => {
    const { className, style, onClick, currentSlide } = props_prev
    if (currentSlide === 0) {
      return ''
    }
    return (
      <div
        className={cx(className, styles['btnPrev'])}
        style={{ ...style }}
        onClick={onClick}
      >
        <RiArrowLeftSLine
          size={20}
          color='#003553'
          aria-labelledby='Previous Icon'
        />
      </div>
    ) as any
  }
  const settings: CarouselProps = {
    slidesToShow: 1,
    slidesToScroll: 1,
    nextArrow: <SampleNextArrow />,
    prevArrow: <SamplePrevArrow />,
    arrows: true,
    swipe: false,
    fade: true,
    responsive: [
      {
        breakpoint: 576,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1
        }
      }
    ]
  }

  const renderPaymentInfo = (data: any[]) => {
    const { informHtml, layout, banner } = (first(data) as ITEM_DATA) || {}
    switch (layout) {
      case 'INFORM':
        return (
          <div className={styles.informHtml}>
            <p
              className={styles.informBooking}
              dangerouslySetInnerHTML={{ __html: informHtml }}
            />
            {banner.status && (
              <Link href={banner.url}>
                <a
                  className={styles['bannerInform']}
                  target={banner.cta.targetWeb}
                >
                  <Image
                    src={banner.imageUrl}
                    alt='inform'
                    width={600}
                    height={300}
                    layout='responsive'
                    objectFit='contain'
                    objectPosition={'center top'}
                    className={styles['imgInform']}
                  />
                </a>
              </Link>
            )}
          </div>
        )

      default:
        return (
          <div className={styles.paymentCard}>
            {data?.map((bill, i) => {
              return (
                <div key={bill?.bookingInfo?._id}>
                  <div>
                    {!isCs && (
                      <CardDownload status={bill?.bookingInfo?.status} />
                    )}
                    <BookingBillPage
                      key={i}
                      repayment={repayment}
                      shareToPay={shareToPay}
                      // handleComplainBooking={handleComplaintBooking}
                      isMobile={isMobile}
                      // dataComplaint={dataComplaint}
                      bookingBill={{
                        id: bill?.bookingInfo?.transactionId || transactionId,
                        ...bill
                      }}
                      isCs={isCs}
                      getShareBookingInfo={getShareBookingInfo}
                      toggleShareBooking={toggleShareBooking}
                    />
                  </div>
                </div>
              )
            })}
          </div>
        )
    }
  }

  const CancelBookingSection = ({
    refundNote,
    toggleCancelModel
  }: {
    refundNote: any
    toggleCancelModel: (e: any) => void
  }) => (
    <>
      <div className={styles['cancelBooking']}>
        <MPButton
          className={styles['btn']}
          onClick={toggleCancelModel}
          icon={<IoClose size={18} color='#24313d' />}
        >
          Hủy phiếu
        </MPButton>
      </div>
      {refundNote && (
        <div style={{ ...refundNote?.box }} className={styles['refundNote']}>
          <AiOutlineInfoCircle size={16} color={'#f5222d'} />
          <div
            style={{ ...refundNote?.text }}
            dangerouslySetInnerHTML={{ __html: refundNote?.title }}
          />
        </div>
      )}
    </>
  )

  return [
    {
      key: 'NormalBooking',
      label: 'Phiếu khám',
      children: (
        <Row className={styles.paymentInfo}>
          <Col span={24}>
            {!isMobile && (
              <MPContainer className={styles['button_extend']}>
                <Button
                  className={styles['listBooking']}
                  onClick={() => router.push('/user?key=bills')}
                >
                  <MdOutlineStickyNote2 size={18} color='#00b5f1' />
                  Danh sách phiếu khám
                </Button>
              </MPContainer>
            )}
            {size(data) > 1 && isMobile ? (
              <>
                {data?.[0]?.bookingInfo?.status === 0 && (
                  <div className={styles['bufferZone']} />
                )}
                <Carousel {...settings} className={styles['carousel']}>
                  {data?.map((bill, i) => {
                    return (
                      <div key={bill?.bookingInfo?._id}>
                        <p className={cx(styles['TagExam'])}>
                          Phiếu khám {i + 1}
                        </p>
                        <div>
                          <CardDownload status={bill?.bookingInfo?.status} />
                          <BookingBillPage
                            key={i}
                            repayment={repayment}
                            shareToPay={shareToPay}
                            bookingBill={{
                              id:
                                bill?.bookingInfo?.transactionId ||
                                transactionId,
                              ...bill
                            }}
                            isCs={isCs}
                            getShareBookingInfo={getShareBookingInfo}
                            toggleShareBooking={toggleShareBooking}
                          />
                        </div>
                      </div>
                    )
                  })}
                </Carousel>
              </>
            ) : (
              renderPaymentInfo(data)
            )}
            {/* Button && Note khi hủy phiếu */}
            {first(data)?.bookingInfo?.status ===
              BOOKING_STATUS.DA_THANH_TOAN &&
              first(data)?.layout !== 'INFORM' && (
                <CancelBookingSection
                  refundNote={refundNote}
                  toggleCancelModel={toggleCancelModel}
                />
              )}
          </Col>
        </Row>
      )
    },
    {
      key: 'Care247',
      label: (
        <div className={styles.tabCare247}>
          <label>Dịch vụ Care247 </label>{' '}
          {care247Available?.status === 0 && (
            <GiPlainCircle color='red' size={8} />
          )}
        </div>
      ),
      children: [
        BOOKING_STATUS.CHUA_THANH_TOAN,
        BOOKING_STATUS.DA_HUY
      ].includes(care247Available?.status) ? (
        <div className={styles.paymentCard}>
          <BookingNoPaymentCare
            isMobile={isMobile}
            care247Available={care247Available}
            bookingInfo={{
              ...first(data)?.bookingInfo
            }}
            handleReserve={onReserveMedproCare}
            handleOpenModalPaymentCare247={onOpenModalPaymentCare247}
          />
        </div>
      ) : (
        <div className={styles.paymentCard}>
          <BookingBillCarePage
            bookingInfo={first(data)?.bookingInfo}
            isMobile={isMobile}
            care247Available={care247Available}
            handleReserveAddon={onReserveMedproCareAddon}
          />
        </div>
      )
    }
  ] as TabsProps['items']
}

interface Care247 {
  content?: string
  footer?: string
  header?: string
  popupPayPending?: any
  popupPaySuccess?: any
  status?: number
}

interface ReserveMedproCare {
  transactionId: string
  redirectUrl: string
  groupId?: string
  medproCareServiceIds?: string[]
}

interface ReserveMedproCareAddon {
  transactionId: string
  redirectUrl: string
  groupId?: number
  medproCareServiceId?: string
  addonId: string
}
interface ITEM_BOOKING {
  isMobile: boolean
  router: any
  data: any[]
  refundNote: any
  care247Available: Care247
  isCs?: boolean
  toggleCancelModel: (e: any) => void
  repayment: (e: any) => void
  shareToPay: () => void
  transactionId: string
  onReserveMedproCare: (values: ReserveMedproCare) => Promise<void>
  onReserveMedproCareAddon: (values: ReserveMedproCareAddon) => Promise<void>
  onOpenModalPaymentCare247: () => void
  getShareBookingInfo: (_id: string) => void
  toggleShareBooking: any
}

interface ITEM_DATA {
  informHtml?: string
  layout?: string
  banner?: any
}
