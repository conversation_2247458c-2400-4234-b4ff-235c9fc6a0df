.paymentMethodsContainer {
  margin-bottom: 24px;

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
  }

  .methodsList {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 20px;

    .methodCard {
      background: white;
      border: 2px solid #f0f0f0;
      border-radius: 12px;
      padding: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 12px;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
      }

      &.active {
        border-color: #1890ff;
        background: #f6ffed;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);

        .methodIcon {
          background: #1890ff;
          color: white;
        }
      }

      .methodIcon {
        width: 48px;
        height: 48px;
        background: #f8f9fa;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        transition: all 0.3s ease;
      }

      .methodInfo {
        flex: 1;

        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin: 0 0 4px 0;
        }

        p {
          font-size: 12px;
          color: #666;
          margin: 0;
          line-height: 1.4;
        }
      }

      .methodSelector {
        .radioButton {
          width: 20px;
          height: 20px;
          border: 2px solid #d9d9d9;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          .radioSelected {
            width: 10px;
            height: 10px;
            background: #1890ff;
            border-radius: 50%;
          }
        }
      }

      &.active .methodSelector .radioButton {
        border-color: #1890ff;
      }
    }
  }

  .selectedMethodInfo {
    .infoBox {
      background: #e6f7ff;
      border: 1px solid #91d5ff;
      border-radius: 8px;
      padding: 16px;
      text-align: center;

      h4 {
        font-size: 14px;
        font-weight: 600;
        color: #0050b3;
        margin: 0 0 8px 0;
      }

      p {
        font-size: 16px;
        font-weight: 600;
        color: #1890ff;
        margin: 0;
      }
    }
  }

  @media (max-width: 768px) {
    .methodsList {
      grid-template-columns: 1fr;

      .methodCard {
        padding: 12px;

        .methodIcon {
          width: 40px;
          height: 40px;
          font-size: 20px;
        }

        .methodInfo {
          h4 {
            font-size: 14px;
          }

          p {
            font-size: 11px;
          }
        }
      }
    }
  }
}
