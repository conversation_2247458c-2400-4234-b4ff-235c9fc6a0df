import { createAction, createSlice } from '@reduxjs/toolkit'
import { LocationState } from './interface'

const initialState: LocationState = {
  data: null
}

const reHydrate = createAction<any>('persist/REHYDRATE')
export const locationSlice = createSlice({
  name: 'location',
  initialState,
  reducers: {
    setUserLocation: (state, action: any) => {
      state.data = action.payload
    },
    getUserLocation: (state, action: any) => {
      // run saga
    },
    clear: (state) => {
      state.data = null
    },
  }
})

// Action creators are generated for each case reducer function
export const { setUserLocation } = locationSlice.actions

export const locationActions = locationSlice.actions

export default locationSlice.reducer
