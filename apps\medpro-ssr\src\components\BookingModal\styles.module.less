.modalCancelBooking {
  :global {
    .ant-modal-content {
      border-radius: 20px;
    }
    .ant-modal-body {
      padding: 24px 60px;
      @media screen and (max-width: 768px) {
        padding: 24px 24px;
      }
    }
  }
}

.drawerCancelBooking {
  // position: relative;
  :global {
    .ant-drawer-content-wrapper {
      border-radius: 16px 16px 0 0;
    }
    .ant-drawer-title {
      margin-right: 0;
    }
  }
}

.Wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}

// .animation {
//   // animation: left 0.5s ease forwards;
// }

// .animation {
//   animation: leftPC 0.5s ease forwards;
// }

.Header {
  text-align: center;
  margin-bottom: 24px;
  @media screen and (max-width: 768px) {
    margin-bottom: 8px;
  }
  &-title {
    text-align: center;
    font-weight: 600;
    font-size: 18px;
    letter-spacing: 0px;
    text-align: center;
    vertical-align: bottom;

    color: #2c3642;
    margin-bottom: 32px;
    letter-spacing: 1px;
    @media screen and (max-width: 768px) {
      display: none;
    }
  }

  .closeButton {
    position: absolute;
    top: -10px;
    right: 0px;
    background: none;
    border: none;
    font-size: 40px;
    color: #2c3642;
    cursor: pointer;
    z-index: 10;
    line-height: 1;
    @media screen and (max-width: 768px) {
      top: 4px;
      right: 16px;
    }
  }
}

.stepsCancel {
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 150px !important;
  margin: 0 auto 12px;
  @media screen and (max-width: 768px) {
    max-width: max-content !important;
    flex-direction: row !important;

    margin-bottom: 0;
  }
  :global {
    .ant-steps-item-icon {
      font-size: 13px;
      font-weight: 700;
    }
    .ant-steps-item-tail {
      transform: rotate(-90deg) !important;
      top: -12px !important;
      left: 24px !important;
    }
    .ant-steps-item-process
      > .ant-steps-item-container
      > .ant-steps-item-tail::after {
      background-color: #d9d9d9;
    }
    .ant-steps-item-wait {
      .ant-steps-item-icon {
        color: #8080808c;
        background-color: #d9d9d9;
        border: none;
      }
    }
  }
}

.HeaderCancel {
  margin-bottom: 32px;
}

.Body {
  @media screen and (max-width: 768px) {
    height: calc(80vh - 90px);
    max-height: calc(80vh - 90px);
    overflow-y: scroll;
  }
  padding-bottom: 30px;
  &-content {
    text-align: center;
    margin-bottom: 24px;
    font-family: Inter;
    font-weight: 400;
    font-style: italic;
    font-size: 16px;
    line-height: 100%;

    color: #636363;
  }

  &-reason {
    .radioGroup {
      display: flex;
      flex-direction: column;
      gap: 21px;
      span:nth-child(2) {
        font-weight: 400;
        font-size: 16px;
        line-height: 100%;
        letter-spacing: 0%;
        color: #24313d;
      }

      font-weight: 400;
      font-size: 16px;
      line-height: 100%;
      letter-spacing: 0%;
      :global {
        .ant-checkbox-checked .ant-checkbox-inner {
          background-color: #00b5f1 !important;
        }
        .ant-checkbox-inner {
          width: 20px !important;
          height: 20px !important;
          &::after {
            left: 28%;
          }
        }
        .ant-checkbox-input {
          width: 20px !important;
          height: 20px !important;
        }
        .ant-checkbox {
          .ant-checkbox-checked {
            width: 20px !important;
            height: 20px !important;
          }
        }
      }
    }
  }

  &-refundInfo {
    &-content {
      text-align: center;
      &-title {
        font-weight: 500;
        font-size: 17px;
        line-height: 22px;
        color: #12263f;
        margin-bottom: 8px;
        strong {
          font-family: Inter;
          font-weight: 700;
          font-size: 28px;
          line-height: 28px;
          color: #24313d;
        }
      }

      &-desc {
        color: #666;
        margin-bottom: 24px;
      }
    }

    &-info {
      background-color: #f5f5f5;
      padding: 16px;
      border-radius: 12px;
      margin-bottom: 16px;
      label {
        font-weight: 590;
        font-size: 17px;
        line-height: 22px;
        letter-spacing: -0.43px;
        color: #12263f;
      }
      &-row {
        margin-top: 8px;

        .row-item {
          display: flex;
          // flex-direction: column;
          gap: 8px;
          @media screen and (max-width: 768px) {
            flex-direction: row !important;
          }

          span {
            font-weight: 400;
            font-size: 16px;
            line-height: 21px;
            letter-spacing: -0.31px;
            color: #757575;
          }
        }
      }
    }

    .formRefundInfo {
      margin-bottom: 16px;
    }

    .Content-Note {
      h3 {
        font-weight: 700;
        font-size: 16px;
        line-height: 21px;
      }
      p {
        font-weight: 400;
        font-size: 16px;
        line-height: 21px;
      }

      color: #000000;
    }
  }
  @media screen and (max-width: 768px) {
    // margin-bottom: 90px;
    padding-bottom: 90px;
  }
}

.Footer {
  @media screen and (max-width: 768px) {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 1000;
    background: #fff;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
  }
  &-btn {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    button {
      font-family: Inter;
      font-weight: 500;
      font-size: 15px;
      line-height: 120%;
      letter-spacing: 0px;
      text-align: center;
      vertical-align: bottom;
      border-radius: 12px;
      padding: 12px 24px;
      height: 50px;
    }

    .btnCancel {
      flex: 1;
      border: 1px solid #1da1f2;
      color: #1da1f2;
    }

    .btnOke {
      flex: 1;
      font-weight: 600;
    }
  }

  &-hotline {
    text-align: center;
    color: #666;

    a {
      color: #1890ff;
      text-decoration: none;
    }
  }
}

.ModuleNotification {
  :global {
    .ant-modal-content {
      border-radius: 8px;
    }
  }

  .ModalHeaderNotifi {
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
  }

  .waitingMessage {
    text-align: center;
    margin-bottom: 16px;
  }

  .waitingTime {
    text-align: center;
    margin-bottom: 16px;

    span {
      color: #1890ff;
      font-weight: 600;
    }
  }

  .btnNotifiContainer {
    display: flex;
    gap: 16px;
  }

  .btnNotifiTel {
    flex: 1;

    a {
      color: #fff;
      text-decoration: none;
    }
  }

  .btnNotifi {
    flex: 1;
  }
}

.modalComplain {
  :global {
    .ant-modal-content {
      border-radius: 8px;
    }
  }

  .titleComplain {
    text-align: center;

    p {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    span {
      color: #666;
    }
  }

  .resultComplain {
    padding: 24px 0;
  }

  .iconComplain {
    display: flex;
    justify-content: center;
    margin-bottom: 16px;
  }
}

.formRefundInfo {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;

  .submit {
    background: #1da1f2;
    color: #fff;
    border: none;
    border-radius: 5px;
    padding: 4 15px;
    min-width: 152px;
    min-height: 32px;
    margin: 10px;
    font-weight: 500;
    transition: all 0.3s;
    &:disabled {
      background-color: #cccccc;
    }
    &:hover {
      background-color: #007bff;
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
    }
  }
}
.formInputGroup {
  margin-bottom: 0 !important;
  @media only screen and (max-width: 768px) {
    margin-bottom: 24px !important;
  }
  .inputGroup {
    display: flex !important;
    justify-content: space-between;
  }
  .selectItem {
    width: 30%;
    margin-bottom: 0;
  }
  .requireInput {
    font-size: 100%;
    top: -0.2em;
    left: 3px;
    color: red;
  }
}
.selectItem {
  @media screen and (min-width: 768px) {
    width: 498px;
  }
  input {
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
}
.selectItem,
.formInputGroup,
.formInputItem {
  label {
    color: var(--primary-body-text, #003553) !important;
    font-family: Inter;
    font-weight: 500;
    font-size: 14px !important;
    line-height: 16px !important;
  }
  .requireInput {
    font-size: 100%;
    top: -0.2em;
    left: 3px;
    color: red;
  }
  :global {
    .ant-form-item-explain-error {
      font-size: 0.7rem;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-radius: 10px;
      background: #fff;
      min-height: 50px;
    }
    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none !important;
    }
    .ant-select-selection-placeholder {
      margin-top: 4px;
    }
    .ant-select-selection-item {
      display: flex;
      align-items: center;
      // margin-top: 4px;
      .ant-image {
        height: 35px !important;
      }
    }
    .ant-select-selection-placeholder {
      display: flex !important;
      align-items: center !important;
    }
    .ant-select-single:not(.ant-select-customize-input)
      .ant-select-selector
      .ant-select-selection-search-input {
      height: 50px !important;
    }
  }
}
.formInputItem {
  margin-bottom: 16px;
  @media only screen and (max-width: 768px) {
    :global {
      .ant-form-item-label {
        padding: 0 0 2px;
      }
      .ant-input-affix-wrapper {
        display: flex;
        height: 50px;
        padding: 8px 16px;
        align-items: center;
        border-radius: 10px;
        &::placeholder {
          color: var(--grey-field, #b1b1b1);
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
        input {
          font-size: 16px;
          height: 35px;
          @media screen and (max-width: 375px) {
            font-size: 14px;
          }
        }
      }
    }
  }
  input {
    display: flex;
    height: 50px;
    padding: 15px 16px 16px 16px;
    align-items: center;
    border-radius: 10px;
    font-size: 16px;
    @media screen and (max-width: 375px) {
      font-size: 14px;
    }
    &::placeholder {
      color: var(--grey-field, #b1b1b1);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
  select {
    display: flex;
    height: 50px;
    padding: 15px 16px 16px 16px;
    align-items: center;
    border-radius: 10px;
    &::placeholder {
      display: flex;
      align-items: center;
      color: var(--grey-field, #b1b1b1);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    :global {
      .ant-select-single .ant-select-selector .ant-select-selection-item,
      .ant-select-single
        .ant-select-selector
        .ant-select-selection-placeholder {
        line-height: 40px !important;
      }
    }
  }
}

.bankOption {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  padding: 4px 0;

  :global {
    .ant-select-item-option {
      .ant-image {
        min-width: 35px !important;
        height: 35px !important;
      }
    }
  }
}

// Optional: highlight on hover
.ant-select-item-option-active:not(.ant-select-item-option-disabled),
.ant-select-item-option-selected {
  background: #e6f7ff;
}

.bankListDrawer {
  :global {
    .ant-drawer-body {
      padding: 0 16px 16px;
    }
  }
}

.bankListContainer {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 8px 16px 16px;
  overflow-y: auto;
  height: 100%;
  @media screen and (max-width: 768px) {
    padding: 8px 0 16px;
  }
}

.bankItem {
  width: 100%;
  height: fit-content;
  min-height: 55px;
  padding: 8px 8px;
  border-radius: 12px;
  border: 1px solid #cbd2d9;
  background: #ffffff;
  text-align: left;
  transition: all 0.3s ease;

  &:hover {
    border-color: #11a2f3;
  }
}

.bankItemContent {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bankIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  // min-width: 40px;
  // height: 40px;

  img {
    object-fit: contain;
  }
}

.bankInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
  overflow: hidden;

  label {
    font-size: 16px;
    font-weight: 400;
    line-height: 19px;
    color: #24313d;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  p {
    font-size: 14px;
    font-weight: 400;
    line-height: 17px;
    color: #627792;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.selectBankItem {
  display: flex;
  align-items: center;
  gap: 8px;

  img {
    object-fit: contain;
  }

  span {
    font-size: 16px;
    color: #24313d;
  }
}

.bankSelectionDrawer {
  animation: right 0.5s ease forwards;
  :global {
    .ant-drawer-body {
      padding: 0;
    }
    .ant-input-affix-wrapper {
      padding: 14px 16px;
      border-radius: 12px;
      background-color: #f0f1f1 !important;
      .ant-input {
        background-color: #f0f1f1 !important;
      }
      .ant-input-prefix {
        background-color: #f0f1f1 !important;
      }
    }
  }
}

.bankSelectionModal {
  :global {
    .ant-modal-content {
      border-radius: 12px;
    }
    .ant-modal-body {
      padding: 0;
    }
  }
}
.drawerContent {
  padding: 0;
  animation: left 0.5s ease forwards;

  position: relative;
}
@keyframes expand {
  from {
    transform: translateY(0%);
    opacity: 0;
  }
  to {
    transform: translateY(100%);
    opacity: 1;
  }
}
@keyframes right {
  from {
    transform: translateX(100vw);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes leftIn {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100vw);
    opacity: 0;
  }
}
@keyframes left {
  from {
    transform: translateX(-100vw);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes leftPC {
  from {
    transform: translateX(-100px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.faceOut {
  animation: leftIn 0.5s ease forwards;
  position: absolute;
}
