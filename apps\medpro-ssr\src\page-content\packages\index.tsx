import {
  AlertModal,
  MPNewMedicalPackage,
  SEARCH_TAB,
  useWindowResize
} from '@medpro-libs/libs'
import { isArray, size } from 'lodash'
import React, { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { AppInfo } from '../../type'
import { useAppSelector } from '../../../store/hooks'
import { PartnerInfo } from '../../../store/hospital/interface'
import { selectAppInfo } from '../../../store/hospital/selector'
import { totalDataActions } from '../../../store/total/slice'
import client from '../../../config/medproSdk'
import { openNotification, showError } from '../../../utils/utils.notification'
import { bookingActions } from '../../../store/booking/slice'
import Radar from 'radar-sdk-js'
import { currentEnv } from '../../../config/envs'
import { locationActions } from '../../../store/location/locationSlice'
import { ExclamationCircleOutlined } from '@ant-design/icons'

export interface GoiKhamSucKhoePageProps {
  partnerId?: string
  token?: string
  appLoading?: boolean
  // Đây này là thông tin của partnerId chạy app. Ví dụ medpro.
  partnerInfoApp?: PartnerInfo
  isWebView?: boolean
  partnerInfo?: PartnerInfo
  hospitals: any[]
  packages: any[]
  type: string
  data: {
    total: any
    results: any[]
  }
  titlePage?: string
  service: any
  appInfo: AppInfo
  isHiddenFilter: boolean
}

const GoiKhamSucKhoePage = (props: GoiKhamSucKhoePageProps) => {
  const { service = {} } = props
  const isMobile = useWindowResize(576)
  const dispatch = useDispatch()
  const [messagePackage, setMessagePackage] = useState('')
  const appInfo = props.partnerInfoApp || useAppSelector(selectAppInfo)
  const locationData = useAppSelector((s) => s.location.data)
  const searchService = async () => {
    return await client.searchService.searchKeyWords()
  }

  const getPackages = async ({
    pageIndex,
    pageSize,
    tags,
    kw,
    partnerId,
    cityId
  }) => {
    return await client.searchService.getPackages(
      {
        offset: Number(pageIndex || 1),
        limit: pageSize,
        tags: tags.join(','),
        search_key: kw,
        city_id: isArray(cityId) ? cityId?.join(',') : cityId,
        latitude: locationData?.latitude,
        longitude: locationData?.longitude
      },
      { appid: '', partnerid: partnerId }
    )
  }

  const handleBooking = async (item: any) => {
    if (size(item?.serviceDescription?.slug) === 0) {
      setMessagePackage('Gói khám đang được cập nhật')
    } else {
      setMessagePackage(undefined)
      if (!isMobile) {
        dispatch(
          bookingActions.handleBookingCta({ type: SEARCH_TAB.PACKAGE, item })
        )
      } else {
        dispatch(bookingActions.handleBookingCta({ type: 'bookingApp', item }))
      }
    }
  }

  const handleDetectLocation = () => {
    Radar.initialize(currentEnv.RADA_KEY)
    Radar.getLocation()
      .then(async (result: any) => {
        const response = await Radar.reverseGeocode({
          latitude: result.latitude,
          longitude: result.longitude
        })
        dispatch(
          locationActions.setUserLocation({
            ...result,
            ...response?.addresses[0]
          })
        )
      })
      .catch((err) => {
        let errorMessage = 'Đã xảy ra lỗi không xác định'
        if (err?.message?.includes('permissions')) {
          errorMessage = 'Medpro không được phép sử dụng vị trí của bạn.'
        }
        openNotification(
          'info',
          {
            message: (
              <div>
                <ExclamationCircleOutlined /> <span>Thông báo</span>
              </div>
            ),
            description: errorMessage,
            placement: 'topLeft'
          },
          'primary'
        )
      })
  }

  return (
    <>
      <MPNewMedicalPackage
        data={props.data}
        service={service}
        searchService={searchService}
        getPackages={getPackages}
        handleBooking={handleBooking}
        titlePage={props.titlePage}
        isHiddenFilter={props.isHiddenFilter}
        appInfo={appInfo}
        showError={showError}
        handleDetectLocation={handleDetectLocation}
        locationData={locationData}
      />
      {messagePackage && (
        <AlertModal
          open={!!messagePackage}
          onOk={() => setMessagePackage('')}
          onCancel={() => setMessagePackage('')}
        >
          {messagePackage}
        </AlertModal>
      )}
    </>
  )
}

GoiKhamSucKhoePage.ssr = true
GoiKhamSucKhoePage.breadcrumb = [{ title: 'Gói khám' }]

export default GoiKhamSucKhoePage
