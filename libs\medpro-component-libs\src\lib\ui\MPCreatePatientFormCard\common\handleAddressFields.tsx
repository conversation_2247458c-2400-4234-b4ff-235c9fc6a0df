import { Form, Input, Select } from 'antd'
import { get, size } from 'lodash'
import { Valid } from '../../../common/helper/valid'
import styles from './../styles.module.less'
import { getReplaceUTF8 } from '../../../common/func'

const valid = new Valid()
const { Option } = Select

interface IHandleAddressFields {
  data: any
  isMobile: boolean
}

export const handleAddressFields = ({
  data,
  isMobile
}: IHandleAddressFields) => {
  const ignoreProperties = get(data, 'patient.propertyIgnoreUpdate', [])
  const locationFields = [
    {
      id: 'city_id',
      type: 'text',
      label: 'Tỉnh/Thành',
      placeholder: 'Chọn tỉnh thành',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ validator: valid.province }]}
            className={styles['selectItem']}
          >
            <Select
              size='large'
              disabled={disabled}
              showSearch
              placeholder={placeholder}
              filterOption={(input, option) =>
                getReplaceUTF8(option!.children as unknown as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            >
              {size(data?.province) > 0 &&
                data?.province?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: ''
    },
    {
      id: 'district_id',
      type: 'text',
      label: 'Quận/Huyện',
      placeholder: 'Chọn quận huyện',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ required: require, validator: valid.district }]}
            className={styles['selectItem']}
          >
            <Select
              size='large'
              disabled={disabled}
              showSearch
              placeholder={placeholder}
              filterOption={(input, option) =>
                getReplaceUTF8(option!.children as unknown as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            >
              {size(data?.district) > 0 &&
                data?.district?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: ''
    },
    {
      id: 'ward_id',
      type: 'text',
      label: 'Phường/Xã',
      placeholder: 'Chọn xã phường',
      require: true,
      enter: ({ id, require, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={handleRequireInput(label, require)}
            name={id}
            rules={[{ required: require, validator: valid.ward }]}
            className={styles['selectItem']}
            style={!isMobile ? { marginTop: 18 } : {}}
          >
            <Select
              size='large'
              disabled={disabled}
              showSearch
              placeholder={placeholder}
              filterOption={(input, option) =>
                getReplaceUTF8(option!.children as unknown as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
            >
              {size(data?.ward) > 0 &&
                data?.ward?.map((item: any, index: number) => (
                  <Option key={index} value={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
        )
      },
      hidden: false,
      width: ''
    },
    {
      id: 'address',
      type: 'text',
      label: (
        <div className={styles['boxAddress']}>
          <div>{handleRequireInput('Số nhà/Tên đường/Ấp thôn xóm', true)}</div>

          <p className={styles['sup']}>
            (không bao gồm tỉnh/thành, quận/huyện, phường/xã)
          </p>
        </div>
      ),
      placeholder: 'Nhập số nhà, tên đường, ấp thôn xóm,...',
      require: true,
      enter: ({ id, require, type, placeholder, label, disabled }: any) => {
        return (
          <Form.Item
            label={label}
            name={id}
            rules={[{ required: require, validator: valid.address }]}
            className={styles['formInputItem']}
          >
            <Input
              size='large'
              disabled={disabled}
              type={type}
              placeholder={placeholder}
            />
          </Form.Item>
        )
      },
      hidden: false,
      width: ''
    }
  ]
  return locationFields.map((field) => ({
    ...field,
    disabled: ignoreProperties.includes(field.id)
  }))
}
const handleRequireInput = (label: string, require: boolean) => {
  if (require) {
    return (
      <>
        {label} <sup className={styles['requireInput']}>*</sup>
      </>
    )
  }
  return <>{label}</>
}
