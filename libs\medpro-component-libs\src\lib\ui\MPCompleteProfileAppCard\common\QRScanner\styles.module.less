.cameraContainer {
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-top: 20px;

  .cameraWrapper {
    position: relative;

    .cameraFrame {
      position: relative;
      width: 100%;
      min-width: 300px;
      height: 300px;
      border: 2px dashed #ccc;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #3f3f3f;
      box-shadow: 0 1px 5px #3f3f3f;

      @media only screen and (max-width: 320px) {
        width: 100%;
        min-width: 250px;
        height: 250px;
      }
    }

    .actionGroup {
      margin-top: 15px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      justify-content: center;
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translate(-50%, -10%);
      z-index: 99;
      padding-left: 10px;
      padding-right: 10px;

      .btn {
        padding: 10px 15px;
        cursor: pointer;
        border-radius: 8px;
        height: 40px !important;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
        border: none;
      }

      .btnScanning {
        background-color: #007bff;
        color: white;

        &:hover {
          background-color: #0056b3;
        }
      }

      .btnImgScanning {
        background-color: #ffffff12;
        color: white;

        .icon {
          width: 18px;
          height: 18px;
        }

        &:hover {
          background-color: #FFFFFF4D;
        }

        &.btnImagePreviewUrl {
          background-color: #1010105c;

          &:hover {
            background-color: #101010a8;
          }
        }
      }
    }
  }
}

.modalMessageContainer {
  .text {
    font-size: 15px;
  }
}
