import React, { useState } from 'react'
import { Row, Col } from 'antd'
import styles from './styles.module.less'
import PatientInfo from '../../components/PatientInfo'
import QRPayment from '../../components/QRPayment'
import PaymentMethods from '../../components/PaymentMethods'

// Mock data cho demo
const mockPatientData = {
  name: '<PERSON><PERSON> Thị <PERSON>',
  phone: '**********',
  address: 'Hà Nội',
  service: 'Trẻ sơ sinh, ghế nôi',
  doctor: 'BS. Nguyễn Thị B',
  time: '14:30 - 15:00',
  date: '25/12/2024'
}

const mockPaymentData = {
  servicePrice: 122000,
  additionalServices: 0,
  total: 122000,
  transactionId: 'T250M1204CA'
}

export function ThanhToanPage() {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('')
  const [showPaymentMethods, setShowPaymentMethods] = useState(false)

  const handlePaymentMethodSelect = (method: string) => {
    setSelectedPaymentMethod(method)
  }

  const handleShowPaymentMethods = () => {
    setShowPaymentMethods(true)
  }

  return (
    <div className={styles.paymentPage}>
      <div className={styles.container}>
        <Row gutter={[24, 24]}>
          {/* Left Column - Patient Info */}
          <Col xs={24} md={8}>
            <PatientInfo patientData={mockPatientData} />
          </Col>

          {/* Right Column - Payment */}
          <Col xs={24} md={16}>
            {!showPaymentMethods ? (
              <div className={styles.paymentSelection}>
                <PaymentMethods
                  onMethodSelect={handlePaymentMethodSelect}
                  selectedMethod={selectedPaymentMethod}
                />
                {selectedPaymentMethod && (
                  <div className={styles.proceedButton}>
                    <button
                      className={styles.proceedBtn}
                      onClick={handleShowPaymentMethods}
                    >
                      Tiến hành thanh toán
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <QRPayment
                transactionId={mockPaymentData.transactionId}
                amount={mockPaymentData.total}
              />
            )}
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default ThanhToanPage
