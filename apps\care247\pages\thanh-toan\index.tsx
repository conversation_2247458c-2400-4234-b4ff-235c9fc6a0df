import React, { useState, useEffect, useMemo } from 'react'
import { useRouter } from 'next/router'
import { Row, Col, Spin, Alert, message } from 'antd'
import styles from './styles.module.less'
import LeftPanel from '../../components/LeftPanel'
import PaymentCard from '../../components/PaymentCard'
import usePayment from '../../hooks/usePayment'

// Fallback mock data
// const mockData = {
//   orderCode: 'T250M1204CA',
//   customerName: 'Vũ Thị <PERSON>',
//   amount: 122000,
//   service: 'Trẻ sơ sinh, ghế nôi',
//   bankAccount: '9646474000000891799',
//   bankName: 'MASTERBANK - Ngân hàng TMCP Việt Nam',
//   transferCode: '*****************'
// }

export function ThanhToanPage() {
  const router = useRouter()
  const { transactionId, partnerId } = router.query

  const [timeLeft, setTimeLeft] = useState(600) // 10 phút = 600 giây

  const {
    paymentInfo,
    loading,
    getPaymentInfo,
    resetState
  } = usePayment()

  // Timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Fetch payment info when component mounts
  useEffect(() => {
    if (transactionId && partnerId) {
      getPaymentInfo({
        transactionId: transactionId as string,
        partnerId: partnerId as string
      })
    } else {
      //show message
      message.error('Không tìm thấy thông tin thanh toán!')
    }
  }, [transactionId, partnerId, getPaymentInfo])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      resetState()
    }
  }, [resetState])

  // Use API data if available, otherwise fallback to mock data
  const paymentData = useMemo(() => {
    return paymentInfo
      ? {
          orderCode: paymentInfo.feeCode,
          customerName: paymentInfo.hospitalName,
          amount: paymentInfo.amount,
          service: `Phí khám bệnh - ${paymentInfo.hospitalName}`,
          bankAccount: '', 
          bankName: '',
          transferCode: paymentInfo.feeCode,
          subTotal: paymentInfo.subTotal,
          totalFee: paymentInfo.totalFee,
          medproFee: paymentInfo.medproFee,
          transferFee: paymentInfo.transferFee,
          partnerImage: paymentInfo.partnerImage
        }
      : {}
  }, [paymentInfo])

  // Loading state
  if (loading) {
    return (
      <div className={styles.paymentPage}>
        <div className={styles.loadingContainer}>
          <Spin size='large' />
          <p>Đang tải thông tin thanh toán...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.paymentPage}>
      {/* Main Content */}
      <div className={styles.mainContent}>
        <Row gutter={[56, 56]} align='stretch'>
          <Col xs={24} lg={12} xl={12}>
            <LeftPanel />
          </Col>
          <Col xs={24} lg={12} xl={12} flex={1}>
            {paymentData ? (
              <PaymentCard paymentData={paymentData} />
            ) : (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '400px'
                }}
              >
                <Spin size='large' />
              </div>
            )}
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default ThanhToanPage
