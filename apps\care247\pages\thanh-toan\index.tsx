import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { Row, Col, Spin, Alert } from 'antd'
import styles from './styles.module.less'
import LeftPanel from '../../components/LeftPanel'
import PaymentCard from '../../components/PaymentCard'
import usePayment from '../../hooks/usePayment'

// Fallback mock data
const mockData = {
  orderCode: 'T250M1204CA',
  customerName: 'Vũ Thị Kim Ngân',
  amount: 122000,
  service: 'Trẻ sơ sinh, ghế nôi',
  bankAccount: '9646474000000891799',
  bankName: 'MASTERBANK - Ngân hàng TMCP Việt Nam',
  transferCode: '*****************'
}

export function ThanhToanPage() {
  const router = useRouter()
  const { transactionId, partnerId } = router.query

  const [timeLeft, setTimeLeft] = useState(600) // 10 phút = 600 giây

  const {
    paymentInfo,
    loading,
    error,
    getPaymentInfo,
    clearError,
    resetState
  } = usePayment()

  // Timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Fetch payment info when component mounts
  useEffect(() => {
    if (transactionId && partnerId) {
      getPaymentInfo({
        transactionId: transactionId as string,
        partnerId: partnerId as string,
        onSuccess: (payment) => {
          console.log('Payment info loaded successfully:', payment)
        },
        onError: (errorMessage) => {
          console.error('Failed to load payment info:', errorMessage)
        }
      })
    }
  }, [transactionId, partnerId, getPaymentInfo])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      resetState()
    }
  }, [resetState])

  // Use API data if available, otherwise fallback to mock data
  const paymentData = paymentInfo ? {
    orderCode: paymentInfo.transactionId,
    customerName: paymentInfo.customerName,
    amount: paymentInfo.amount,
    service: paymentInfo.service,
    bankAccount: paymentInfo.bankAccount,
    bankName: paymentInfo.bankName,
    transferCode: paymentInfo.transferCode
  } : mockData

  // Loading state
  if (loading) {
    return (
      <div className={styles.paymentPage}>
        <div className={styles.loadingContainer}>
          <Spin size="large" />
          <p>Đang tải thông tin thanh toán...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.paymentPage}>
      {/* Error Alert */}
      {error && (
        <div className={styles.errorContainer}>
          <Alert
            message="Lỗi tải thông tin thanh toán"
            description={error}
            type="error"
            showIcon
            closable
            onClose={clearError}
          />
        </div>
      )}

      {/* Main Content */}
      <div className={styles.mainContent}>
        <Row gutter={[56, 56]} align='stretch' >
          <Col xs={24} lg={12} xl={12}>
            <LeftPanel />
          </Col>
          <Col xs={24} lg={12} xl={12} flex={1}>
            <PaymentCard paymentData={paymentData} />
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default ThanhToanPage
