import React, { useState, useEffect } from 'react'
import { Row, Col } from 'antd'
import styles from './styles.module.less'
import LeftPanel from '../../components/LeftPanel'
import PaymentCard from '../../components/PaymentCard'

// Mock data giống hệt UI
const mockData = {
  orderCode: 'T250M1204CA',
  customerName: 'Vũ Thị Kim Ngân',
  amount: 122000,
  service: 'Trẻ sơ sinh, ghế nôi',
  bankAccount: '9646474000000891799',
  bankName: 'MASTERBANK - Ngân hàng TMCP Việt Nam',
  transferCode: '*****************'
}

export function ThanhToanPage() {
  const [timeLeft, setTimeLeft] = useState(600) // 10 phút = 600 giây

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  return (
    <div className={styles.paymentPage}>
      {/* Main Content */}
      <div className={styles.mainContent}>
        <Row gutter={[56, 56]} align='stretch' >
          <Col xs={24} lg={12} xl={12}>
            <LeftPanel />
          </Col>
          <Col xs={24} lg={12} xl={12} flex={1}>
            <PaymentCard paymentData={mockData} />
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default ThanhToanPage
