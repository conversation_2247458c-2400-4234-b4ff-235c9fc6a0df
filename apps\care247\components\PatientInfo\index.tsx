import React from 'react'
import styles from './styles.module.less'

interface PatientData {
  name: string
  phone: string
  address: string
  service: string
  doctor: string
  time: string
  date: string
}

interface PatientInfoProps {
  patientData: PatientData
}

export function PatientInfo({ patientData }: PatientInfoProps) {
  return (
    <div className={styles.patientInfoContainer}>
      <div className={styles.logo}>
        <div className={styles.logoPlaceholder}>
          <span>Care247</span>
        </div>
        <h2>Bạn đồng hành cùng bạn trên mọi hành trình chăm sóc sức khỏe</h2>
      </div>
      
      <div className={styles.patientInfo}>
        <h3>Thông tin đặt khám</h3>
        <div className={styles.infoItem}>
          <span className={styles.label}>Họ tên:</span>
          <span className={styles.value}>{patientData.name}</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.label}>Số điện thoại:</span>
          <span className={styles.value}>{patientData.phone}</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.label}>Địa chỉ:</span>
          <span className={styles.value}>{patientData.address}</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.label}>Dịch vụ:</span>
          <span className={styles.value}>{patientData.service}</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.label}>Bác sĩ:</span>
          <span className={styles.value}>{patientData.doctor}</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.label}>Thời gian:</span>
          <span className={styles.value}>{patientData.time}</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.label}>Ngày khám:</span>
          <span className={styles.value}>{patientData.date}</span>
        </div>
      </div>

      <div className={styles.imageSection}>
        <div className={styles.imagePlaceholder}>
          <div className={styles.doctorIcon}>👨‍⚕️</div>
          <div className={styles.patientIcon}>👩</div>
        </div>
        <div className={styles.imageText}>
          <p>Đồng hành cùng bạn trên mọi hành trình chăm sóc sức khỏe</p>
          <p>Tra cứu thông tin sức khỏe và nhận tư vấn từ các chuyên gia hàng đầu</p>
        </div>
      </div>
    </div>
  )
}

export default PatientInfo
