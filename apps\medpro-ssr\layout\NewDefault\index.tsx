import React, {
  <PERSON>ps<PERSON>ith<PERSON>hildren,
  useCallback,
  useEffect,
  useMemo,
  useState
} from 'react'
import Radar from 'radar-sdk-js'
import styles from './styles.module.less'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MPNew<PERSON>ooter,
  MPNewHeader,
  NewsCategories,
  PageRoutesV2
} from '@medpro-libs/libs'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { useAppSelector } from '../../store/hooks'
import { BsFillTelephoneFill } from 'react-icons/bs'
import { locationActions } from '../../store/location/locationSlice'
import { selectAppInfo, selectExtraConfig } from '../../store/hospital/selector'
import { PartnerInfo } from '@medpro-libs/types'
import { UserInfo } from '../../store/user/interface'
import { useRouter } from 'next/router'
import { useDispatch } from 'react-redux'
import { login, logOut } from '../../store/user/userSlice'
import {
  openNotification,
  showError,
  showMessage
} from '../../utils/utils.notification'
import {
  LINK_ANDROID_APP,
  LINK_DANG_KY_BO_CO_THUONG,
  LINK_IOS_APP,
  LINK_THONG_BAO_BO_CO_THUONG
} from '../../utils/manageResource'
import actionStore from '../../store/actionStore'
import { getEventTopicId } from '../../utils/method'
import { totalDataActions } from '../../store/total/slice'
import Image from 'next/image'
import CSYT from './../../public/images/home/<USER>'
import DVYT from './../../public/images/home/<USER>'
import Contact from './../../public/images/home/<USER>'
import HuongDan from './../../public/images/home/<USER>'
import TinTuc from './../../public/images/home/<USER>'
import VeChungToi from './../../public/images/home/<USER>'
import {
  getProtocol,
  handleUpdateLinkFromPartner
} from '../../utils/utils.function'
import { downloadApp } from '../../src/components/pages/Home/staticData'
import {
  featureActions,
  fetchFeatureListByPartner
} from '../../store/feature/slice'
import { selectSortedFeature } from '../../store/feature/selector'
import AppLoader from '../../src/components/shapes/AppLoader'
import useRouteLoading from '../../hooks/useRouteLoading'
import SEOHead from '../../src/components/SEOHead'
import { debounce, find, last, size } from 'lodash'
import { handleSEOTitleSubPage, SEOPage } from '../../utils/SEOPage'
import client from '../../config/medproSdk'
import { LIST_TYPE_PARTNER, NewHospitalType } from '../../utils/utils.contants'
import { bookingActions } from '../../store/booking/slice'
import { Modal } from 'antd'
import { useWindowDimensions } from 'libs/medpro-component-libs/src/lib/hooks/useWindowDimesion'
import Home from './../../public/images/home/<USER>'
import HomeActive from './../../public/images/home/<USER>'
import Guide from './../../public/images/home/<USER>'
import GuideActive from './../../public/images/home/<USER>'
import Booking from './../../public/images/home/<USER>'
import BookingActive from './../../public/images/home/<USER>'
import Bell from './../../public/images/home/<USER>'
import BellActive from './../../public/images/home/<USER>'
import User from './../../public/images/home/<USER>'
import UserActive from './../../public/images/home/<USER>'
import cx from 'classnames'
import { PageRoutes } from '../../utils/PageRoutes'

export interface NewLayoutProps extends PropsWithChildren {
  pageTitle?: string
  breadcrumb?: any[]
  breadcrumbBgColor?: string
  token?: string
  partnerInfoApp: PartnerInfo
  userInfo: UserInfo
  footerMobileMenu: boolean
  isSEODetail?: boolean
  showBreadcrumb: boolean
  behavior?: string
  backgroundColor?: string
  CSYT_DYNAMIC?: boolean
}

let timeout
export const NewDefaultLayout = ({
  breadcrumbBgColor,
  showBreadcrumb = true,
  behavior,
  backgroundColor,
  CSYT_DYNAMIC,
  ...props
}: NewLayoutProps) => {
  const behaviorAnKhang = behavior === 'AnKhang'
  const router = useRouter()
  const dispatch = useDispatch()
  const { isLoading } = useRouteLoading()
  const { windowWidth } = useWindowDimensions()
  const isMobile = windowWidth < 576
  const userToken = useAppSelector((s) => s.user.accessToken)
  const userInfo = useAppSelector((s) => s.user.userInfo)
  const loadingUserInfo = useAppSelector((s) => s.user.loading)
  const notiOfUser = useAppSelector((state) => state.total.notiOfUser)
  const unreadCount = useAppSelector((state) => state.total.unreadNotiCount)
  const countBooking = useAppSelector((s) => s.patient?.countBooking)
  const breadcrumbData = useAppSelector((s) => s.total.breadcrumb)
  const locationData = useAppSelector((s) => s.location.data)
  const cashBack = useAppSelector((s) => s.total.cashBack)
  const appInfo = useAppSelector(selectAppInfo)
  const features = useAppSelector(selectSortedFeature)
  const extraConfig = useAppSelector(selectExtraConfig)
  const partnerInfoApp = props.partnerInfoApp || appInfo
  const [menuHeader, setMenuHeader] = useState<any[]>(dataMenu)
  const [menuFooter, setMenuFooter] = useState<any>(dataFooter)
  const [keySearch, setKeySearch] = useState<any>('')
  const [searching, setSearching] = useState<any>(false)
  const [searchData, setSearchData] = useState<any>({})
  const hiddenFooter = router.query?.inNotiApp === 'true'
  const [warningBooking, setWarningBooking] = useState({
    status: false,
    message: ''
  })
  const featureSelected = features.find(
    (item) => item.slug === router.query?.app_feature_slug
  )
  useEffect(() => {
    if (size(features) < 1) {
      dispatch(
        fetchFeatureListByPartner({
          version: '1',
          locale: 'vn',
          appid: 'medpro'
        })
      )
    }
  }, [])

  useEffect(() => {
    if (userToken) {
      if (size(features) < 1) {
        dispatch(
          fetchFeatureListByPartner({
            version: '1',
            locale: 'vn',
            appid: appInfo?.partnerId
          })
        )
      }
      if (isMobile) {
        dispatch(totalDataActions.getUnReadNotif())
      } else {
        dispatch(totalDataActions.getNotiOfUser())
        dispatch(totalDataActions.getUnReadNotif())
      }
    }
  }, [userToken])

  useEffect(() => {
    if (features?.length > 0) {
      const filteredFeature = features.filter((f) => f.status && !f.disabled)

      const childFeatureLink = filteredFeature.map((f, index) => {
        return {
          key: `${index + 1}`,
          link:
            PageRoutesV2.hospitalList({ appFeatureSlug: f.slug }).pathname +
            (behaviorAnKhang ? '?behavior=AnKhang' : ''),
          label: f.name,
          sorOrder: index + 1,
          customUrl: f?.customUrl
        }
      })
      const newMenu = menuHeader.map((menu, index) => {
        const behaviorLink = `${menu.link}${
          behaviorAnKhang ? '?behavior=AnKhang' : ''
        }`
        if (index === 1) {
          return {
            ...menu,
            link: behaviorLink,
            children: [...childFeatureLink]
          }
        }
        return {
          ...menu,
          link: behaviorLink,
          children: (menu?.children || []).map((child) => {
            return {
              ...child,
              link: `${child.link}${behaviorAnKhang ? '?behavior=AnKhang' : ''}`
            }
          })
        }
      })
      console.log('newMenu', newMenu)
      setMenuHeader(newMenu)

      const _menuFooter = menuFooter.menu.map((menu, index) => {
        if (index === 0) {
          return {
            ...menu,
            children: childFeatureLink
          }
        }
        return {
          ...menu,
          children: (menu?.children || []).map((child) => {
            return {
              ...child,
              link: `${child.link}${behaviorAnKhang ? '?behavior=AnKhang' : ''}`
            }
          })
        }
      })
      const _menuFooterMobile = menuFooter.menuMobile.map((menu) => {
        return {
          ...menu,
          link: `${menu.link}${behaviorAnKhang ? '?behavior=AnKhang' : ''}`,
          children: (menu?.children || []).map((child) => {
            return {
              ...child,
              link: `${child.link}${behaviorAnKhang ? '?behavior=AnKhang' : ''}`
            }
          })
        }
      })
      setMenuFooter({
        ...menuFooter,
        menu: _menuFooter,
        menuMobile: _menuFooterMobile
      })
    }
  }, [])

  const handleLogin = () => {
    dispatch(login())
  }

  const handleLogout = () => {
    dispatch(logOut())
    router.push('/')
    showMessage('Bạn đã đăng xuất thành công', 'info')
  }

  const onClickNotificationItem = (item: any) => {
    if (item.id) {
      dispatch(actionStore.setMarkViewedNoti({ id: item.id }))
      //getNotiOfUser gọi lại data cho chuông ở header
      !isMobile && dispatch(actionStore.getNotiOfUser())
      const eventFormTopicId = getEventTopicId(item)
      if (item?.eventData?.type === 100 && item?.eventData?.url) {
        window.open(getProtocol(item?.eventData?.url), '_blank')
      } else {
        router.push(eventFormTopicId)
      }
    }
  }

  const downloadAppUponPartnerInfo = handleUpdateLinkFromPartner(
    downloadApp,
    partnerInfoApp
  )

  const onSearchDebounce = useCallback(
    async (event: any) => {
      clearTimeout(timeout)
      const kw = event.target.value
      setKeySearch(kw)
      setSearching(true)
      timeout = setTimeout(() => {
        void onSearch(kw)
      }, 500)
    },
    [router.query]
  )

  const onSearch = useCallback(
    debounce(async (kw) => {
      try {
        const { data } = await client.searchService.search(
          {
            search_key: kw,
            category: 'all',
            offset: 0,
            limit: 10,
            latitude: locationData?.latitude,
            longitude: locationData?.longitude
          },
          { appid: '', partnerid: '' }
        )
        const newData = data.reduce((c, i) => {
          c[i.category] = i.results
          return c
        }, {})
        setSearchData(newData)
      } catch (err) {
        showError(err)
      } finally {
        clearTimeout(timeout)
        setTimeout(() => setSearching(false), 250)
      }
    }),
    [setSearchData, setSearching]
  )

  const handleDetectLocation = () => {
    Radar.getLocation()
      .then((result: any) => {
        dispatch(locationActions.setUserLocation(result))
      })
      .catch((err) => {
        let errorMessage = 'Đã xảy ra lỗi không xác định'
        if (err?.message?.includes('permissions')) {
          errorMessage = 'Medpro không được phép sử dụng vị trí của bạn.'
        }
        openNotification(
          'info',
          {
            message: (
              <div>
                <ExclamationCircleOutlined /> <span>Thông báo</span>
              </div>
            ),
            description: errorMessage,
            placement: 'topLeft'
          },
          'primary'
        )
      })
  }

  const handleBookingSearch = async ({ type, item }: any) => {
    if (isMobile) {
      dispatch(bookingActions.handleBookingCta({ type: 'bookingApp', item }))
    } else if (item.category === 'doctor' && item?.description?.disabled) {
      setWarningBooking((preState) => ({
        ...preState,
        status: true,
        message: item.description.message
      }))
    } else {
      dispatch(
        bookingActions.handleBookingCta({
          type,
          item,
          options: { behavior: router.query?.behavior }
        })
      )
    }
  }

  const bgTitle = [
    'Cơ sở y tế',
    'Tạo hồ sơ bệnh nhân',
    'Thông tin thanh toán viện phí',
    'Gói khám',
    'Hồ sơ bệnh nhân',
    'Phiếu khám bệnh',
    'Thông báo',
    'Danh sách thông báo',
    'Danh sách phiếu khám bệnh',
    'Danh sách hồ sơ bệnh nhân',
    ...LIST_TYPE_PARTNER.map((item) => item.title)
  ]

  const isHomePage = router.pathname === '/'
  const finalBreadcrumb = props.breadcrumb ? props.breadcrumb : breadcrumbData

  const meta = useMemo(() => {
    return find(SEOPage, {
      key: router.asPath.replace('/', '')
    })
  }, [router.asPath])
  const newsMobile = useMemo(() => {
    return (
      ([
        'tin-tuc',
        'chon-lich-kham',
        'chi-tiet-phieu-kham-benh',
        'thanh-toan-ho',
        'thanh-toan-lai-app'
      ].includes(router.pathname.split('/')[1]) ||
        router.pathname === '/[partner_slug]/goi-kham/[packageSlug]') &&
      isMobile
    )
  }, [isMobile, router.pathname])
  return (
    <main>
      <AppLoader customSpinning={isLoading} delay={500} />
      <section className={styles.defaultLayout}>
        <MPNewHeader
          noti={{
            list: notiOfUser || [],
            unreadCount: unreadCount,
            getNotiOfUser: () => {
              dispatch(totalDataActions.getNotiOfUser())
            }
          }}
          data={menuHeader}
          user={{}}
          handleLogin={handleLogin}
          handleLogout={handleLogout}
          onSearchDebounce={onSearchDebounce}
          handleBookingSearch={handleBookingSearch}
          searching={searching}
          searchData={searchData}
          locationData={locationData}
          locationType={'header'}
          author={{
            loading: loadingUserInfo,
            data: props.userInfo || userInfo
          }}
          userToken={userToken}
          logo={partnerInfoApp?.logo}
          logoWhite={partnerInfoApp?.logoWhite}
          partnerId={'medpro'}
          onClickNotificationItem={onClickNotificationItem}
          handleDetectLocation={handleDetectLocation}
          downloadApp={downloadAppUponPartnerInfo}
          appInfo={appInfo}
          featureSelected={featureSelected}
          cashBack={isHomePage ? cashBack : ''}
          behavior={behavior}
          countBooking={countBooking}
          CSYT_DYNAMIC={CSYT_DYNAMIC}
        />

        <div
          style={{ backgroundColor: backgroundColor }}
          className={cx(
            styles.body,
            cashBack?.status && isHomePage && styles['isCashBack'],
            behaviorAnKhang && styles['behavior']
          )}
        >
          {showBreadcrumb && !newsMobile && (
            <MPBreadcrumbNew
              background={
                bgTitle.includes(finalBreadcrumb?.[0]?.title)
                  ? 'white'
                  : breadcrumbBgColor
              }
              data={finalBreadcrumb}
              CSYT_DYNAMIC={CSYT_DYNAMIC}
            />
          )}
          {props.children}
        </div>
        <MPNewFooter
          isBottomTab={props.footerMobileMenu}
          data={{ ...menuFooter, appInfo }}
          bottomTabMenu={BottomTabMenu}
          unReadNoti={unreadCount}
          downloadApp={downloadAppUponPartnerInfo}
          hiddenFooter={hiddenFooter}
          countBooking={countBooking}
        />
        {![
          'chon-lich-kham',
          'tao-moi-ho-so',
          'cap-nhat-thong-tin',
          'tim-ho-so-app',
          'xac-nhan-thong-tin-benh-nhan-app',
          'tuyen-dung'
        ].includes(router.pathname.split('/')[1]) &&
          !behaviorAnKhang && (
            <a href={'tel:19002115'}>
              <div className={styles['support']}>
                <BsFillTelephoneFill
                  size={22}
                  color='#00b5f1'
                  aria-labelledby='Phone CSKH'
                />
              </div>
            </a>
          )}

        {warningBooking.status && (
          <Modal
            title={'Thông báo'}
            open={warningBooking.status}
            footer={null}
            centered
            onCancel={() =>
              setWarningBooking((preState) => ({
                ...preState,
                status: false,
                message: ''
              }))
            }
            className={styles['modal']}
          >
            <div
              className={styles['description']}
              dangerouslySetInnerHTML={{
                __html: warningBooking.message
              }}
            />
          </Modal>
        )}
      </section>
    </main>
  )
}

const menuCoSoYte = [
  {
    key: '1',
    label: 'Bệnh viện công',
    link: `/co-so-y-te/${NewHospitalType.PUBLIC_HOSPITAL}`,
    sortOrder: 1
  },
  {
    key: '2',
    label: 'Bệnh viện tư',
    link: `/co-so-y-te/${NewHospitalType.PRIVATE_HOSPITAL}`,
    sortOrder: 2
  },
  {
    key: '3',
    label: 'Phòng khám',
    link: `/co-so-y-te/${NewHospitalType.PHONG_KHAM}`,
    sortOrder: 3
  },
  {
    key: '4',
    label: 'Phòng mạch',
    link: `/co-so-y-te/${NewHospitalType.PHONG_MACH}`,
    sortOrder: 4
  },
  {
    key: '5',
    label: 'Xét nghiệm',
    link: `/co-so-y-te/${NewHospitalType.XET_NGHIEM}`,
    sortOrder: 5
  },
  {
    key: '6',
    label: 'Y tế tại nhà',
    link: `/co-so-y-te/${NewHospitalType.Y_TE_TAI_NHA}`,
    sortOrder: 6
  },
  {
    key: '7',
    label: 'Tiêm chủng',
    link: `/co-so-y-te/${NewHospitalType.TIEM_CHUNG}`,
    sortOrder: 6
  }
]

const BottomTabMenu = [
  {
    key: '',
    label: 'Trang chủ',
    link: '/',
    sortOrder: 1,
    icon: Home,
    iconActive: HomeActive,
    width: 25,
    height: 24,
    status: true
  },
  {
    key: 'huong-dan',
    label: 'Hướng dẫn',
    link: '/huong-dan/cai-dat-ung-dung',
    icon: Guide,
    iconActive: GuideActive,
    width: 24,
    height: 24,
    sortOrder: 2,
    status: true
  },
  {
    key: 'phieu-kham',
    label: 'Phiếu khám',
    link: '/user?key=bills',
    icon: Booking,
    iconActive: BookingActive,
    width: 24,
    height: 24,
    sortOrder: 3,
    status: true
  },
  {
    key: 'thong-bao',
    label: 'Thông báo',
    link: '/user?key=notifications',
    icon: Bell,
    iconActive: BellActive,
    width: 25,
    height: 26,
    sortOrder: 4,
    status: true
  },
  {
    key: 'ho-so',
    label: 'Hồ sơ',
    link: '/user?key=records',
    icon: User,
    iconActive: UserActive,
    width: 25,
    height: 24,
    sortOrder: 5,
    status: true
  }
]

export const dataMenu = [
  {
    key: 'co-so-y-te',
    label: 'Cơ sở y tế',
    children: menuCoSoYte,
    link: '/co-so-y-te',
    sortOrder: 1,
    icon: <Image src={CSYT} alt='Icon CSYT' layout='fixed' />,
    status: true
  },
  {
    key: 'dich-vu',
    label: 'Dịch vụ y tế',
    children: [],
    link: '/dich-vu-y-te',
    sortOrder: 2,
    icon: <Image src={DVYT} alt='Icon DVYT' layout='fixed' />,
    status: true
  },
  {
    key: 'goi-kham-doanh-nghiep',
    label: 'Khám sức khỏe doanh nghiệp',
    link: '/kham-suc-khoe-doanh-nghiep',
    sortOrder: 3,
    icon: <Image src={DVYT} alt='Icon GKSKDN' layout='fixed' />,
    status: true
  },
  {
    key: 'tin-tuc',
    label: 'Tin tức',
    children: [
      {
        key: '1',
        label: 'Tin dịch vụ',
        link: `/tin-tuc/${NewsCategories.TIN_DICH_VU}`,
        sortOrder: 1
      },
      {
        key: '2',
        label: 'Tin y tế',
        link: `/tin-tuc/${NewsCategories.TIN_Y_TE}`,
        sortOrder: 2
      },
      {
        key: '3',
        label: 'Y học thường thức',
        link: `/tin-tuc/${NewsCategories.Y_HOC_THUONG_THUC}`,
        sortOrder: 3
      }
    ],
    link: '/tin-tuc',
    sortOrder: 4,
    icon: <Image src={TinTuc} alt='Icon tin tức' layout='fixed' />,
    status: true
  },
  {
    key: 'huong-dan',
    label: 'Hướng dẫn',
    link: '/huong-dan/cai-dat-ung-dung',
    children: [
      {
        key: '1',
        label: 'Cài đặt ứng dụng',
        link: '/huong-dan/cai-dat-ung-dung',
        sortOrder: 1
      },
      {
        key: '2',
        label: 'Đặt lịch khám',
        link: '/huong-dan/dat-lich-kham',
        sortOrder: 2
      },
      {
        key: '3',
        label: 'Tư vấn khám bệnh qua video',
        link: '/huong-dan/tu-van-kham-benh-qua-video',
        sortOrder: 5
      },
      {
        key: '4',
        label: 'Quy trình hoàn phí',
        link: '/huong-dan/quy-trinh-hoan-phi',
        sortOrder: 3
      },
      {
        key: '5',
        label: 'Câu hỏi thường gặp',
        link: '/huong-dan/cau-hoi-thuong-gap',
        sortOrder: 4
      },
      {
        key: '6',
        label: 'Quy trình đi khám',
        link: '/huong-dan/quy-trinh-di-kham',
        sortOrder: 5
      }
    ],
    sortOrder: 5,
    icon: <Image src={HuongDan} alt='Icon hướng dẫn' layout='fixed' />,
    status: true
  },

  {
    key: 'hop-tac',
    label: 'Liên hệ hợp tác',
    children: [
      {
        key: '1',
        label: 'Cơ sở y tế',
        link: PageRoutes.pricing.path,
        sortOrder: 1
      },
      // {
      //   key: '2',
      //   label: 'Doanh nghiệp',
      //   link: `/kham-suc-khoe-doanh-nghiep`,
      //   sortOrder: 2
      // },
      {
        key: '3',
        label: 'Quảng cáo',
        link: `/hop-tac-quang-cao`,
        sortOrder: 3
      },
      {
        key: '4',
        label: 'Tuyển Dụng',
        link: `/tuyen-dung`,
        sortOrder: 4
      },
      {
        key: '5',
        label: 'Về Medpro',
        link: '/gioi-thieu',
        sortOrder: 5
      }
    ],
    link: '/hop-tac-cung-medpro',
    sortOrder: 6,
    icon: <Image src={Contact} alt='Icon hợp tác' layout='fixed' />,
    status: true
  }
]

export const dataFooter = {
  menuMobile: [
    {
      title: 'Về Medpro',
      link: '/gioi-thieu',
      children: []
    },
    {
      title: 'Hướng dẫn',
      link: '#',
      children: [
        {
          label: 'Cài đặt ứng dụng',
          link: '/huong-dan/cai-dat-ung-dung'
        },
        {
          label: 'Đặt lịch khám',
          link: '/huong-dan/dat-lich-kham'
        },
        {
          label: 'Tư vấn khám bệnh qua video',
          link: '/huong-dan/tu-van-kham-benh-qua-video'
        },
        {
          label: 'Quy trình hoàn phí',
          link: '/huong-dan/quy-trinh-hoan-phi'
        },
        {
          label: 'Câu hỏi thường gặp',
          link: '/huong-dan/cau-hoi-thuong-gap'
        },
        {
          label: 'Quy trình đi khám',
          link: '/huong-dan/quy-trinh-di-kham'
        }
      ]
    },
    {
      title: 'Liên hệ hợp tác',
      link: '#',
      children: [
        {
          label: 'Tham gia Medpro',
          link: `/hop-tac-cung-medpro`
        },
        {
          label: 'Doanh nghiệp',
          link: `/kham-suc-khoe-doanh-nghiep`
        },
        {
          label: 'Quảng cáo',
          link: `/hop-tac-quang-cao`
        },
        {
          label: 'Tuyển Dụng',
          link: `/tuyen-dung`
        }
      ]
    },

    {
      title: 'Điều khoản dịch vụ',
      link: '/dieu-khoan-dich-vu'
    },
    {
      title: 'Chính sách bảo mật',
      link: '/chinh-sach-bao-mat'
    },
    {
      title: 'Quy định sử dụng',
      link: '/quy-dinh-su-dung'
    }
  ],
  menu: [
    {
      title: 'Dịch vụ Y tế',
      link: '',
      children: [
        {
          label: 'Đặt khám tại cơ sở',
          link: ''
        },
        {
          label: 'Đặt khám bác sĩ',
          link: ''
        },
        {
          label: 'Gói khám sức khỏe',
          link: ''
        },
        {
          label: 'Điều dưỡng tại nhà',
          link: ''
        },
        {
          label: 'Xét nghiệm tại nhà',
          link: ''
        }
      ]
    },
    {
      title: 'Cơ sở y tế',
      link: '',
      children: menuCoSoYte
    },
    {
      title: 'Hướng dẫn',
      link: '',
      children: [
        {
          label: 'Cài đặt ứng dụng',
          link: '/huong-dan/cai-dat-ung-dung'
        },
        {
          label: 'Đặt lịch khám',
          link: '/huong-dan/dat-lich-kham'
        },
        {
          label: 'Tư vấn khám bệnh qua video',
          link: '/huong-dan/tu-van-kham-benh-qua-video'
        },
        {
          label: 'Quy trình hoàn phí',
          link: '/huong-dan/quy-trinh-hoan-phi'
        },
        {
          label: 'Câu hỏi thường gặp',
          link: '/huong-dan/cau-hoi-thuong-gap'
        },
        {
          label: 'Quy trình đi khám',
          link: '/huong-dan/quy-trinh-di-kham'
        }
      ]
    },
    {
      title: 'Liên hệ hợp tác',
      link: '',
      children: [
        {
          label: 'Tham gia Medpro',
          link: `/hop-tac-cung-medpro`
        },
        {
          label: 'Khám sức khỏe doanh nghiệp',
          link: `/kham-suc-khoe-doanh-nghiep`
        },
        {
          label: 'Quảng cáo',
          link: `/hop-tac-quang-cao`
        },
        {
          label: 'Tuyển Dụng',
          link: `/tuyen-dung`
        }
      ]
    },
    {
      title: 'Tin tức',
      link: '',
      children: [
        {
          label: 'Tin dịch vụ',
          link: `/tin-tuc/${NewsCategories.TIN_DICH_VU}`
        },
        {
          label: 'Tin Y Tế',
          link: `/tin-tuc/${NewsCategories.TIN_Y_TE}`
        },
        {
          label: 'Y Học thường thức',
          link: `/tin-tuc/${NewsCategories.Y_HOC_THUONG_THUC}`
        }
      ]
    },
    {
      title: 'Về Medpro',
      link: '',
      children: [
        {
          label: 'Giới thiệu',
          link: '/gioi-thieu'
        },
        {
          label: 'Điều khoản dịch vụ',
          link: '/dieu-khoan-dich-vu'
        },
        {
          label: 'Chính sách bảo mật',
          link: '/chinh-sach-bao-mat'
        },
        {
          label: 'Quy định sử dụng',
          link: '/quy-dinh-su-dung'
        }
      ]
    }
  ],
  downloads: {
    dkbct: LINK_DANG_KY_BO_CO_THUONG,
    tbbct: LINK_THONG_BAO_BO_CO_THUONG,
    android: LINK_ANDROID_APP,
    ios: LINK_IOS_APP
  }
}
